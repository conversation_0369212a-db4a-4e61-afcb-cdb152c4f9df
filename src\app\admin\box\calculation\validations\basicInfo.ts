import { z } from 'zod';

// 盒子基础信息校验规则
export const boxBasicInfoSchema = z.object({
  name: z.string().min(1, '盒子名称不能为空').max(100, '盒子名称不能超过100个字符'),
  quantity: z.number().min(1, '数量必须大于0').max(1000000, '数量不能超过100万'),
  length: z.number().min(1, '长度必须大于0').max(10000, '长度不能超过10米'),
  width: z.number().min(1, '宽度必须大于0').max(10000, '宽度不能超过10米'),
  height: z.number().min(1, '高度必须大于0').max(10000, '高度不能超过10米'),
  foldingEdge: z.number().min(0).max(1000).optional(),
  thickness: z.number().min(0).max(100).optional(),
  description: z.string().max(500, '描述不能超过500个字符').optional(),
});

// 盒子属性校验规则
export const boxAttributeSchema = z.object({
  id: z.number().optional(),
  name: z.string().min(1, '属性名称不能为空'),
  code: z.string().min(1, '属性代码不能为空'),
  value: z.number().nullable().optional(),
});

// 完整的基础信息校验
export const basicInfoFormSchema = z.object({
  basicInfo: boxBasicInfoSchema,
  attributes: z.array(boxAttributeSchema).optional(),
});

export type BoxBasicInfoForm = z.infer<typeof boxBasicInfoSchema>;
export type BoxAttributeForm = z.infer<typeof boxAttributeSchema>;
export type BasicInfoForm = z.infer<typeof basicInfoFormSchema>; 