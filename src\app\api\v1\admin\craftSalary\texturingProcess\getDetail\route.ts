import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';
import { z } from 'zod';

const getDetailSchema = z.object({
  id: z.number().positive('ID必须是正整数'),
});

const handler = withValidation(
  getDetailSchema,
  async (request: AuthenticatedRequest, validatedData: any) => {
    const { id } = validatedData;

    // 查询压纹工艺详情
    const texturingProcess = await prisma.texturingProcess.findFirst({
      where: {
        id,
        isDel: false,
      },
    });

    assert(!!texturingProcess, ErrorCode.NOT_FOUND, '压纹工艺不存在');

    return successResponse(
      texturingProcess,
      '获取压纹工艺详情成功'
    );
  }
); 
export const POST = withInternalAuth(handler);