'use client';

import React, { useState } from 'react';
import { Form, Input, Button, Card, Typography, Space, InputNumber, message, Breadcrumb, Row, Col, Empty, Alert, Tooltip, Tag, Select, Spin } from 'antd';
import { ArrowLeftOutlined, PlusOutlined, DeleteOutlined, SaveOutlined, FunctionOutlined, SettingOutlined, FormOutlined, QuestionCircleOutlined, InfoCircleOutlined, ReloadOutlined, MinusCircleOutlined } from '@ant-design/icons';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { customFormulaApi } from '@/services/adminApi';
import { CustomFormulaAttribute } from '@/types/customFormula';
import { createCustomFormulaSchema } from '@/lib/validations/admin/customFormula';
import { FormulaStatus } from '@/types/customFormula';
import { handleSuccess, handleError } from '@/lib/utils/handler';
import { useErrorHandler, useAsyncError } from '@/lib/hooks/useErrorHandler';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

// 属性显示组件
const AttributesList = ({ attributes }: { attributes: CustomFormulaAttribute[] }) => {
  if (!attributes || attributes.length === 0) {
    return (
      <Alert
        message="尚未添加参数"
        description="请先在上方「计算参数」区域添加参数，添加后可在公式中引用"
        type="info"
        showIcon
      />
    );
  }

  return (
    <div style={{ marginBottom: 16 }}>
      <Text strong>可用参数：</Text>
      <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8, marginTop: 8 }}>
        {attributes.map((attr, index) => (
          <Tag key={index} color="blue">
            {attr.name}{attr.value !== undefined && attr.value !== null ? `（默认值: ${attr.value}）` : ''}
          </Tag>
        ))}
      </div>
    </div>
  );
};

export default function CreateCustomFormulaPage() {
  const router = useRouter();
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);
  // 添加属性列表状态，用于实时更新属性显示
  const [attributesList, setAttributesList] = useState<CustomFormulaAttribute[]>([]);
  const { errorState, clearError } = useErrorHandler();
  const { loading: asyncLoading, execute } = useAsyncError();

  // 表单值变化处理函数
  const handleFormValuesChange = (changedValues: any, allValues: any) => {
    // 如果属性值发生变化
    if (changedValues.attributes) {
      const attributes = allValues.attributes || [];
      const formatted = attributes.map((attr: any) => ({
        name: attr?.name || '',
        value: attr?.value !== undefined ? attr.value : null
      })).filter((attr: any) => attr.name);

      setAttributesList(formatted);
    }
  };

  // 处理提交
  const handleSubmit = async (values: any) => {
    try {
      // 检查属性数据
      const formAttributes = values.attributes || [];
      const validAttributes = formAttributes.filter((attr: any) => attr && attr.name && attr.name.trim());
      
      if (validAttributes.length === 0) {
        throw new Error('请至少添加一个有效的计算参数');
      }

      setSubmitting(true);
      
      const formData = {
        name: values.name,
        initialAmount: values.initialAmount || 0,
        expression: values.expression || '',
        status: parseInt(values.status),
        attributes: validAttributes,
      };

      // 使用 Zod 校验
      const result = createCustomFormulaSchema.safeParse(formData);
      
      if (!result.success) {
        handleError(result.error);
        return;
      }
      
      const apiResult = await execute(
        () => customFormulaApi.create(result.data),
        '创建自定义公式'
      );
      
      if (apiResult) {
        router.push('/admin/customFormula');
      }
    } catch (error) {
       (error);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div style={{ margin: '0 auto' }}>
      <div style={{ marginBottom: 24 }}>
        <Breadcrumb 
          items={[
            { title: <Link href="/admin/customFormula">自定义公式管理</Link> },
            { title: '新建计算公式' },
          ]}
        />
        
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: 16 }}>
          <Title level={2} style={{ margin: 0 }}>
            <Space>
              <FunctionOutlined />新建计算公式
            </Space>
          </Title>
          <Link href="/admin/customFormula">
            <Button icon={<ArrowLeftOutlined />}>返回列表</Button>
          </Link>
        </div>
      </div>

      {/* 错误状态显示 */}
      {errorState.hasError && (
        <Alert
          message="操作失败"
          description={errorState.error?.message}
          type="error"
          showIcon
          closable
          onClose={clearError}
          action={
            <Button size="small" icon={<ReloadOutlined />} onClick={() => clearError()}>
              重试
            </Button>
          }
          style={{ marginBottom: 16 }}
        />
      )}
      
      <Form 
        form={form}
        onFinish={handleSubmit}
        layout="vertical"
        autoComplete="off"
        onValuesChange={handleFormValuesChange}
        initialValues={{
          status: FormulaStatus.ENABLED,
          initialAmount: 0,
        }}
      >
        {/* 基本信息区域 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={24}>
            <Card 
              title={
                <Space>
                  <SettingOutlined />
                  <span>基本信息</span>
                </Space>
              }
            >
              <Row gutter={16}>
                <Col span={6}>
                  <Form.Item
                    name="name"
                    label="公式名称"
                    rules={[{ required: true, message: '请输入公式名称' }]}
                  >
                    <Input placeholder="请输入公式名称" />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    name="initialAmount"
                    label="起步金额"
                    rules={[{ required: true, message: '请输入起步金额' }]}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      min={0}
                      max={1000000}
                      step={0.01}
                      precision={2}
                      placeholder="请输入起步金额"
                    />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    name="status"
                    label="状态"
                    initialValue={FormulaStatus.ENABLED}
                    rules={[{ required: true, message: '请选择状态' }]}
                  >
                    <Select>
                      <Option value={FormulaStatus.ENABLED}>启用</Option>
                      <Option value={FormulaStatus.DISABLED}>禁用</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>

        {/* 计算属性区域 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={24}>
            <Form.List name="attributes">
              {(fields, { add, remove }) => (
                <Card 
                  title={
                    <Space>
                      <SettingOutlined />
                      <span>公式属性</span>
                      <Tooltip title="添加用于计算的参数，例如：长、宽、高、糊口、头部、底部">
                        <QuestionCircleOutlined style={{ color: '#999' }} />
                      </Tooltip>
                      <Text type="secondary" style={{ fontSize: 14 }}>
                        这些属性可在公式中引用
                      </Text>
                    </Space>
                  }
                  extra={
                    <Button
                      type="primary"
                      ghost
                      icon={<PlusOutlined />}
                      onClick={() => add()}
                      disabled={fields.length >= 8}
                    >
                      添加属性
                    </Button>
                  }
                >
                  {/* 属性列表 */}
                  {fields.length === 0 ? (
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description="暂无属性，请添加属性"
                    >
                    </Empty>
                  ) : (
                    <Row gutter={[16, 16]}>
                      {fields.map(({ key, name, ...restField }) => (
                        <Col key={key} span={6}>
                          <Card
                            size="small"
                            title={`属性 #${name + 1}`}
                            extra={
                              <Button
                                type="text"
                                danger
                                icon={<DeleteOutlined />}
                                onClick={() => remove(name)}
                              >
                                删除
                              </Button>
                            }
                          >
                            <Form.Item
                              {...restField}
                              name={[name, 'name']}
                              style={{ marginBottom: 12 }}
                              rules={[{ required: true, message: '请输入属性名称' }]}
                            >
                              <Input placeholder="属性名称" />
                            </Form.Item>
                            <Form.Item
                              {...restField}
                              name={[name, 'value']}
                              style={{ marginBottom: 0 }}
                            >
                              <Input placeholder="默认值（可选）" type="number" />
                            </Form.Item>
                          </Card>
                        </Col>
                      ))}
                    </Row>
                  )}
                </Card>
              )}
            </Form.List>
          </Col>
        </Row>

        {/* 计算公式区域 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={24}>
            <Card 
              title={
                <Space>
                  <FormOutlined />
                  <span>计算公式</span>
                  <Tooltip title="使用参数名称编写计算公式">
                    <InfoCircleOutlined />
                  </Tooltip>
                </Space>
              }
            >
              <AttributesList attributes={attributesList} />
              
              <Form.Item
                name="expression"
                label="计算公式"
                extra="在公式中可以使用上面定义的参数名称，例如: 长*宽*高*0.01"
                rules={[{ required: true, message: '请输入计算公式' }]}
              >
                <TextArea
                  rows={4}
                  placeholder="请输入计算公式，例如: 长*宽*高*0.01"
                />
              </Form.Item>
            </Card>
          </Col>
        </Row>
        
        {/* 提交按钮 */}
        <div style={{ display: 'flex', justifyContent: 'center', marginBottom: 48 }}>
          <Space size="large">
            <Link href="/admin/customFormula">
              <Button size="large">取消</Button>
            </Link>
            <Button 
              type="primary" 
              size="large" 
              icon={<SaveOutlined />} 
              htmlType="submit"
              loading={submitting || asyncLoading}
            >
              保存公式
            </Button>
          </Space>
        </div>
      </Form>
    </div>
  );
} 