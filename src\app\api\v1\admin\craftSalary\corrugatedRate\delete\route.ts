import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const deleteCorrugatedRateSchema = z.object({
  id: z.number().int().positive(),
});

export const POST = withValidation(
  deleteCorrugatedRateSchema,
  async (request: NextRequest, validatedData: z.infer<typeof deleteCorrugatedRateSchema>) => {
    const { id } = validatedData;

    // 检查瓦楞率配置是否存在
    const existingCorrugatedRate = await prisma.corrugatedRate.findFirst({
      where: {
        id: id,
        isDel: false,
      },
    });

    assert(!!existingCorrugatedRate, ErrorCode.NOT_FOUND, '瓦楞率配置不存在');

    // 软删除瓦楞率配置
    await prisma.corrugatedRate.update({
      where: { id },
      data: { isDel: true },
    });

    return successResponse({ id }, '删除瓦楞率配置成功');
  }
); 