import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { CorrugatedProcessListParams, corrugatedProcessListParamsSchema } from '@/lib/validations/admin/corrugatedProcess';
import { withValidation } from '@/lib/middleware/errorHandler';
import { paginatedResponse } from '@/lib/utils/apiResponse';
import { Prisma } from '@prisma/client';

export const POST = withValidation(
  corrugatedProcessListParamsSchema,
  async (request: NextRequest, validatedQuery: CorrugatedProcessListParams) => {
    const data = validatedQuery;
    const page = data.page ?? 1;
    const pageSize = data.pageSize ?? 10;
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const where: Prisma.CorrugatedProcessWhereInput = {
      isDel: false,
    };

    if (data.search) {
      where.OR = [
        { code: { contains: data.search } },
        { materialName: { contains: data.search } },
        { remark: { contains: data.search } },
      ];
    }

    // 构建排序条件
    const sortBy = data.sortBy ?? 'createdAt';
    const sortOrder = data.sortOrder ?? 'desc';
    const orderBy: Prisma.CorrugatedProcessOrderByWithRelationInput = {
      [sortBy]: sortOrder
    } as Prisma.CorrugatedProcessOrderByWithRelationInput;

    // 查询总数和列表数据
    const [total, list] = await Promise.all([
      prisma.corrugatedProcess.count({ where }),
      prisma.corrugatedProcess.findMany({
        where,
        orderBy,
        skip,
        take: pageSize,
      })
    ]);

    return paginatedResponse(
      list,
      {
        page,
        pageSize,
        total,
      },
      '获取瓦楞工艺列表成功'
    );
  }
); 