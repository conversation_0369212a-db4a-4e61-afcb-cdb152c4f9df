-- 材料尺寸初始化数据
-- 插入6个标准尺寸配置，如果type不存在则插入，存在则更新

INSERT INTO material_size (name, type, size, description, sort_order, is_default, created_at, updated_at) VALUES
('正度长度', 'regularLength', 1092, '正度纸张长度，单位毫米', 1, true, NOW(), NOW()),
('正度宽度', 'regularWidth', 787, '正度纸张宽度，单位毫米', 2, true, NOW(), NOW()),
('大度长度', 'largeLength', 1194, '大度纸张长度，单位毫米', 3, true, NOW(), NOW()),
('大度宽度', 'largeWidth', 889, '大度纸张宽度，单位毫米', 4, true, NOW(), NOW()),
('特规长度', 'specialLength', 787, '特规纸张长度，单位毫米', 5, true, NOW(), NOW()),
('特规宽度', 'specialWidth', 1092, '特规纸张宽度，单位毫米', 6, true, NOW(), NOW())
ON DUPLICATE KEY UPDATE
  name = VALUES(name),
  size = VALUES(size),
  description = VALUES(description),
  sort_order = VALUES(sort_order),
  is_default = VALUES(is_default),
  updated_at = NOW(); 