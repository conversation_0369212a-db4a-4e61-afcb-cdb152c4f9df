/**
 * 数据格式化工具函数
 * 提供统一的数据格式化功能
 */

/**
 * 尺寸对象接口
 */
export interface Dimensions {
  width: number;
  height: number;
  length?: number;
}

/**
 * 格式化尺寸字符串
 * @param dimensions 尺寸对象
 * @param unit 单位（默认为mm）
 * @param precision 精度（默认为1位小数）
 * @returns 格式化后的尺寸字符串
 */
export const formatDimensions = (
  dimensions: Dimensions,
  unit: string = 'mm',
  precision: number = 1
): string => {
  if (dimensions.length !== undefined) {
    // 三维尺寸：长×宽×高
    return `${dimensions.length.toFixed(precision)}×${dimensions.width.toFixed(precision)}×${dimensions.height.toFixed(precision)}${unit}`;
  } else {
    // 二维尺寸：宽×高
    return `${dimensions.width.toFixed(precision)}×${dimensions.height.toFixed(precision)}${unit}`;
  }
};

/**
 * 格式化面积值
 * @param area 面积值（平方毫米）
 * @param targetUnit 目标单位（m²或mm²）
 * @param precision 精度（默认为3位小数）
 * @returns 格式化后的面积字符串
 */
export const formatArea = (
  area: number,
  targetUnit: 'm²' | 'mm²' = 'm²',
  precision: number = 3
): string => {
  if (targetUnit === 'm²') {
    const areaM2 = area / 1000000; // 转换mm²到m²
    return `${areaM2.toFixed(precision)} m²`;
  } else {
    return `${area.toFixed(precision)} mm²`;
  }
};

/**
 * 格式化利用率百分比
 * @param rate 利用率（0-100）
 * @param precision 精度（默认为1位小数）
 * @returns 格式化后的百分比字符串
 */
export const formatUtilizationRate = (
  rate: number,
  precision: number = 1
): string => {
  return `${rate.toFixed(precision)}%`;
};

/**
 * 格式化材料规格
 * @param spec 规格代码
 * @param size 尺寸对象（可选）
 * @returns 格式化后的规格字符串
 */
export const formatSpecification = (
  spec: string,
  size?: Dimensions
): string => {
  const specNames: Record<string, string> = {
    'regular': '正度',
    'large': '大度',
    'special': '特规',
    'stock': '现货尺寸',
    'custom': '自定义'
  };
  
  const specName = specNames[spec] || spec;
  
  if (size) {
    return `${specName} (${formatDimensions(size)})`;
  }
  
  return specName;
};

/**
 * 格式化数量和单位
 * @param quantity 数量
 * @param unit 单位
 * @param precision 精度（默认根据单位自动判断）
 * @returns 格式化后的数量字符串
 */
export const formatQuantityWithUnit = (
  quantity: number,
  unit: string,
  precision?: number
): string => {
  // 根据单位自动判断精度
  let defaultPrecision = 0;
  if (unit.includes('m²') || unit.includes('平方')) {
    defaultPrecision = 2;
  } else if (unit.includes('吨')) {
    defaultPrecision = 4;
  } else if (unit.includes('缺少')) {
    defaultPrecision = 0;
  }
  
  const finalPrecision = precision !== undefined ? precision : defaultPrecision;
  return `${quantity.toFixed(finalPrecision)} ${unit}`;
};

/**
 * 格式化拼版信息
 * @param impositionX X方向拼版数
 * @param impositionY Y方向拼版数
 * @param isRotated 是否旋转
 * @returns 格式化后的拼版信息字符串
 */
export const formatImpositionInfo = (
  impositionX: number,
  impositionY: number,
  isRotated: boolean = false
): string => {
  const total = impositionX * impositionY;
  const rotationText = isRotated ? ' (已旋转)' : '';
  return `${impositionX}×${impositionY} = ${total}个/张${rotationText}`;
};

/**
 * 格式化材料名称和规格
 * @param materialName 材料名称
 * @param spec 规格
 * @param size 尺寸（可选）
 * @returns 格式化后的材料信息字符串
 */
export const formatMaterialNameWithSpec = (
  materialName: string,
  spec: string,
  size?: Dimensions
): string => {
  const formattedSpec = formatSpecification(spec, size);
  return `${materialName} (${formattedSpec})`;
};

/**
 * 格式化工艺参数
 * @param parameters 参数对象
 * @returns 格式化后的参数字符串
 */
export const formatProcessParameters = (
  parameters: Record<string, any>
): string => {
  const paramStrings: string[] = [];
  
  Object.entries(parameters).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      // 特殊处理一些常见参数
      switch (key) {
        case 'length':
        case 'width':
        case 'height':
          paramStrings.push(`${key}: ${value}mm`);
          break;
        case 'area':
          paramStrings.push(`${key}: ${formatArea(value)}`);
          break;
        default:
          paramStrings.push(`${key}: ${value}`);
          break;
      }
    }
  });
  
  return paramStrings.join(', ');
};

/**
 * 格式化时间戳
 * @param timestamp 时间戳或日期字符串
 * @param format 格式类型
 * @returns 格式化后的时间字符串
 */
export const formatTimestamp = (
  timestamp: string | number | Date,
  format: 'date' | 'datetime' | 'time' = 'datetime'
): string => {
  const date = new Date(timestamp);
  
  if (isNaN(date.getTime())) {
    return '无效日期';
  }
  
  const options: Intl.DateTimeFormatOptions = {
    timeZone: 'Asia/Shanghai'
  };
  
  switch (format) {
    case 'date':
      options.year = 'numeric';
      options.month = '2-digit';
      options.day = '2-digit';
      break;
    case 'time':
      options.hour = '2-digit';
      options.minute = '2-digit';
      options.second = '2-digit';
      break;
    case 'datetime':
    default:
      options.year = 'numeric';
      options.month = '2-digit';
      options.day = '2-digit';
      options.hour = '2-digit';
      options.minute = '2-digit';
      options.second = '2-digit';
      break;
  }
  
  return date.toLocaleString('zh-CN', options);
};

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @param precision 精度（默认为2位小数）
 * @returns 格式化后的文件大小字符串
 */
export const formatFileSize = (
  bytes: number,
  precision: number = 2
): string => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(precision))} ${sizes[i]}`;
};

/**
 * 安全的数字格式化
 * @param value 要格式化的值
 * @param precision 精度
 * @param defaultValue 默认值
 * @returns 格式化后的数字字符串
 */
export const safeFormatNumber = (
  value: any,
  precision: number = 2,
  defaultValue: string = '0'
): string => {
  const num = Number(value);
  if (isNaN(num)) {
    return defaultValue;
  }
  return num.toFixed(precision);
};
