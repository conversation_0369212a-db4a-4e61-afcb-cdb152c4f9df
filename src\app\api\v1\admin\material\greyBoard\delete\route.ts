import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { deleteGreyBoardSchema, DeleteGreyBoardParams } from '@/lib/validations/admin/greyBoard';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation<DeleteGreyBoardParams>(
  deleteGreyBoardSchema,
  async (request: NextRequest, validatedQuery: DeleteGreyBoardParams) => {
    // 检查灰板纸是否存在
    const existingGreyBoard = await prisma.greyBoard.findFirst({
      where: {
        id: validatedQuery.id,
        isDel: false,
      },
    });

    assertExists(existingGreyBoard, ErrorCode.MATERIAL_NOT_FOUND, '灰板纸不存在');

    // 软删除灰板纸
    await prisma.greyBoard.update({
      where: { id: validatedQuery.id },
      data: {
        isDel: true,
      },
    });

    return successResponse({ id: validatedQuery.id }, '删除灰板纸成功');
  }
); 