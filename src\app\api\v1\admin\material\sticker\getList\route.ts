import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getStickerListSchema, StickerQueryParams } from '@/lib/validations/admin/sticker';
import { withValidation } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { paginatedResponse } from '@/lib/utils/apiResponse';

const handler = withValidation<StickerQueryParams>(
  getStickerListSchema,
  async (request: AuthenticatedRequest, validatedQuery: StickerQueryParams) => {
    const { page, pageSize, keyword, category } = validatedQuery;

    // 构建查询条件
    const where = {
      isDel: false,
      ...(keyword && {
        OR: [
          { name: { contains: keyword } },
          { unit: { contains: keyword } },
          { category: { contains: keyword } },
        ],
      }),
      ...(category && { category: { contains: category } }),
    };

    // 查询总数和数据
    const [total, list] = await Promise.all([
      prisma.sticker.count({ where }),
      prisma.sticker.findMany({
        where,
        orderBy: { id: 'desc' },
        skip: (page - 1) * pageSize,
        take: pageSize,
      }),
    ]);

    const pagination = { total, page, pageSize };
    return paginatedResponse(list, pagination, '获取不干胶列表成功');
  }
); 
export const POST = withInternalAuth(handler);