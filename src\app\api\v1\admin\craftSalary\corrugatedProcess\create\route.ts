import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { CreateCorrugatedProcessData, createCorrugatedProcessSchema } from '@/lib/validations/admin/corrugatedProcess';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation(
  createCorrugatedProcessSchema,
  async (request: NextRequest, validatedData: CreateCorrugatedProcessData) => {
    const data = validatedData;

    // 检查材质名称是否重复
    const existingCorrugatedProcess = await prisma.corrugatedProcess.findFirst({
      where: {
        materialName: data.materialName,
        isDel: false,
      },
    });

    assert(!existingCorrugatedProcess, ErrorCode.DUPLICATE_ENTRY, '材质名称已存在');

    // 创建瓦楞工艺
    const corrugatedProcess = await prisma.corrugatedProcess.create({
      data: {
        code: data.code,
        materialName: data.materialName,
        price: data.price,
        unit: data.unit,
        setupFee: data.setupFee,
        thickness: data.thickness,
        coreWeight1: data.coreWeight1,
        fluteType1: data.fluteType1,
        linerWeight1: data.linerWeight1,
        coreWeight2: data.coreWeight2,
        fluteType2: data.fluteType2,
        linerWeight2: data.linerWeight2,
        remark: data.remark,
      },
    });

    return successResponse(corrugatedProcess, '创建瓦楞工艺成功');
  }
); 