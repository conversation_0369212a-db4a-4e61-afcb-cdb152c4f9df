import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withValidation } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';

export const POST = withValidation(
  null,
  async (request: NextRequest) => {
    // 查询所有特殊材料的品类，去重并排序
    const categories = await prisma.specialMaterial.findMany({
      where: {
        isDel: false,
        category: {
          not: '',
        },
      },
      select: {
        category: true,
      },
      distinct: ['category'],
      orderBy: {
        category: 'asc',
      },
    });

    // 提取品类名称并过滤空值
    const categoryList = categories
      .map(item => item.category)
      .filter(Boolean) as string[];

    return successResponse(categoryList, '获取特殊材料品类列表成功');
  }
); 