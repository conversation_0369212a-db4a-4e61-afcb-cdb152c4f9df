import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const getPrintingDetailSchema = z.object({
  id: z.coerce.number().positive('ID必须是正整数')
});

export const POST = withValidation(
  getPrintingDetailSchema,
  async (request: NextRequest, validatedQuery: any) => {
    const data = validatedQuery;

    // 查询印刷数据详细信息
    const printing = await prisma.printing.findFirst({
      where: {
        id: data.id,
        isDel: false,
      },
    });

    assertExists(printing, ErrorCode.RESOURCE_NOT_FOUND, '印刷数据不存在');

    return successResponse(printing, '获取印刷配置详情成功');
  }
); 