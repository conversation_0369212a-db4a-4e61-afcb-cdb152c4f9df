'use client';

import React, { useState, useEffect } from 'react';
import {
  Table, Button, Space, Modal, Form, Input,
  Select, InputNumber, Popconfirm, Card, Typography,
  message, Tabs, Row, Col,
  Tag
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined,
  SearchOutlined, ReloadOutlined, PrinterOutlined, SettingOutlined
} from '@ant-design/icons';
import { printingApi, printingMachineApi } from '@/services/adminApi';
import { Printing, PrintingMachine, PRINTING_UNITS, PrintingUnit } from '@/types/craftSalary';
import { useAsyncError } from '@/lib/hooks/useErrorHandler';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

/**
 * 印刷管理主页面
 */
export default function PrintingManagementPage() {
  // 错误处理Hook
  const { execute: executePrinting, loading: printingLoading } = useAsyncError();
  const { execute: executeMachine, loading: machineLoading } = useAsyncError();

  // 印刷数据相关状态
  const [printingList, setPrintingList] = useState<Printing[]>([]);
  const [printingTotal, setPrintingTotal] = useState(0);
  const [printingCurrent, setPrintingCurrent] = useState(1);
  const [printingPageSize, setPrintingPageSize] = useState(10);
  const [printingKeyword, setPrintingKeyword] = useState('');
  const [printingUnit, setPrintingUnit] = useState<PrintingUnit | ''>('');

  // 印刷机数据相关状态
  const [machineList, setMachineList] = useState<PrintingMachine[]>([]);
  const [machineTotal, setMachineTotal] = useState(0);
  const [machineCurrent, setMachineCurrent] = useState(1);
  const [machinePageSize, setMachinePageSize] = useState(10);
  const [machineKeyword, setMachineKeyword] = useState('');

  // 模态框相关状态
  const [printingModalVisible, setPrintingModalVisible] = useState(false);
  const [machineModalVisible, setMachineModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [printingForm] = Form.useForm();
  const [machineForm] = Form.useForm();

  // 初始加载数据
  useEffect(() => {
    fetchPrintingList();
    fetchMachineList();
  }, []);

  // 获取印刷数据列表
  const fetchPrintingList = async (page = printingCurrent, pageSize = printingPageSize, search = printingKeyword, unit = printingUnit) => {
    const requestParams: any = {
      page,
      pageSize,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    };
    
    if (search) {
      requestParams.search = search;
    }
    
    if (unit) {
      requestParams.unit = unit;
    }
    
    const result = await executePrinting(async () => {
      return await printingApi.getList(requestParams);
    }, '获取印刷数据列表');

    if (result) {
      setPrintingList(result.list || []);
      setPrintingTotal(result.pagination?.total || 0);
    } else {
      setPrintingList([]);
      setPrintingTotal(0);
    }
  };

  // 获取印刷机数据列表
  const fetchMachineList = async (page = machineCurrent, pageSize = machinePageSize, search = machineKeyword) => {
    const requestParams: any = {
      page,
      pageSize,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    };
    
    if (search) {
      requestParams.search = search;
    }
    
    const result = await executeMachine(async () => {
      return await printingMachineApi.getList(requestParams);
    }, '获取印刷机数据列表');

    if (result) {
      setMachineList(result.list || []);
      setMachineTotal(result.pagination?.total || 0);
    } else {
      setMachineList([]);
      setMachineTotal(0);
    }
  };

  // 处理印刷数据分页变化
  const handlePrintingTableChange = (pagination: any) => {
    setPrintingCurrent(pagination.current);
    setPrintingPageSize(pagination.pageSize);
    fetchPrintingList(pagination.current, pagination.pageSize);
  };

  // 处理印刷机分页变化
  const handleMachineTableChange = (pagination: any) => {
    setMachineCurrent(pagination.current);
    setMachinePageSize(pagination.pageSize);
    fetchMachineList(pagination.current, pagination.pageSize);
  };

  // 打开添加印刷数据模态框
  const showAddPrintingModal = () => {
    setModalTitle('添加印刷配置');
    setEditingRecord(null);
    printingForm.resetFields();
    printingForm.setFieldsValue({
      basePrice: 0,
      price1000_1999: 0,
      price2000_2999: 0,
      price3000_3999: 0,
      price4000_4999: 0,
      price5000_5999: 0,
      price6000_6999: 0,
      price7000_7999: 0,
      price8000_8999: 0,
      price9000_9999: 0,
      price10000Plus: 0.01,
      ctpPlateFee: 0,
      spotColorFee: 0,
      unit: '元/张'
    });
    setPrintingModalVisible(true);
  };

  // 打开编辑印刷数据模态框
  const showEditPrintingModal = (record: Printing) => {
    setModalTitle('编辑印刷配置');
    setEditingRecord(record);
    printingForm.setFieldsValue({
      machineModel: record.machineModel,
      basePrice: record.basePrice,
      price1000_1999: record.price1000_1999,
      price2000_2999: record.price2000_2999,
      price3000_3999: record.price3000_3999,
      price4000_4999: record.price4000_4999,
      price5000_5999: record.price5000_5999,
      price6000_6999: record.price6000_6999,
      price7000_7999: record.price7000_7999,
      price8000_8999: record.price8000_8999,
      price9000_9999: record.price9000_9999,
      price10000Plus: record.price10000Plus,
      unit: record.unit,
      ctpPlateFee: record.ctpPlateFee,
      spotColorFee: record.spotColorFee,
      remark: record.remark || ''
    });
    setPrintingModalVisible(true);
  };

  // 打开添加印刷机模态框
  const showAddMachineModal = () => {
    setModalTitle('添加印刷机');
    setEditingRecord(null);
    machineForm.resetFields();
    setMachineModalVisible(true);
  };

  // 打开编辑印刷机模态框
  const showEditMachineModal = (record: PrintingMachine) => {
    setModalTitle('编辑印刷机');
    setEditingRecord(record);
    machineForm.setFieldsValue({
      machineName: record.machineName,
      maxLength: record.maxLength,
      maxWidth: record.maxWidth,
      remark: record.remark || ''
    });
    setMachineModalVisible(true);
  };

  // 处理印刷数据表单提交
  const handlePrintingFormSubmit = async () => {
    try {
      const values = await printingForm.validateFields();
      
      const requestData = {
        machineModel: values.machineModel,
        basePrice: Number(values.basePrice),
        price1000_1999: Number(values.price1000_1999),
        price2000_2999: Number(values.price2000_2999),
        price3000_3999: Number(values.price3000_3999),
        price4000_4999: Number(values.price4000_4999),
        price5000_5999: Number(values.price5000_5999),
        price6000_6999: Number(values.price6000_6999),
        price7000_7999: Number(values.price7000_7999),
        price8000_8999: Number(values.price8000_8999),
        price9000_9999: Number(values.price9000_9999),
        price10000Plus: Number(values.price10000Plus),
        unit: values.unit,
        ctpPlateFee: Number(values.ctpPlateFee),
        spotColorFee: Number(values.spotColorFee),
        remark: values.remark || ''
      };

      const result = await executePrinting(async () => {
        if (editingRecord) {
          return await printingApi.update({ ...requestData, id: editingRecord.id });
        } else {
          return await printingApi.create(requestData);
        }
      }, editingRecord ? '更新印刷配置' : '创建印刷配置');

      if (result) {
        message.success('保存成功');
        setPrintingModalVisible(false);
        fetchPrintingList();
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理印刷机表单提交
  const handleMachineFormSubmit = async () => {
    try {
      const values = await machineForm.validateFields();
      
      const requestData = {
        machineName: values.machineName,
        maxLength: Number(values.maxLength),
        maxWidth: Number(values.maxWidth),
        remark: values.remark || ''
      };

      const result = await executeMachine(async () => {
        if (editingRecord) {
          return await printingMachineApi.update({ ...requestData, id: editingRecord.id });
        } else {
          return await printingMachineApi.create(requestData);
        }
      }, editingRecord ? '更新印刷机' : '创建印刷机');

      if (result) {
        setMachineModalVisible(false);
        fetchMachineList();
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 删除印刷数据
  const handleDeletePrinting = async (id: number) => {
    const result = await executePrinting(async () => {
      return await printingApi.delete(id);
    }, '删除印刷数据');

    if (result) {
      fetchPrintingList();
    }
  };

  // 删除印刷机
  const handleDeleteMachine = async (id: number) => {
    const result = await executeMachine(async () => {
      return await printingMachineApi.delete(id);
    }, '删除印刷机');

    if (result) {
      fetchMachineList();
    }
  };

  // 印刷数据表格列定义
  const printingColumns = [
    {
      title: '印刷机型',
      dataIndex: 'machineModel',
      key: 'machineModel',
      align: 'center' as const,
      width: 120,
    },
    {
      title: '计价单位',
      dataIndex: 'unit',
      key: 'unit',
      align: 'center' as const,
      width: 100,
      render: (unit: string) => (
        <Tag color="blue">{unit}</Tag>
      )
    },
    {
      title: '起步价',
      dataIndex: 'basePrice',
      key: 'basePrice',
      align: 'center' as const,
      width: 80,
      render: (price: number) => `¥${price.toFixed(2)}`
    },
    {
      title: '1000-1999',
      dataIndex: 'price1000_1999',
      key: 'price1000_1999',
      align: 'center' as const,
      width: 80,
      render: (price: number) => price > 0 ? `¥${price.toFixed(2)}` : '-'
    },
    {
      title: '2000-2999',
      dataIndex: 'price2000_2999',
      key: 'price2000_2999',
      align: 'center' as const,
      width: 80,
      render: (price: number) => price > 0 ? `¥${price.toFixed(2)}` : '-'
    },
    {
      title: '10000以上',
      dataIndex: 'price10000Plus',
      key: 'price10000Plus',
      align: 'center' as const,
      width: 80,
      render: (price: number) => `¥${price.toFixed(2)}`
    },
    {
      title: 'CTP板费',
      dataIndex: 'ctpPlateFee',
      key: 'ctpPlateFee',
      align: 'center' as const,
      width: 80,
      render: (price: number) => price > 0 ? `¥${price.toFixed(2)}` : '-'
    },
    {
      title: '专色费',
      dataIndex: 'spotColorFee',
      key: 'spotColorFee',
      align: 'center' as const,
      width: 80,
      render: (price: number) => price > 0 ? `¥${price.toFixed(2)}` : '-'
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      align: 'center' as const,
      width: 120,
      ellipsis: true,
      render: (remark: string) => remark || '-'
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      align: 'center' as const,
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'action',
      align: 'center' as const,
      fixed: 'right' as const,
      width: 150,
      render: (_: any, record: Printing) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => showEditPrintingModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此印刷配置吗？"
            onConfirm={() => handleDeletePrinting(record.id)}
            okText="是"
            cancelText="否"
          >
            <Button
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 印刷机表格列定义
  const machineColumns = [
    {
      title: '印刷机名称',
      dataIndex: 'machineName',
      key: 'machineName',
      align: 'center' as const,
      width: 200,
    },
    {
      title: '最大长度(mm)',
      dataIndex: 'maxLength',
      key: 'maxLength',
      align: 'center' as const,
      width: 120,
      render: (length: number) => `${length.toLocaleString()}`
    },
    {
      title: '最大宽度(mm)',
      dataIndex: 'maxWidth',
      key: 'maxWidth',
      align: 'center' as const,
      width: 120,
      render: (width: number) => `${width.toLocaleString()}`
    },
    {
      title: '最大面积(平方米)',
      key: 'maxArea',
      align: 'center' as const,
      width: 130,
      render: (_: any, record: PrintingMachine) => {
        const area = (record.maxLength * record.maxWidth) / 1000000;
        return `${area.toFixed(2)} m²`;
      }
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      align: 'center' as const,
      width: 200,
      ellipsis: true,
      render: (remark: string) => remark || '-'
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      align: 'center' as const,
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'action',
      align: 'center' as const,
      fixed: 'right' as const,
      width: 150,
      render: (_: any, record: PrintingMachine) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => showEditMachineModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此印刷机吗？"
            onConfirm={() => handleDeleteMachine(record.id)}
            okText="是"
            cancelText="否"
          >
            <Button
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>印刷管理</Title>

      <Tabs
        defaultActiveKey="1"
        items={[
          {
            key: '1',
            label: (
              <span>
                印刷工艺价格
              </span>
            ),
            children: (
              <Card>
                <div style={{ marginBottom: 16 }}>
                  <Row gutter={16} align="middle">
                    <Col span={4}>
                      <Input
                        placeholder="搜索机型或备注"
                        prefix={<SearchOutlined />}
                        allowClear
                        value={printingKeyword}
                        onChange={(e) => setPrintingKeyword(e.target.value)}
                        onPressEnter={() => fetchPrintingList(1, printingPageSize, printingKeyword, printingUnit)}
                      />
                    </Col>
                    <Col>
                      <Button
                        type="primary"
                        icon={<SearchOutlined />}
                        onClick={() => fetchPrintingList(1, printingPageSize, printingKeyword, printingUnit)}
                      >
                        搜索
                      </Button>
                    </Col>
                    <Col>
                      <Button
                        icon={<ReloadOutlined />}
                        onClick={() => {
                          setPrintingKeyword('');
                          setPrintingUnit('');
                          fetchPrintingList(1, printingPageSize, '', '');
                        }}
                      >
                        重置
                      </Button>
                    </Col>
                    <Col flex="auto" style={{ textAlign: 'right' }}>
                      <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={showAddPrintingModal}
                      >
                        添加印刷配置
                      </Button>
                    </Col>
                  </Row>
                </div>

                <Table
                  columns={printingColumns}
                  dataSource={printingList}
                  rowKey="id"
                  pagination={{
                    current: printingCurrent,
                    pageSize: printingPageSize,
                    total: printingTotal,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                  }}
                  loading={printingLoading}
                  onChange={handlePrintingTableChange}
                  bordered
                  size="middle"
                  scroll={{ x: 1200 }}
                  locale={{ emptyText: '暂无数据' }}
                />
              </Card>
            )
          },
          {
            key: '2',
            label: (
              <span>
                印刷机尺寸配置
              </span>
            ),
            children: (
              <Card>
                <div style={{ marginBottom: 16 }}>
                  <Row gutter={16} align="middle">
                    <Col span={6}>
                      <Input
                        placeholder="搜索印刷机名称或备注"
                        prefix={<SearchOutlined />}
                        allowClear
                        value={machineKeyword}
                        onChange={(e) => setMachineKeyword(e.target.value)}
                        onPressEnter={() => fetchMachineList(1, machinePageSize, machineKeyword)}
                      />
                    </Col>
                    <Col>
                      <Button
                        type="primary"
                        icon={<SearchOutlined />}
                        onClick={() => fetchMachineList(1, machinePageSize, machineKeyword)}
                      >
                        搜索
                      </Button>
                    </Col>
                    <Col>
                      <Button
                        icon={<ReloadOutlined />}
                        onClick={() => {
                          setMachineKeyword('');
                          fetchMachineList(1, machinePageSize, '');
                        }}
                      >
                        重置
                      </Button>
                    </Col>
                    <Col flex="auto" style={{ textAlign: 'right' }}>
                      <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={showAddMachineModal}
                      >
                        添加印刷机
                      </Button>
                    </Col>
                  </Row>
                </div>

                <Table
                  columns={machineColumns}
                  dataSource={machineList}
                  rowKey="id"
                  pagination={{
                    current: machineCurrent,
                    pageSize: machinePageSize,
                    total: machineTotal,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                  }}
                  loading={machineLoading}
                  onChange={handleMachineTableChange}
                  bordered
                  size="middle"
                  scroll={{ x: 1000 }}
                  locale={{ emptyText: '暂无数据' }}
                />
              </Card>
            )
          }
        ]}
      />

      {/* 印刷数据表单模态框 */}
      <Modal
        title={modalTitle}
        open={printingModalVisible}
        onOk={handlePrintingFormSubmit}
        onCancel={() => setPrintingModalVisible(false)}
        width={800}
        okText="确定"
        cancelText="取消"
      >
        <Form
          form={printingForm}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="machineModel"
                label="印刷机型"
                rules={[
                  { required: true, message: '请输入印刷机型' },
                  { max: 100, message: '机型名称不能超过100字符' }
                ]}
              >
                <Input placeholder="请输入印刷机型" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="unit"
                label="计价单位"
                rules={[{ required: true, message: '请选择计价单位' }]}
              >
                <Select placeholder="请选择计价单位">
                  {PRINTING_UNITS.map(unit => (
                    <Option key={unit} value={unit}>{unit}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="basePrice"
                label="起步价"
                rules={[{ required: true, message: '请输入起步价' }]}
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="0.00"
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="ctpPlateFee"
                label="CTP板费"
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="0.00"
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="spotColorFee"
                label="专色费"
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="0.00"
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
          </Row>

          <div style={{ marginBottom: 16 }}>
            <h4 style={{ marginBottom: 16 }}>分段定价配置</h4>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="price1000_1999"
                  label="1000-1999"
                >
                  <InputNumber
                    min={0}
                    precision={2}
                    style={{ width: '100%' }}
                    placeholder="0.00"
                    addonBefore="¥"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="price2000_2999"
                  label="2000-2999"
                >
                  <InputNumber
                    min={0}
                    precision={2}
                    style={{ width: '100%' }}
                    placeholder="0.00"
                    addonBefore="¥"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="price3000_3999"
                  label="3000-3999"
                >
                  <InputNumber
                    min={0}
                    precision={2}
                    style={{ width: '100%' }}
                    placeholder="0.00"
                    addonBefore="¥"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="price4000_4999"
                  label="4000-4999"
                >
                  <InputNumber
                    min={0}
                    precision={2}
                    style={{ width: '100%' }}
                    placeholder="0.00"
                    addonBefore="¥"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="price5000_5999"
                  label="5000-5999"
                >
                  <InputNumber
                    min={0}
                    precision={2}
                    style={{ width: '100%' }}
                    placeholder="0.00"
                    addonBefore="¥"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="price6000_6999"
                  label="6000-6999"
                >
                  <InputNumber
                    min={0}
                    precision={2}
                    style={{ width: '100%' }}
                    placeholder="0.00"
                    addonBefore="¥"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="price7000_7999"
                  label="7000-7999"
                >
                  <InputNumber
                    min={0}
                    precision={2}
                    style={{ width: '100%' }}
                    placeholder="0.00"
                    addonBefore="¥"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="price8000_8999"
                  label="8000-8999"
                >
                  <InputNumber
                    min={0}
                    precision={2}
                    style={{ width: '100%' }}
                    placeholder="0.00"
                    addonBefore="¥"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="price9000_9999"
                  label="9000-9999"
                >
                  <InputNumber
                    min={0}
                    precision={2}
                    style={{ width: '100%' }}
                    placeholder="0.00"
                    addonBefore="¥"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="price10000Plus"
                  label="10000以上单价"
                  rules={[{ required: true, message: '请输入10000以上单价' }]}
                >
                  <InputNumber
                    min={0.01}
                    precision={2}
                    style={{ width: '100%' }}
                    placeholder="0.01"
                    addonBefore="¥"
                  />
                </Form.Item>
              </Col>
            </Row>
          </div>

          <Form.Item
            name="remark"
            label="备注"
          >
            <TextArea
              placeholder="请输入备注信息（可选）"
              rows={3}
              showCount
              maxLength={500}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 印刷机表单模态框 */}
      <Modal
        title={modalTitle}
        open={machineModalVisible}
        onOk={handleMachineFormSubmit}
        onCancel={() => setMachineModalVisible(false)}
        width={600}
        okText="确定"
        cancelText="取消"
      >
        <Form
          form={machineForm}
          layout="vertical"
        >
          <Form.Item
            name="machineName"
            label="印刷机名称"
            rules={[
              { required: true, message: '请输入印刷机名称' },
              { max: 100, message: '机器名称不能超过100字符' }
            ]}
          >
            <Input placeholder="请输入印刷机名称" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="maxLength"
                label="最大印刷长度(mm)"
                rules={[{ required: true, message: '请输入最大长度' }]}
              >
                <InputNumber
                  min={1}
                  style={{ width: '100%' }}
                  placeholder="请输入最大长度"
                  addonAfter="mm"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="maxWidth"
                label="最大印刷宽度(mm)"
                rules={[{ required: true, message: '请输入最大宽度' }]}
              >
                <InputNumber
                  min={1}
                  style={{ width: '100%' }}
                  placeholder="请输入最大宽度"
                  addonAfter="mm"
                />
              </Form.Item>
            </Col>
          </Row>

          {/* 实时显示最大面积 */}
          <Form.Item shouldUpdate={(prevValues, curValues) => 
            prevValues.maxLength !== curValues.maxLength || 
            prevValues.maxWidth !== curValues.maxWidth
          }>
            {({ getFieldValue }) => {
              const length = Number(getFieldValue('maxLength')) || 0;
              const width = Number(getFieldValue('maxWidth')) || 0;
              const area = (length * width) / 1000000;
              
              return (
                <div style={{ padding: 12, backgroundColor: '#f5f5f5', borderRadius: 6, marginBottom: 16 }}>
                  <div style={{ fontSize: 14, color: '#666' }}>
                    最大印刷面积: <span style={{ fontWeight: 'bold', color: '#1890ff' }}>
                      {area > 0 ? `${area.toFixed(2)} m²` : '请输入长度和宽度'}
                    </span>
                  </div>
                  {length > 0 && width > 0 && (
                    <div style={{ fontSize: 12, color: '#999', marginTop: 4 }}>
                      {length.toLocaleString()} mm × {width.toLocaleString()} mm
                    </div>
                  )}
                </div>
              );
            }}
          </Form.Item>

          <Form.Item
            name="remark"
            label="备注"
          >
            <TextArea
              placeholder="请输入备注信息（可选）"
              rows={3}
              showCount
              maxLength={500}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
} 