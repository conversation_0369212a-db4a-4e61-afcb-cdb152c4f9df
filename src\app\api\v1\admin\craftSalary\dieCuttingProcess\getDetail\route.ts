import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const getDieCuttingProcessDetailSchema = z.object({
  id: z.coerce.number().positive('ID必须是正整数')
});

export const POST = withValidation(
  getDieCuttingProcessDetailSchema,
  async (request: NextRequest, validatedQuery: any) => {
    const data = validatedQuery;

    // 查询模切工艺详细信息
    const dieCuttingProcess = await prisma.dieCuttingProcess.findFirst({
      where: {
        id: data.id,
        isDel: false,
      },
    });

    assertExists(dieCuttingProcess, ErrorCode.RESOURCE_NOT_FOUND, '模切工艺不存在');

    return successResponse(dieCuttingProcess, '获取模切工艺详情成功');
  }
);
