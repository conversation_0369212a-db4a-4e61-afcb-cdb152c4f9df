import { z } from 'zod';
import { LAMINATING_PROCESS_UNITS } from '@/types/craftSalary';

// 对裱工艺表单验证模式
export const laminatingProcessFormSchema = z.object({
  name: z.string()
    .min(1, '请输入对裱工艺名称')
    .max(100, '名称长度不能超过100字符'),
  
  price: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '价格必须是大于等于0的数字'),
  
  unit: z.enum(LAMINATING_PROCESS_UNITS, {
    required_error: '请选择计价单位',
    invalid_type_error: '无效的计价单位',
  }),
  
  basePrice: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '起步价必须是大于等于0的数字'),
  
  remark: z.string()
    .max(500, '备注长度不能超过500字符')
    .optional()
    .or(z.literal(''))
});

// 对裱工艺创建请求验证模式
export const createLaminatingProcessSchema = z.object({
  name: z.string().min(1).max(100),
  price: z.number().min(0),
  unit: z.enum(LAMINATING_PROCESS_UNITS),
  basePrice: z.number().min(0),
  remark: z.string().max(500).optional()
});

// 对裱工艺更新请求验证模式
export const updateLaminatingProcessSchema = createLaminatingProcessSchema.extend({
  id: z.number().int().positive()
});

// 对裱工艺列表查询参数验证模式
export const laminatingProcessListParamsSchema = z.object({
  page: z.number().int().positive().optional(),
  pageSize: z.number().int().positive().max(100).optional(),
  search: z.string().optional(),
  unit: z.enum(LAMINATING_PROCESS_UNITS).optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
});

// 表单数据转换函数
export function transformLaminatingProcessFormData(formData: z.infer<typeof laminatingProcessFormSchema>) {
  return {
    name: formData.name,
    price: Number(formData.price),
    unit: formData.unit,
    basePrice: Number(formData.basePrice),
    remark: formData.remark || undefined
  };
}

export type LaminatingProcessFormData = z.infer<typeof laminatingProcessFormSchema>;
export type CreateLaminatingProcessData = z.infer<typeof createLaminatingProcessSchema>;
export type UpdateLaminatingProcessData = z.infer<typeof updateLaminatingProcessSchema>; 