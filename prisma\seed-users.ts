import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

export enum UserRole {
  USER = 'user',
  SUPER_USER = 'super_user', 
  INTERNAL_USER = 'internal_user',
  ADMIN = 'admin'
}

async function main() {
  console.log('开始创建初始用户...');

  // 检查是否已存在管理员用户
  const existingAdmin = await prisma.user.findFirst({
    where: {
      role: UserRole.ADMIN,
      isDel: false
    }
  });

  if (existingAdmin) {
    console.log('管理员用户已存在，跳过创建');
    return;
  }

  // 创建默认管理员用户
  const adminPassword = await bcrypt.hash('admin123456', 12);
  
  const admin = await prisma.user.create({
    data: {
      name: '系统管理员',
      phone: '13800138000',
      email: '<EMAIL>',
      password: adminPassword,
      role: UserRole.ADMIN,
      state: 1, // 启用状态
    }
  });

  console.log('管理员用户创建成功:', {
    id: admin.id,
    name: admin.name,
    phone: admin.phone,
    email: admin.email,
    role: admin.role
  });

  // 创建测试用户
  const userPassword = await bcrypt.hash('user123456', 12);
  
  const testUser = await prisma.user.create({
    data: {
      name: '测试用户',
      phone: '13800138001',
      email: '<EMAIL>',
      password: userPassword,
      role: UserRole.USER,
      state: 1, // 启用状态
    }
  });

  console.log('测试用户创建成功:', {
    id: testUser.id,
    name: testUser.name,
    phone: testUser.phone,
    email: testUser.email,
    role: testUser.role
  });

  // 创建超级用户（有到期时间）
  const superUserPassword = await bcrypt.hash('super123456', 12);
  const expiresAt = new Date();
  expiresAt.setFullYear(expiresAt.getFullYear() + 1); // 一年后过期
  
  const superUser = await prisma.user.create({
    data: {
      name: '超级用户',
      phone: '13800138002',
      email: '<EMAIL>',
      password: superUserPassword,
      role: UserRole.SUPER_USER,
      expiresAt: expiresAt,
      state: 1, // 启用状态
    }
  });

  console.log('超级用户创建成功:', {
    id: superUser.id,
    name: superUser.name,
    phone: superUser.phone,
    email: superUser.email,
    role: superUser.role,
    expiresAt: superUser.expiresAt
  });

  // 创建内部用户
  const internalUserPassword = await bcrypt.hash('internal123456', 12);
  
  const internalUser = await prisma.user.create({
    data: {
      name: '内部用户',
      phone: '13800138003',
      email: '<EMAIL>',
      password: internalUserPassword,
      role: UserRole.INTERNAL_USER,
      state: 1, // 启用状态
    }
  });

  console.log('内部用户创建成功:', {
    id: internalUser.id,
    name: internalUser.name,
    phone: internalUser.phone,
    email: internalUser.email,
    role: internalUser.role
  });

  console.log('\n初始用户创建完成！');
  console.log('登录信息：');
  console.log('管理员 - 手机号: 13800138000, 密码: admin123456');
  console.log('测试用户 - 手机号: 13800138001, 密码: user123456');
  console.log('超级用户 - 手机号: 13800138002, 密码: super123456');
  console.log('内部用户 - 手机号: 13800138003, 密码: internal123456');
}

main()
  .catch((e) => {
    console.error('创建用户失败:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
