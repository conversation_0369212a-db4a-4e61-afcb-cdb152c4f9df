import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createUserSchema, CreateUserParams } from '@/lib/validations/user';
import { withValidation } from '@/lib/middleware/errorHandler';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { hashPassword } from '@/lib/auth/password';
import { UserRole, UserState } from '@/types/user';

const handler = withValidation<CreateUserParams>(
  createUserSchema,
  async (request: AuthenticatedRequest, validatedData: CreateUserParams) => {
    const { name, phone, email, password, role, expiresAt, state } = validatedData;

    try {
      // 检查手机号是否已存在
      const existingPhoneUser = await prisma.user.findFirst({
        where: {
          phone,
          isDel: false
        }
      });

      if (existingPhoneUser) {
        return NextResponse.json(
          errorResponse(ErrorCode.DUPLICATE_ENTRY, '手机号已存在'),
          { status: 400 }
        );
      }

      // 检查邮箱是否已存在（如果提供了邮箱）
      if (email && email.trim()) {
        const existingEmailUser = await prisma.user.findFirst({
          where: {
            email: email.trim(),
            isDel: false
          }
        });

        if (existingEmailUser) {
          return NextResponse.json(
            errorResponse(ErrorCode.DUPLICATE_ENTRY, '邮箱已存在'),
            { status: 400 }
          );
        }
      }

      // 哈希密码
      const hashedPassword = await hashPassword(password);

      // 准备用户数据
      const userData: any = {
        name: name.trim(),
        phone: phone.trim(),
        email: email && email.trim() ? email.trim() : null,
        password: hashedPassword,
        role,
        state: state ?? UserState.ENABLED
      };

      // 如果是超级用户，设置到期时间
      if (role === UserRole.SUPER_USER && expiresAt) {
        userData.expiresAt = new Date(expiresAt);
      }

      // 创建用户
      const newUser = await prisma.user.create({
        data: userData,
        select: {
          id: true,
          name: true,
          phone: true,
          email: true,
          role: true,
          expiresAt: true,
          state: true,
          createdAt: true,
          updatedAt: true
        }
      });

      return NextResponse.json(
        successResponse(newUser, '用户创建成功')
      );

    } catch (error) {
      console.error('创建用户失败:', error);
      
      // 处理数据库唯一约束错误
      if (error instanceof Error && error.message.includes('Unique constraint')) {
        if (error.message.includes('phone')) {
          return NextResponse.json(
            errorResponse(ErrorCode.DUPLICATE_ENTRY, '手机号已存在'),
            { status: 400 }
          );
        }
        if (error.message.includes('email')) {
          return NextResponse.json(
            errorResponse(ErrorCode.DUPLICATE_ENTRY, '邮箱已存在'),
            { status: 400 }
          );
        }
      }

      return NextResponse.json(
        errorResponse(ErrorCode.INTERNAL_ERROR, '创建用户失败，请稍后重试'),
        { status: 500 }
      );
    }
  }
);

export const POST = withInternalAuth(handler);
