'use client';

import React, { useState, useEffect } from 'react';
import {
  Table, Button, Space, Modal, Form, Input,
  Select, InputNumber, Popconfirm, Card, Typography,
  message, Row, Col, Tag, Tabs
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined,
  SearchOutlined, ReloadOutlined
} from '@ant-design/icons';
import { dieCuttingProcessApi, dieCuttingPlateFeeApi } from '@/services/adminApi';
import { 
  DieCuttingProcess, 
  DieCuttingPlateFee,
  DIE_CUTTING_PROCESS_UNITS, 
  DIE_CUTTING_PLATE_FEE_UNITS,
  DieCuttingProcessUnit,
  DieCuttingPlateFeeUnit
} from '@/types/craftSalary';
import { useAsyncError } from '@/lib/hooks/useErrorHandler';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

/**
 * 模切工艺管理页面
 */
export default function DieCuttingProcessManagementPage() {
  // 错误处理Hook
  const { execute: executeProcess, loading: processLoading } = useAsyncError();
  const { execute: executePlateFee, loading: plateFeeLoading } = useAsyncError();

  // 当前Tab
  const [activeTab, setActiveTab] = useState('process');

  // 模切工艺数据相关状态
  const [processList, setProcessList] = useState<DieCuttingProcess[]>([]);
  const [processTotal, setProcessTotal] = useState(0);
  const [processCurrent, setProcessCurrent] = useState(1);
  const [processPageSize, setProcessPageSize] = useState(10);
  const [processKeyword, setProcessKeyword] = useState('');
  const [processUnit, setProcessUnit] = useState<DieCuttingProcessUnit | ''>('');

  // 刀版费数据相关状态
  const [plateFeeList, setPlateFeeList] = useState<DieCuttingPlateFee[]>([]);
  const [plateFeeTotal, setPlateFeeTotal] = useState(0);
  const [plateFeeCurrent, setPlateFeeCurrent] = useState(1);
  const [plateFeePageSize, setPlateFeePageSize] = useState(10);
  const [plateFeeKeyword, setPlateFeeKeyword] = useState('');
  const [plateFeeUnit, setPlateFeeUnit] = useState<DieCuttingPlateFeeUnit | ''>('');

  // 模态框相关状态
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [processForm] = Form.useForm();
  const [plateFeeForm] = Form.useForm();

  // 初始加载数据
  useEffect(() => {
    if (activeTab === 'process') {
      fetchProcessList();
    } else {
      fetchPlateFeeList();
    }
  }, [activeTab]);

  // 获取模切工艺数据列表
  const fetchProcessList = async (page = processCurrent, pageSize_ = processPageSize, search = processKeyword, unit_ = processUnit) => {
    const requestParams: any = {
      page,
      pageSize: pageSize_,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    };
    
    if (search) {
      requestParams.search = search;
    }
    
    if (unit_) {
      requestParams.unit = unit_;
    }
    
    const result = await executeProcess(async () => {
      return await dieCuttingProcessApi.getList(requestParams);
    }, '获取模切工艺列表');

    if (result) {
      setProcessList(result.list || []);
      setProcessTotal(result.pagination?.total || 0);
    } else {
      setProcessList([]);
      setProcessTotal(0);
    }
  };

  // 获取刀版费数据列表
  const fetchPlateFeeList = async (page = plateFeeCurrent, pageSize_ = plateFeePageSize, search = plateFeeKeyword, unit_ = plateFeeUnit) => {
    const requestParams: any = {
      page,
      pageSize: pageSize_,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    };
    
    if (search) {
      requestParams.search = search;
    }
    
    if (unit_) {
      requestParams.unit = unit_;
    }
    
    const result = await executePlateFee(async () => {
      return await dieCuttingPlateFeeApi.getList(requestParams);
    }, '获取刀版费列表');

    if (result) {
      setPlateFeeList(result.list || []);
      setPlateFeeTotal(result.pagination?.total || 0);
    } else {
      setPlateFeeList([]);
      setPlateFeeTotal(0);
    }
  };

  // 处理模切工艺分页变化
  const handleProcessTableChange = (pagination: any) => {
    setProcessCurrent(pagination.current);
    setProcessPageSize(pagination.pageSize);
    fetchProcessList(pagination.current, pagination.pageSize);
  };

  // 处理刀版费分页变化
  const handlePlateFeeTableChange = (pagination: any) => {
    setPlateFeeCurrent(pagination.current);
    setPlateFeePageSize(pagination.pageSize);
    fetchPlateFeeList(pagination.current, pagination.pageSize);
  };

  // 打开添加模切工艺模态框
  const showAddProcessModal = () => {
    setModalTitle('添加模切工艺');
    setEditingRecord(null);
    processForm.resetFields();
    processForm.setFieldsValue({
      price: 0,
      basePrice: 0,
      unit: '元/张'
    });
    setModalVisible(true);
  };

  // 打开编辑模切工艺模态框
  const showEditProcessModal = (record: DieCuttingProcess) => {
    setModalTitle('编辑模切工艺');
    setEditingRecord(record);
    processForm.setFieldsValue({
      name: record.name,
      price: record.price,
      unit: record.unit,
      basePrice: record.basePrice,
      remark: record.remark || ''
    });
    setModalVisible(true);
  };

  // 打开添加刀版费模态框
  const showAddPlateFeeModal = () => {
    setModalTitle('添加刀版费');
    setEditingRecord(null);
    plateFeeForm.resetFields();
    plateFeeForm.setFieldsValue({
      price: 0,
      basePrice: 0,
      impositionQuantity: 0,
      unit: '元/平方'
    });
    setModalVisible(true);
  };

  // 打开编辑刀版费模态框
  const showEditPlateFeeModal = (record: DieCuttingPlateFee) => {
    setModalTitle('编辑刀版费');
    setEditingRecord(record);
    plateFeeForm.setFieldsValue({
      name: record.name,
      price: record.price,
      unit: record.unit,
      basePrice: record.basePrice,
      impositionQuantity: record.impositionQuantity,
      remark: record.remark || ''
    });
    setModalVisible(true);
  };

  // 处理模切工艺表单提交
  const handleProcessFormSubmit = async () => {
    try {
      const values = await processForm.validateFields();
      
      const requestData = {
        name: values.name,
        price: Number(values.price),
        unit: values.unit,
        basePrice: Number(values.basePrice),
        remark: values.remark || ''
      };

      const result = await executeProcess(async () => {
        if (editingRecord) {
          return await dieCuttingProcessApi.update({ ...requestData, id: editingRecord.id });
        } else {
          return await dieCuttingProcessApi.create(requestData);
        }
      }, editingRecord ? '更新模切工艺' : '创建模切工艺');

      if (result) {
        message.success('保存成功');
        setModalVisible(false);
        fetchProcessList();
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理刀版费表单提交
  const handlePlateFeeFormSubmit = async () => {
    try {
      const values = await plateFeeForm.validateFields();
      
      const requestData = {
        name: values.name,
        price: Number(values.price),
        unit: values.unit,
        basePrice: Number(values.basePrice),
        impositionQuantity: Number(values.impositionQuantity),
        remark: values.remark || ''
      };

      const result = await executePlateFee(async () => {
        if (editingRecord) {
          return await dieCuttingPlateFeeApi.update({ ...requestData, id: editingRecord.id });
        } else {
          return await dieCuttingPlateFeeApi.create(requestData);
        }
      }, editingRecord ? '更新刀版费' : '创建刀版费');

      if (result) {
        message.success('保存成功');
        setModalVisible(false);
        fetchPlateFeeList();
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 删除模切工艺
  const handleDeleteProcess = async (id: number) => {
    const result = await executeProcess(async () => {
      return await dieCuttingProcessApi.delete(id);
    }, '删除模切工艺');

    if (result) {
      fetchProcessList();
    }
  };

  // 删除刀版费
  const handleDeletePlateFee = async (id: number) => {
    const result = await executePlateFee(async () => {
      return await dieCuttingPlateFeeApi.delete(id);
    }, '删除刀版费');

    if (result) {
      fetchPlateFeeList();
    }
  };

  // 模切工艺表格列定义
  const processColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center' as const,
      width: 150,
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      align: 'center' as const,
      width: 100,
      render: (price: number) => `¥${price.toFixed(2)}`
    },
    {
      title: '计价单位',
      dataIndex: 'unit',
      key: 'unit',
      align: 'center' as const,
      width: 100,
      render: (unit: string) => (
        <Tag color="blue">{unit}</Tag>
      )
    },
    {
      title: '起步价',
      dataIndex: 'basePrice',
      key: 'basePrice',
      align: 'center' as const,
      width: 100,
      render: (price: number) => price > 0 ? `¥${price.toFixed(2)}` : '-'
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      align: 'center' as const,
      width: 200,
      ellipsis: true,
      render: (remark: string) => remark || '-'
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      align: 'center' as const,
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'action',
      align: 'center' as const,
      fixed: 'right' as const,
      width: 150,
      render: (_: any, record: DieCuttingProcess) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => showEditProcessModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此模切工艺吗？"
            onConfirm={() => handleDeleteProcess(record.id)}
            okText="是"
            cancelText="否"
          >
            <Button
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 刀版费表格列定义
  const plateFeeColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center' as const,
      width: 150,
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      align: 'center' as const,
      width: 100,
      render: (price: number) => `¥${price.toFixed(2)}`
    },
    {
      title: '计价单位',
      dataIndex: 'unit',
      key: 'unit',
      align: 'center' as const,
      width: 100,
      render: (unit: string) => (
        <Tag color="blue">{unit}</Tag>
      )
    },
    {
      title: '起步金额',
      dataIndex: 'basePrice',
      key: 'basePrice',
      align: 'center' as const,
      width: 100,
      render: (price: number) => price > 0 ? `¥${price.toFixed(2)}` : '-'
    },
    {
      title: '按拼版数量',
      dataIndex: 'impositionQuantity',
      key: 'impositionQuantity',
      align: 'center' as const,
      width: 120,
      render: (quantity: number) => quantity > 0 ? quantity.toString() : '-'
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      align: 'center' as const,
      width: 200,
      ellipsis: true,
      render: (remark: string) => remark || '-'
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      align: 'center' as const,
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'action',
      align: 'center' as const,
      fixed: 'right' as const,
      width: 150,
      render: (_: any, record: DieCuttingPlateFee) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => showEditPlateFeeModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此刀版费吗？"
            onConfirm={() => handleDeletePlateFee(record.id)}
            okText="是"
            cancelText="否"
          >
            <Button
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>模切工艺管理</Title>

      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'process',
              label: '模切工艺',
              children: (
                <div>
                  <div style={{ marginBottom: 16 }}>
                    <Row gutter={16} align="middle">
                      <Col span={4}>
                        <Input
                          placeholder="搜索名称或备注"
                          prefix={<SearchOutlined />}
                          allowClear
                          value={processKeyword}
                          onChange={(e) => setProcessKeyword(e.target.value)}
                          onPressEnter={() => fetchProcessList(1, processPageSize, processKeyword, processUnit)}
                        />
                      </Col>
                      <Col>
                        <Button
                          type="primary"
                          icon={<SearchOutlined />}
                          onClick={() => fetchProcessList(1, processPageSize, processKeyword, processUnit)}
                        >
                          搜索
                        </Button>
                      </Col>
                      <Col>
                        <Button
                          icon={<ReloadOutlined />}
                          onClick={() => {
                            setProcessKeyword('');
                            setProcessUnit('');
                            fetchProcessList(1, processPageSize, '', '');
                          }}
                        >
                          重置
                        </Button>
                      </Col>
                      <Col flex="auto" style={{ textAlign: 'right' }}>
                        <Button
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={showAddProcessModal}
                        >
                          添加模切工艺
                        </Button>
                      </Col>
                    </Row>
                  </div>

                  <Table
                    columns={processColumns}
                    dataSource={processList}
                    rowKey="id"
                    pagination={{
                      current: processCurrent,
                      pageSize: processPageSize,
                      total: processTotal,
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total) => `共 ${total} 条`,
                    }}
                    loading={processLoading}
                    onChange={handleProcessTableChange}
                    bordered
                    size="middle"
                    scroll={{ x: 1000 }}
                    locale={{ emptyText: '暂无数据' }}
                  />
                </div>
              )
            },
            {
              key: 'plateFee',
              label: '刀版费',
              children: (
                <div>
                  <div style={{ marginBottom: 16 }}>
                    <Row gutter={16} align="middle">
                      <Col span={4}>
                        <Input
                          placeholder="搜索名称或备注"
                          prefix={<SearchOutlined />}
                          allowClear
                          value={plateFeeKeyword}
                          onChange={(e) => setPlateFeeKeyword(e.target.value)}
                          onPressEnter={() => fetchPlateFeeList(1, plateFeePageSize, plateFeeKeyword, plateFeeUnit)}
                        />
                      </Col>
                      <Col>
                        <Button
                          type="primary"
                          icon={<SearchOutlined />}
                          onClick={() => fetchPlateFeeList(1, plateFeePageSize, plateFeeKeyword, plateFeeUnit)}
                        >
                          搜索
                        </Button>
                      </Col>
                      <Col>
                        <Button
                          icon={<ReloadOutlined />}
                          onClick={() => {
                            setPlateFeeKeyword('');
                            setPlateFeeUnit('');
                            fetchPlateFeeList(1, plateFeePageSize, '', '');
                          }}
                        >
                          重置
                        </Button>
                      </Col>
                      <Col flex="auto" style={{ textAlign: 'right' }}>
                        <Button
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={showAddPlateFeeModal}
                        >
                          添加刀版费
                        </Button>
                      </Col>
                    </Row>
                  </div>

                  <Table
                    columns={plateFeeColumns}
                    dataSource={plateFeeList}
                    rowKey="id"
                    pagination={{
                      current: plateFeeCurrent,
                      pageSize: plateFeePageSize,
                      total: plateFeeTotal,
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total) => `共 ${total} 条`,
                    }}
                    loading={plateFeeLoading}
                    onChange={handlePlateFeeTableChange}
                    bordered
                    size="middle"
                    scroll={{ x: 1200 }}
                    locale={{ emptyText: '暂无数据' }}
                  />
                </div>
              )
            }
          ]}
        />
      </Card>

      {/* 模切工艺表单模态框 */}
      {activeTab === 'process' && (
        <Modal
          title={modalTitle}
          open={modalVisible}
          onOk={handleProcessFormSubmit}
          onCancel={() => setModalVisible(false)}
          width={600}
          okText="确定"
          cancelText="取消"
        >
          <Form
            form={processForm}
            layout="vertical"
          >
            <Form.Item
              name="name"
              label="名称"
              rules={[
                { required: true, message: '请输入模切工艺名称' },
                { max: 100, message: '名称长度不能超过100字符' }
              ]}
            >
              <Input placeholder="请输入模切工艺名称" />
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="price"
                  label="价格"
                  rules={[{ required: true, message: '请输入价格' }]}
                >
                  <InputNumber
                    min={0}
                    precision={2}
                    style={{ width: '100%' }}
                    placeholder="0.00"
                    addonBefore="¥"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="unit"
                  label="计价单位"
                  rules={[{ required: true, message: '请选择计价单位' }]}
                >
                  <Select placeholder="请选择计价单位">
                    {DIE_CUTTING_PROCESS_UNITS.map(unit => (
                      <Option key={unit} value={unit}>{unit}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="basePrice"
              label="起步价"
            >
              <InputNumber
                min={0}
                precision={2}
                style={{ width: '100%' }}
                placeholder="0.00"
                addonBefore="¥"
              />
            </Form.Item>

            <Form.Item
              name="remark"
              label="备注"
            >
              <TextArea
                placeholder="请输入备注信息（可选）"
                rows={3}
                showCount
                maxLength={500}
              />
            </Form.Item>
          </Form>
        </Modal>
      )}

      {/* 刀版费表单模态框 */}
      {activeTab === 'plateFee' && (
        <Modal
          title={modalTitle}
          open={modalVisible}
          onOk={handlePlateFeeFormSubmit}
          onCancel={() => setModalVisible(false)}
          width={600}
          okText="确定"
          cancelText="取消"
        >
          <Form
            form={plateFeeForm}
            layout="vertical"
          >
            <Form.Item
              name="name"
              label="名称"
              rules={[
                { required: true, message: '请输入刀版费名称' },
                { max: 100, message: '名称长度不能超过100字符' }
              ]}
            >
              <Input placeholder="请输入刀版费名称" />
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="price"
                  label="价格"
                  rules={[{ required: true, message: '请输入价格' }]}
                >
                  <InputNumber
                    min={0}
                    precision={2}
                    style={{ width: '100%' }}
                    placeholder="0.00"
                    addonBefore="¥"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="unit"
                  label="计价单位"
                  rules={[{ required: true, message: '请选择计价单位' }]}
                >
                  <Select placeholder="请选择计价单位">
                    {DIE_CUTTING_PLATE_FEE_UNITS.map(unit => (
                      <Option key={unit} value={unit}>{unit}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="basePrice"
                  label="起步金额"
                >
                  <InputNumber
                    min={0}
                    precision={2}
                    style={{ width: '100%' }}
                    placeholder="0.00"
                    addonBefore="¥"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="impositionQuantity"
                  label="按拼版数量"
                >
                  <InputNumber
                    min={0}
                    precision={2}
                    style={{ width: '100%' }}
                    placeholder="0.00"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="remark"
              label="备注"
            >
              <TextArea
                placeholder="请输入备注信息（可选）"
                rows={3}
                showCount
                maxLength={500}
              />
            </Form.Item>
          </Form>
        </Modal>
      )}
    </div>
  );
}
