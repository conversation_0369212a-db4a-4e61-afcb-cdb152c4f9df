import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { CreateLaminatingProcessData, createLaminatingProcessSchema } from '@/lib/validations/admin/laminatingProcess';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation(
  createLaminatingProcessSchema,
  async (request: NextRequest, validatedData: CreateLaminatingProcessData) => {
    const data = validatedData;

    // 检查名称是否重复
    const existingLaminatingProcess = await prisma.laminatingProcess.findFirst({
      where: {
        name: data.name,
        isDel: false,
      },
    });

    assert(!existingLaminatingProcess, ErrorCode.DUPLICATE_ENTRY, '对裱工艺名称已存在');

    // 创建对裱工艺
    const laminatingProcess = await prisma.laminatingProcess.create({
      data: {
        name: data.name,
        price: data.price,
        unit: data.unit,
        basePrice: data.basePrice,
        remark: data.remark,
      },
    });

    return successResponse(laminatingProcess, '创建对裱工艺成功');
  }
); 