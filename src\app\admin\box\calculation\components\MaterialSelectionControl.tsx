/**
 * 材料选择控制组件
 * 根据部件合并结果选择对应的材料
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card, Form, Select, Space, Tag, Alert, Typography, Row, Col, Spin } from 'antd';
import { ExperimentOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { perfLog, apiCallTracker } from '@/lib/utils/perfLog';
import { MaterialTypeAnalysis, PartGroup } from '../types/packaging';
import { MaterialConfig, PartMaterialConfig } from '../types/calculation';
import { Paper, SpecialPaper, GreyBoard, BoxMaterial } from '@/types/material';
import { paperApi, specialPaperApi, greyBoardApi, materialSizeApi, boxMaterialApi } from '@/services/adminApi';
import { printingMachineApi } from '@/services/adminApi';
import { validateFacePaperConfig, validateGreyBoardConfig } from '../validations/materials';

const { Text } = Typography;
const { Option } = Select;

// 材料规格选项
interface MaterialSpecOption {
  value: string;
  label: string;
  size?: { width: number; height: number };
  available: boolean;
  price?: number;
}

interface MaterialSelectionControlProps {
  materialAnalysis: MaterialTypeAnalysis;
  facePartGroups: PartGroup[];
  greyPartGroups: PartGroup[];
  selectedMaterials?: MaterialConfig;
  onMaterialChange: (materials: Partial<MaterialConfig>) => void;
  onPartMaterialConfigChange?: (configs: Record<string, PartMaterialConfig>) => void;
  showValidation?: boolean; // 控制是否显示验证状态
  // 新增：从外部传入的部件材料配置，用于状态持久化
  initialPartMaterialConfigs?: Record<string, PartMaterialConfig>;
  // 新增：控制是否允许初始化数据，防止重复API调用
  allowDataInitialization?: boolean;
  // 新增：从外部传入的材料数据，避免重复API调用
  materialData?: {
    papers: Paper[];
    specialPapers: SpecialPaper[];
    greyBoards: GreyBoard[];
    boxMaterials: BoxMaterial[];
    printingMachines: any[];
    materialSizes: any;
    dataFetched: {
      papers: boolean;
      specialPapers: boolean;
      greyBoards: boolean;
      boxMaterials: boolean;
      printingMachines: boolean;
      materialSizes: boolean;
    };
  };
}

const MaterialSelectionControlComponent: React.FC<MaterialSelectionControlProps> = ({
  materialAnalysis,
  facePartGroups,
  greyPartGroups,
  onPartMaterialConfigChange,
  showValidation = false,
  initialPartMaterialConfigs = {},
  allowDataInitialization = true,
  materialData }) => {

  // 添加调试信息
  perfLog.debug('MaterialSelectionControl 组件渲染', {
    canSelectFacePaper: materialAnalysis.canSelectFacePaper,
    canSelectGreyBoard: materialAnalysis.canSelectGreyBoard,
    facePartGroupsLength: facePartGroups.length,
    greyPartGroupsLength: greyPartGroups.length,
    hasExternalMaterialData: !!materialData,
    timestamp: Date.now()
  });

  // 使用外部传入的材料数据，如果没有则使用内部状态
  const [internalPapers, setInternalPapers] = useState<Paper[]>([]);
  const [internalSpecialPapers, setInternalSpecialPapers] = useState<SpecialPaper[]>([]);
  const [internalGreyBoards, setInternalGreyBoards] = useState<GreyBoard[]>([]);
  const [internalBoxMaterials, setInternalBoxMaterials] = useState<BoxMaterial[]>([]);
  const [internalPrintingMachines, setInternalPrintingMachines] = useState<any[]>([]);
  const [internalMaterialSizes, setInternalMaterialSizes] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  // 添加状态来跟踪是否已经获取过数据，避免重复调用
  const [internalDataFetched, setInternalDataFetched] = useState({
    papers: false,
    specialPapers: false,
    greyBoards: false,
    boxMaterials: false,
    printingMachines: false,
    materialSizes: false
  });

  // 使用外部数据或内部数据
  const papers = materialData?.papers || internalPapers;
  const specialPapers = materialData?.specialPapers || internalSpecialPapers;
  const greyBoards = materialData?.greyBoards || internalGreyBoards;
  const boxMaterials = materialData?.boxMaterials || internalBoxMaterials;
  const printingMachines = materialData?.printingMachines || internalPrintingMachines;
  const materialSizes = materialData?.materialSizes || internalMaterialSizes;
  const dataFetched = materialData?.dataFetched || internalDataFetched;

  // 添加调试信息
  perfLog.debug('MaterialSelectionControl 材料数据状态:', {
    hasExternalData: !!materialData,
    papersCount: papers.length,
    specialPapersCount: specialPapers.length,
    greyBoardsCount: greyBoards.length,
    boxMaterialsCount: boxMaterials.length,
    printingMachinesCount: printingMachines.length,
    hasMaterialSizes: !!materialSizes,
    dataFetched
  });

  // 添加外部数据详细调试信息
  if (materialData) {
    perfLog.debug('MaterialSelectionControl 外部材料数据详情:', {
      externalPapersCount: materialData.papers?.length || 0,
      externalSpecialPapersCount: materialData.specialPapers?.length || 0,
      externalGreyBoardsCount: materialData.greyBoards?.length || 0,
      externalBoxMaterialsCount: materialData.boxMaterials?.length || 0,
      externalPrintingMachinesCount: materialData.printingMachines?.length || 0,
      externalDataFetched: materialData.dataFetched,
      specialPapersData: materialData.specialPapers?.map(p => ({ id: p.id, name: p.name })) || []
    });
  }

  // 每个部件组的材料配置 - 使用传入的初始配置进行状态持久化
  const [partMaterialConfigs, setPartMaterialConfigs] = useState<Record<string, PartMaterialConfig>>(initialPartMaterialConfigs);

  // 添加部件组和配置的调试信息
  perfLog.debug('MaterialSelectionControl 部件组和配置状态:', {
    facePartGroupsCount: facePartGroups.length,
    greyPartGroupsCount: greyPartGroups.length,
    facePartGroupIds: facePartGroups.map(g => g.id),
    greyPartGroupIds: greyPartGroups.map(g => g.id),
    partMaterialConfigsKeys: Object.keys(partMaterialConfigs),
    initialPartMaterialConfigsKeys: Object.keys(initialPartMaterialConfigs || {}),
    partMaterialConfigs: Object.keys(partMaterialConfigs).map(key => ({
      id: key,
      category: partMaterialConfigs[key]?.materialCategory,
      materialId: partMaterialConfigs[key]?.materialId,
      materialName: partMaterialConfigs[key]?.materialName,
      hasConfig: !!partMaterialConfigs[key]
    })),
    // 详细的配置内容
    detailedConfigs: partMaterialConfigs
  });

  // 获取部件组的验证状态
  const getPartGroupValidationStatus = (groupId: string, materialType: 'face' | 'grey') => {
    const config = partMaterialConfigs[groupId];
    if (!config) {
      return { isValid: false, errors: ['未配置材料'] };
    }

    const errors: string[] = [];

    // 检查基本字段
    if (!config.materialId) errors.push('未选择材料');
    if (!config.materialSpec) errors.push('未选择规格');

    // 面纸需要额外检查打印机
    if (materialType === 'face' && !config.printingMachineId) {
      errors.push('未选择打印机');
    }

    const isValid = materialType === 'face'
      ? validateFacePaperConfig(config)
      : validateGreyBoardConfig(config);

    return { isValid, errors };
  };

  // 获取纸类材料
  const fetchPapers = async () => {
    const apiKey = 'paper-getList';

    if (dataFetched.papers) {
      perfLog.debug('纸类材料已获取，跳过重复调用');
      return;
    }

    if (!apiCallTracker.canCall(apiKey)) {
      perfLog.debug('纸类材料API调用被阻止，防止重复调用');
      return;
    }

    try {
      setLoading(true);
      const paperResult = await paperApi.getList({ page: 1, pageSize: 100 });
      const papers = paperResult.success && paperResult.data ? paperResult.data.list : [];
      setInternalPapers(papers);
      setInternalDataFetched(prev => ({ ...prev, papers: true }));
    } catch (error) {
      perfLog.error('获取纸类材料失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取特种纸材料
  const fetchSpecialPapers = async () => {
    const apiKey = 'specialPaper-getList';

    if (dataFetched.specialPapers) {
      perfLog.debug('特种纸材料已获取，跳过重复调用');
      return;
    }

    if (!apiCallTracker.canCall(apiKey)) {
      perfLog.debug('特种纸材料API调用被阻止，防止重复调用');
      return;
    }

    try {
      setLoading(true);
      const specialPaperResult = await specialPaperApi.getList({ page: 1, pageSize: 100 });
      const specialPapers = specialPaperResult.success && specialPaperResult.data ? specialPaperResult.data.list : [];
      setInternalSpecialPapers(specialPapers);
      setInternalDataFetched(prev => ({ ...prev, specialPapers: true }));
    } catch (error) {
      perfLog.error('获取特种纸材料失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取灰板纸材料
  const fetchGreyBoards = async () => {
    const apiKey = 'greyBoard-getList';

    if (dataFetched.greyBoards) {
      perfLog.debug('灰板纸材料已获取，跳过重复调用');
      return;
    }

    if (!apiCallTracker.canCall(apiKey)) {
      perfLog.debug('灰板纸材料API调用被阻止，防止重复调用');
      return;
    }

    try {
      setLoading(true);
      const result = await greyBoardApi.getList({ page: 1, pageSize: 100 });
      const greyBoards = result.success && result.data ? result.data.list : [];
      setInternalGreyBoards(greyBoards);
      setInternalDataFetched(prev => ({ ...prev, greyBoards: true }));
    } catch (error) {
      perfLog.error('获取灰板纸材料失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取纸箱材料
  const fetchBoxMaterials = async () => {
    const apiKey = 'boxMaterial-getList';

    if (dataFetched.boxMaterials) {
      perfLog.debug('纸箱材料已获取，跳过重复调用');
      return;
    }

    if (!apiCallTracker.canCall(apiKey)) {
      perfLog.debug('纸箱材料API调用被阻止，防止重复调用');
      return;
    }

    try {
      setLoading(true);
      const result = await boxMaterialApi.getList({ page: 1, pageSize: 100 });
      const boxMaterials = result.success && result.data ? result.data.list : [];
      setInternalBoxMaterials(boxMaterials);
      setInternalDataFetched(prev => ({ ...prev, boxMaterials: true }));
    } catch (error) {
      perfLog.error('获取纸箱材料失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取打印机列表
  const fetchPrintingMachines = async () => {
    const apiKey = 'printingMachine-getOptions';

    if (dataFetched.printingMachines) {
      perfLog.debug('打印机列表已获取，跳过重复调用');
      return;
    }

    if (!apiCallTracker.canCall(apiKey)) {
      perfLog.debug('打印机列表API调用被阻止，防止重复调用');
      return;
    }

    try {
      const result = await printingMachineApi.getOptions();
      if (result.success) {
        setInternalPrintingMachines(result.data);
        setInternalDataFetched(prev => ({ ...prev, printingMachines: true }));
      }
    } catch (error) {
      perfLog.error('获取打印机列表失败:', error);
    }
  };

  // 获取材料尺寸配置
  const fetchMaterialSizes = async () => {
    const apiKey = 'materialSize-getList';

    if (dataFetched.materialSizes) {
      perfLog.debug('材料尺寸配置已获取，跳过重复调用');
      return;
    }

    if (!apiCallTracker.canCall(apiKey)) {
      perfLog.debug('材料尺寸配置API调用被阻止，防止重复调用');
      return;
    }

    try {
      const result = await materialSizeApi.getList();
      if (result.success && result.data) {
        setInternalMaterialSizes(result.data.config);
      }
      setInternalDataFetched(prev => ({ ...prev, materialSizes: true }));
    } catch (error) {
      perfLog.error('获取材料尺寸配置失败:', error);
      // 如果获取失败，使用默认值
      setInternalMaterialSizes({
        regularLength: 1092,
        regularWidth: 787,
        largeLength: 1194,
        largeWidth: 889,
        specialLength: 787,
        specialWidth: 1092
      });
      setInternalDataFetched(prev => ({ ...prev, materialSizes: true }));
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    perfLog.debug('MaterialSelectionControl 组件挂载，开始获取材料尺寸配置');

    // 如果有外部材料数据，则不进行内部API调用
    if (materialData) {
      perfLog.debug('MaterialSelectionControl 使用外部材料数据，跳过材料尺寸配置获取');
      return;
    }

    // 获取材料尺寸配置（只在组件挂载时调用一次）
    fetchMaterialSizes();

    // 组件卸载时的清理
    return () => {
      perfLog.debug('MaterialSelectionControl 组件卸载');
    };
  }, [materialData]); // 添加materialData依赖

  // 根据材料分析结果获取对应的材料数据（受allowDataInitialization控制）
  useEffect(() => {
    // 如果有外部材料数据，则不进行内部API调用
    if (materialData) {
      perfLog.debug('MaterialSelectionControl 使用外部材料数据，跳过内部API调用');
      return;
    }

    if (allowDataInitialization && materialAnalysis.canSelectFacePaper) {
      perfLog.debug('MaterialSelectionControl 开始获取面纸相关数据');
      fetchPapers();
      fetchSpecialPapers();
      fetchPrintingMachines();
    } else if (!allowDataInitialization) {
      perfLog.debug('MaterialSelectionControl 数据初始化被禁用，跳过面纸数据获取');
    }
  }, [materialAnalysis.canSelectFacePaper, allowDataInitialization, materialData]); // 添加materialData依赖

  useEffect(() => {
    // 如果有外部材料数据，则不进行内部API调用
    if (materialData) {
      perfLog.debug('MaterialSelectionControl 使用外部材料数据，跳过内部API调用');
      return;
    }

    if (allowDataInitialization && materialAnalysis.canSelectGreyBoard) {
      perfLog.debug('MaterialSelectionControl 开始获取灰板纸相关数据');
      fetchGreyBoards();
      fetchBoxMaterials();
    } else if (!allowDataInitialization) {
      perfLog.debug('MaterialSelectionControl 数据初始化被禁用，跳过灰板纸数据获取');
    }
  }, [materialAnalysis.canSelectGreyBoard, allowDataInitialization, materialData]); // 添加materialData依赖

  // 当外部传入的初始配置变化时，更新本地状态
  useEffect(() => {
    if (initialPartMaterialConfigs && Object.keys(initialPartMaterialConfigs).length > 0) {
      setPartMaterialConfigs(initialPartMaterialConfigs);
      perfLog.debug('MaterialSelectionControl 从外部恢复状态:', initialPartMaterialConfigs);
    }
  }, [initialPartMaterialConfigs]);

  // 传递部件材料配置变化
  useEffect(() => {
    // 只在有实际配置变化时才输出日志
    const hasConfigs = Object.keys(partMaterialConfigs).length > 0;
    if (hasConfigs) {
      perfLog.debug('部件材料配置变化:', partMaterialConfigs);
    }
    if (onPartMaterialConfigChange) {
      onPartMaterialConfigChange(partMaterialConfigs);
    }
  }, [partMaterialConfigs]); // 移除 onPartMaterialConfigChange 依赖，避免无限循环


  // 统一的材料品类选择处理函数
  const handleMaterialCategoryChange = useCallback((partGroupId: string, category: 'paper' | 'specialPaper' | 'greyBoard' | 'corrugated') => {
    perfLog.debug('材料品类选择变化:', {
      partGroupId,
      category,
      currentConfig: partMaterialConfigs[partGroupId],
      allConfigs: Object.keys(partMaterialConfigs).map(key => ({
        id: key,
        category: partMaterialConfigs[key]?.materialCategory
      }))
    });

    setPartMaterialConfigs(prev => {
      const newConfigs = {
        ...prev,
        [partGroupId]: {
          ...prev[partGroupId],
          partGroupId,
          materialCategory: category,
          // 重置后续选择
          materialId: undefined,
          materialName: undefined,
          materialSpec: undefined,
          specOptions: undefined,
          materialSize: undefined,
          // 瓦楞相关字段
          facePaper: undefined,
          linerPaper: undefined,
          structure: undefined,
          structurePrice: undefined
        }
      };

      perfLog.debug('材料品类选择更新后的配置:', {
        partGroupId,
        newConfig: newConfigs[partGroupId],
        allNewConfigs: Object.keys(newConfigs).map(key => ({
          id: key,
          category: newConfigs[key]?.materialCategory
        }))
      });

      return newConfigs;
    });
  }, [partMaterialConfigs]);

  // 第二步：选择具体材料
  const handleMaterialChange = (partGroupId: string, materialId: number) => {
    const config = partMaterialConfigs[partGroupId];
    if (!config?.materialCategory) return;

    let selectedMaterial: Paper | SpecialPaper | undefined;
    if (config.materialCategory === 'paper') {
      selectedMaterial = papers.find(p => p.id === materialId);
    } else {
      selectedMaterial = specialPapers.find(p => p.id === materialId);
    }

    if (selectedMaterial) {
      // 生成规格选项
      const specOptions = generateSpecOptions(selectedMaterial, config.materialCategory);

      setPartMaterialConfigs(prev => ({
        ...prev,
        [partGroupId]: {
          ...prev[partGroupId],
          materialId,
          materialName: selectedMaterial!.name,
          specOptions: specOptions.map(opt => ({
            value: opt.value,
            label: opt.label,
            size: opt.size,
            available: opt.available,
            price: opt.price
          })),
          // 重置规格选择
          materialSpec: undefined,
          materialSize: undefined
        }
      }));
    }
  };

  // 第三步：选择材料规格
  const handleSpecChange = (partGroupId: string, specValue: string) => {
    const config = partMaterialConfigs[partGroupId];
    const specOption = config?.specOptions?.find(opt => opt.value === specValue);

    perfLog.debug('材料规格选择变化:', {
      partGroupId,
      specValue,
      specOption,
      configSpecOptions: config?.specOptions
    });

    if (specOption) {
      setPartMaterialConfigs(prev => ({
        ...prev,
        [partGroupId]: {
          ...prev[partGroupId],
          materialSpec: specValue,
          materialSize: specOption.size
        }
      }));
    }
  };

  // 处理打印机选择
  const handlePrintingMachineChange = (partGroupId: string, machineId: number | null) => {
    const selectedMachine = printingMachines.find(m => m.value === machineId);

    perfLog.debug('打印机选择变化:', {
      partGroupId,
      machineId,
      selectedMachine,
      printingMachines: printingMachines.map(m => ({ value: m.value, label: m.label, maxLength: m.maxLength, maxWidth: m.maxWidth }))
    });

    if (selectedMachine) {
      setPartMaterialConfigs(prev => ({
        ...prev,
        [partGroupId]: {
          ...prev[partGroupId],
          printingMachineId: machineId,
          printingMachineName: selectedMachine.label,
          printingMachineMaxLength: selectedMachine.maxLength,
          printingMachineMaxWidth: selectedMachine.maxWidth
        }
      }));
    } else {
      // 清除打印机选择
      setPartMaterialConfigs(prev => ({
        ...prev,
        [partGroupId]: {
          ...prev[partGroupId],
          printingMachineId: undefined,
          printingMachineName: undefined,
          printingMachineMaxLength: undefined,
          printingMachineMaxWidth: undefined
        }
      }));
    }
  };

  // 处理灰板纸选择
  const handleGreyBoardChange = (partGroupId: string, greyBoardId: number) => {
    const selectedGreyBoard = greyBoards.find(g => g.id === greyBoardId);
    if (selectedGreyBoard) {
      // 生成规格选项
      const specOptions = generateGreyBoardSpecOptions(selectedGreyBoard);

      setPartMaterialConfigs(prev => ({
        ...prev,
        [partGroupId]: {
          ...prev[partGroupId],
          partGroupId,
          materialId: greyBoardId,
          materialName: selectedGreyBoard.name,
          specOptions: specOptions.map(opt => ({
            value: opt.value,
            label: opt.label,
            size: opt.size,
            available: opt.available,
            price: opt.price
          })),
          // 重置规格选择
          materialSpec: undefined,
          materialSize: undefined
        }
      }));
    }
  };

  // 处理灰板纸规格选择
  const handleGreyBoardSpecChange = (partGroupId: string, specValue: string) => {
    const config = partMaterialConfigs[partGroupId];
    const specOption = config?.specOptions?.find(opt => opt.value === specValue);

    if (specOption) {
      setPartMaterialConfigs(prev => ({
        ...prev,
        [partGroupId]: {
          ...prev[partGroupId],
          materialSpec: specValue,
          materialSize: specOption.size
        }
      }));
    }
  };

  // 处理面纸选择
  const handleFacePaperChange = (partGroupId: string, facePaper: string) => {
    setPartMaterialConfigs(prev => ({
      ...prev,
      [partGroupId]: {
        ...prev[partGroupId],
        materialCategory: 'corrugated', // 设置瓦楞材料分类
        facePaper,
        materialName: facePaper,
        // 重置里纸和结构选择
        linerPaper: undefined,
        structure: undefined,
        structurePrice: undefined,
        materialId: undefined // 清除之前的materialId，因为不再需要
      }
    }));
  };

  // 处理里纸选择
  const handleLinerPaperChange = (partGroupId: string, linerPaper: string) => {
    const config = partMaterialConfigs[partGroupId];

    if (config?.facePaper) {
      setPartMaterialConfigs(prev => ({
        ...prev,
        [partGroupId]: {
          ...prev[partGroupId],
          linerPaper,
          materialName: `${config.facePaper}/${linerPaper}`,
          // 重置结构选择
          structure: undefined,
          structurePrice: undefined
        }
      }));
    }
  };

  // 处理结构选择
  const handleStructureChange = (partGroupId: string, structure: string) => {
    const config = partMaterialConfigs[partGroupId];

    if (config?.facePaper && config?.linerPaper) {
      // 根据面纸和里纸找到匹配的纸箱材料
      const selectedBoxMaterial = boxMaterials.find(m =>
        m.facePaper === config.facePaper && m.linerPaper === config.linerPaper
      );

      if (selectedBoxMaterial) {
        const structurePrice = getStructurePrice(selectedBoxMaterial, structure);
        const structureLabel = getStructureOptions(selectedBoxMaterial).find(s => s.value === structure)?.label || structure;

        setPartMaterialConfigs(prev => ({
          ...prev,
          [partGroupId]: {
            ...prev[partGroupId],
            materialCategory: 'corrugated', // 确保瓦楞材料分类设置
            structure,
            structurePrice,
            materialSpec: structureLabel, // 使用结构标签作为规格显示
            materialSize: undefined, // 瓦楞材料不传递尺寸约束，让拼板选择1x1默认
            materialName: `${config.facePaper}/${config.linerPaper} - ${structureLabel}`,
            materialId: selectedBoxMaterial.id // 设置materialId用于后续计算
          }
        }));
      }
    }
  };

  // 获取面纸选项（去重）
  const getFacePaperOptions = (boxMaterials: BoxMaterial[]): string[] => {
    const facePapers = boxMaterials.map(m => m.facePaper).filter(Boolean);
    return [...new Set(facePapers)];
  };

  // 获取结构选项（根据价格配置）
  const getStructureOptions = (selectedBoxMaterial: BoxMaterial): Array<{value: string, label: string, price?: number}> => {
    const structures = [];

    if (selectedBoxMaterial.threeLayerBE && selectedBoxMaterial.threeLayerBE > 0) {
      structures.push({ value: 'threeLayerBE', label: '三层B/E', price: selectedBoxMaterial.threeLayerBE });
    }
    if (selectedBoxMaterial.threeLayerAC && selectedBoxMaterial.threeLayerAC > 0) {
      structures.push({ value: 'threeLayerAC', label: '三层A/C', price: selectedBoxMaterial.threeLayerAC });
    }
    if (selectedBoxMaterial.fiveLayerABBC && selectedBoxMaterial.fiveLayerABBC > 0) {
      structures.push({ value: 'fiveLayerABBC', label: '五层AB/BC', price: selectedBoxMaterial.fiveLayerABBC });
    }
    if (selectedBoxMaterial.fiveLayerEB && selectedBoxMaterial.fiveLayerEB > 0) {
      structures.push({ value: 'fiveLayerEB', label: '五层EB', price: selectedBoxMaterial.fiveLayerEB });
    }
    if (selectedBoxMaterial.sevenLayerEBA && selectedBoxMaterial.sevenLayerEBA > 0) {
      structures.push({ value: 'sevenLayerEBA', label: '七层EBA', price: selectedBoxMaterial.sevenLayerEBA });
    }

    return structures;
  };

  // 获取结构价格
  const getStructurePrice = (selectedBoxMaterial: BoxMaterial, structure: string): number => {
    switch (structure) {
      case 'threeLayerBE': return selectedBoxMaterial.threeLayerBE || 0;
      case 'threeLayerAC': return selectedBoxMaterial.threeLayerAC || 0;
      case 'fiveLayerABBC': return selectedBoxMaterial.fiveLayerABBC || 0;
      case 'fiveLayerEB': return selectedBoxMaterial.fiveLayerEB || 0;
      case 'sevenLayerEBA': return selectedBoxMaterial.sevenLayerEBA || 0;
      default: return 0;
    }
  };

  // 生成规格选项
  const generateSpecOptions = (material: Paper | SpecialPaper, category: 'paper' | 'specialPaper' | 'greyBoard' | 'corrugated'): MaterialSpecOption[] => {
    const options: MaterialSpecOption[] = [];

    // 获取材料尺寸配置，如果没有则使用默认值
    const sizes = materialSizes || {
      regularLength: 1092,
      regularWidth: 787,
      largeLength: 1194,
      largeWidth: 889,
      specialLength: 787,
      specialWidth: 1092
    };

    if (category === 'paper') {
      const paper = material as Paper;

      // 正度规格
      if (paper.regularPrice && paper.regularPrice > 0) {
        options.push({
          value: 'regular',
          label: `正度 (${sizes.regularWidth}×${sizes.regularLength}mm)`,
          size: { width: sizes.regularWidth, height: sizes.regularLength },
          available: true,
          price: paper.regularPrice
        });
      }

      // 大度规格
      if (paper.largePrice && paper.largePrice > 0) {
        options.push({
          value: 'large',
          label: `大度 (${sizes.largeWidth}×${sizes.largeLength}mm)`,
          size: { width: sizes.largeWidth, height: sizes.largeLength },
          available: true,
          price: paper.largePrice
        });
      }
    } else {
      const specialPaper = material as SpecialPaper;

      // 正度规格
      if (specialPaper.isRegular) {
        options.push({
          value: 'regular',
          label: `正度 (${sizes.regularWidth}×${sizes.regularLength}mm)`,
          size: { width: sizes.regularWidth, height: sizes.regularLength },
          available: true,
          price: specialPaper.price
        });
      }

      // 大度规格
      if (specialPaper.isLarge) {
        options.push({
          value: 'large',
          label: `大度 (${sizes.largeWidth}×${sizes.largeLength}mm)`,
          size: { width: sizes.largeWidth, height: sizes.largeLength },
          available: true,
          price: specialPaper.price
        });
      }

      // 特规尺寸
      if (specialPaper.isSpecial && specialPaper.size1 && specialPaper.size2) {
        options.push({
          value: 'special',
          label: `特规 (${specialPaper.size1}×${specialPaper.size2}mm)`,
          size: { width: specialPaper.size1, height: specialPaper.size2 },
          available: true,
          price: specialPaper.price
        });
      }
    }

    return options;
  };

  // 生成灰板纸规格选项
  const generateGreyBoardSpecOptions = (greyBoard: GreyBoard): MaterialSpecOption[] => {
    const options: MaterialSpecOption[] = [];

    // 获取材料尺寸配置，如果没有则使用默认值
    const sizes = materialSizes || {
      regularLength: 1092,
      regularWidth: 787,
      largeLength: 1194,
      largeWidth: 889,
      specialLength: 787,
      specialWidth: 1092
    };

    // 正度规格
    if (greyBoard.isRegular) {
      options.push({
        value: 'regular',
        label: `正度 (${sizes.regularWidth}×${sizes.regularLength})`,
        size: { width: sizes.regularWidth, height: sizes.regularLength },
        available: true,
        price: greyBoard.price
      });
    }

    // 大度规格
    if (greyBoard.isLarge) {
      options.push({
        value: 'large',
        label: `大度 (${sizes.largeWidth}×${sizes.largeLength})`,
        size: { width: sizes.largeWidth, height: sizes.largeLength },
        available: true,
        price: greyBoard.price
      });
    }

    // 现货尺寸
    if (greyBoard.isStockSize && greyBoard.stockLength && greyBoard.stockWidth) {
      options.push({
        value: 'stock',
        label: `现货尺寸 (${greyBoard.stockWidth}×${greyBoard.stockLength})`,
        size: { width: greyBoard.stockWidth, height: greyBoard.stockLength },
        available: true,
        price: greyBoard.price
      });
    }

    return options;
  };

  // 计算最优拼版尺寸（最小浪费）
  const calculateOptimalImpositionSize = (parts: any[]) => {
    if (parts.length === 0) return { width: 0, length: 0, arrangement: '', efficiency: 0 };
    if (parts.length === 1) {
      return {
        width: parts[0].width,
        length: parts[0].length,
        arrangement: '单个部件',
        efficiency: 100
      };
    }

    // 尝试不同的排列方式
    const arrangements = [];

    // 1. 横向排列（所有部件水平排列）
    const horizontalWidth = parts.reduce((sum, p) => sum + p.width, 0);
    const horizontalLength = Math.max(...parts.map(p => p.length));
    const horizontalArea = horizontalWidth * horizontalLength;
    arrangements.push({
      width: horizontalWidth,
      length: horizontalLength,
      area: horizontalArea,
      arrangement: '横向排列',
      description: parts.map(p => `${p.part.name}(${p.width.toFixed(1)}×${p.length.toFixed(1)})`).join(' + ')
    });

    // 2. 纵向排列（所有部件垂直排列）
    const verticalWidth = Math.max(...parts.map(p => p.width));
    const verticalLength = parts.reduce((sum, p) => sum + p.length, 0);
    const verticalArea = verticalWidth * verticalLength;
    arrangements.push({
      width: verticalWidth,
      length: verticalLength,
      area: verticalArea,
      arrangement: '纵向排列',
      description: parts.map(p => `${p.part.name}(${p.width.toFixed(1)}×${p.length.toFixed(1)})`).join(' | ')
    });

    // 3. 如果有多个部件，尝试混合排列（2x2, 2x1等）
    if (parts.length >= 3) {
      // 尝试2x2排列（前两个横向，后面的纵向）
      const firstRowWidth = parts[0].width + parts[1].width;
      const firstRowLength = Math.max(parts[0].length, parts[1].length);
      const remainingParts = parts.slice(2);
      const remainingWidth = Math.max(...remainingParts.map(p => p.width));
      const remainingLength = remainingParts.reduce((sum, p) => sum + p.length, 0);

      const mixedWidth = Math.max(firstRowWidth, remainingWidth);
      const mixedLength = firstRowLength + remainingLength;
      const mixedArea = mixedWidth * mixedLength;

      arrangements.push({
        width: mixedWidth,
        length: mixedLength,
        area: mixedArea,
        arrangement: '混合排列',
        description: `第一行: ${parts[0].part.name}+${parts[1].part.name}, 其他纵向排列`
      });
    }

    // 4. 尝试旋转部件的排列（如果部件可以旋转）
    const rotatedArrangements: any[] = [];
    parts.forEach((part, index) => {
      if (part.canRotate) {
        const rotatedParts = [...parts];
        rotatedParts[index] = {
          ...part,
          width: part.length,
          length: part.width,
          isRotated: true
        };

        // 重新计算横向排列
        const rotatedHorizontalWidth = rotatedParts.reduce((sum, p) => sum + p.width, 0);
        const rotatedHorizontalLength = Math.max(...rotatedParts.map(p => p.length));
        const rotatedHorizontalArea = rotatedHorizontalWidth * rotatedHorizontalLength;

        rotatedArrangements.push({
          width: rotatedHorizontalWidth,
          length: rotatedHorizontalLength,
          area: rotatedHorizontalArea,
          arrangement: `横向排列(${part.part.name}旋转)`,
          description: rotatedParts.map(p => `${p.part.name}(${p.width.toFixed(1)}×${p.length.toFixed(1)}${p.isRotated ? '旋转' : ''})`).join(' + ')
        });
      }
    });

    // 合并所有排列方案
    const allArrangements = [...arrangements, ...rotatedArrangements];

    // 选择面积最小的方案
    const optimal = allArrangements.reduce((best, current) =>
      current.area < best.area ? current : best
    );

    // 计算效率（部件总面积 / 拼版面积）
    const totalPartArea = parts.reduce((sum, p) => sum + p.area, 0);
    const efficiency = (totalPartArea / optimal.area) * 100;

    return {
      width: optimal.width,
      length: optimal.length,
      area: optimal.area,
      arrangement: optimal.arrangement,
      description: optimal.description,
      efficiency: efficiency,
      wasteArea: optimal.area - totalPartArea
    };
  };

  // 获取部件组的详细尺寸信息
  const getPartGroupDimensions = (group: PartGroup) => {
    if (group.parts.length === 1) {
      // 单个部件：显示实际计算的尺寸
      const part = group.parts[0];
      return {
        description: `部件尺寸: ${part.width.toFixed(1)}×${part.length.toFixed(1)}mm`,
        details: `面积: ${part.area.toFixed(0)}mm²`,
        isCalculated: true
      };
    } else {
      // 合并部件：计算最优拼版尺寸
      const optimal = calculateOptimalImpositionSize(group.parts);
      const totalArea = group.parts.reduce((sum, p) => sum + p.area, 0);

      return {
        description: `合并尺寸: ${optimal.width.toFixed(1)}×${optimal.length.toFixed(1)}mm`,
        details: `${optimal.arrangement} | 效率: ${optimal.efficiency.toFixed(1)}% | 浪费: ${(optimal.wasteArea || 0).toFixed(0)}mm²`,
        isCalculated: true,
        partDetails: optimal.description,
        totalPartArea: totalArea,
        impositionArea: optimal.area,
        efficiency: optimal.efficiency
      };
    }
  };

  return (
    <Card
      title={
        <Space>
          <ExperimentOutlined />
          <span>选择部件材料</span>
        </Space>
      }

      style={{ marginBottom: 16 }}
    >
      <Spin spinning={loading}>
        {/* 材料类型分析结果 */}
        <div style={{ marginBottom: 16 }}>
          <Text strong>材料类型分析：</Text>
          <div style={{ marginTop: 8 }}>
            {materialAnalysis.canSelectFacePaper && (
              <Tag color="blue">需要面纸</Tag>
            )}
            {materialAnalysis.canSelectGreyBoard && (
              <Tag color="green">需要灰板纸</Tag>
            )}
          </div>
        </div>

        {/* 面纸部件组材料选择 */}
        {facePartGroups.length > 0 && (
          <div style={{ marginBottom: 24 }}>
            <Text strong>面纸部件材料配置：</Text>
            {facePartGroups.map((group, index) => (
              <Card
                key={group.id}
                style={{ marginTop: 12 }}
                title={
                  <Space>
                    <Tag color="blue">面纸组 {index + 1}</Tag>
                    <span>{group.name}</span>
                    {showValidation && (() => {
                      const validation = getPartGroupValidationStatus(group.id, 'face');
                      return validation.isValid ? (
                        <Tag color="green">已配置</Tag>
                      ) : (
                        <Tag color="red" icon={<ExclamationCircleOutlined />}>
                          未完成
                        </Tag>
                      );
                    })()}
                  </Space>
                }
              >
                {/* 第一步：选择品类 */}
                <Row gutter={[16, 8]}>
                  <Col span={6}>
                    <Form.Item
                      label="选择品类"
                      layout="vertical"
                      required
                      validateStatus={showValidation && !partMaterialConfigs[group.id]?.materialCategory ? 'error' : undefined}
                      help={showValidation && !partMaterialConfigs[group.id]?.materialCategory ? '请选择材料品类' : ''}
                    >
                      <Select
                        placeholder="请选择品类"
                        value={partMaterialConfigs[group.id]?.materialCategory}
                        onChange={(value) => handleMaterialCategoryChange(group.id, value)}
                      >
                        <Option value="paper">纸类材料</Option>
                        <Option value="specialPaper">特种纸材料</Option>
                      </Select>
                    </Form.Item>
                  </Col>

                  {/* 第二步：选择具体材料 */}
                  <Col span={6}>
                    <Form.Item
                      label="选择材料"
                      layout="vertical"
                      required
                      validateStatus={showValidation && !partMaterialConfigs[group.id]?.materialId ? 'error' : undefined}
                      help={showValidation && !partMaterialConfigs[group.id]?.materialId ? '请选择具体材料' : ''}
                    >
                      <Select
                        placeholder="请选择材料"
                        value={partMaterialConfigs[group.id]?.materialId}
                        onChange={(value) => handleMaterialChange(group.id, value)}
                        disabled={!partMaterialConfigs[group.id]?.materialCategory}
                        showSearch
                        optionFilterProp="children"
                      >
                        {partMaterialConfigs[group.id]?.materialCategory === 'paper' && (() => {
                          perfLog.debug('渲染纸类材料选项:', {
                            groupId: group.id,
                            papersCount: papers.length,
                            papers: papers.map(p => ({ id: p.id, name: p.name }))
                          });
                          return papers.map(paper => (
                            <Option key={`paper-${paper.id}`} value={paper.id}>
                              {paper.name}
                            </Option>
                          ));
                        })()}
                        {partMaterialConfigs[group.id]?.materialCategory === 'specialPaper' && (() => {
                          perfLog.debug('渲染特种纸材料选项:', {
                            groupId: group.id,
                            specialPapersCount: specialPapers.length,
                            specialPapers: specialPapers.map(p => ({ id: p.id, name: p.name }))
                          });
                          return specialPapers.map(paper => (
                            <Option key={`special-${paper.id}`} value={paper.id}>
                              {paper.name}
                            </Option>
                          ));
                        })()}
                      </Select>
                    </Form.Item>
                  </Col>

                  {/* 第三步：选择规格 */}
                  <Col span={6}>
                    <Form.Item
                      label="选择规格"
                      layout="vertical"
                      required
                      validateStatus={showValidation && !partMaterialConfigs[group.id]?.materialSpec ? 'error' : undefined}
                      help={showValidation && !partMaterialConfigs[group.id]?.materialSpec ? '请选择材料规格' : ''}
                    >
                      <Select
                        placeholder="请选择规格"
                        value={partMaterialConfigs[group.id]?.materialSpec}
                        onChange={(value) => handleSpecChange(group.id, value)}
                        disabled={!partMaterialConfigs[group.id]?.materialId}
                      >
                        {partMaterialConfigs[group.id]?.specOptions?.map(spec => (
                          <Option key={spec.value} value={spec.value} disabled={!spec.available}>
                            {spec.label}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>

                  {/* 选择打印机 */}
                  <Col span={6}>
                    <Form.Item
                      label="选择打印机"
                      layout="vertical"
                      required
                      validateStatus={showValidation && !partMaterialConfigs[group.id]?.printingMachineId ? 'error' : undefined}
                      help={showValidation && !partMaterialConfigs[group.id]?.printingMachineId ? '面纸必须选择打印机' : ''}
                    >
                      <Select
                        placeholder="请选择打印机"
                        value={partMaterialConfigs[group.id]?.printingMachineId}
                        onChange={(value) => handlePrintingMachineChange(group.id, value)}
                      >
                        {printingMachines.map(machine => (
                          <Option key={machine.value} value={machine.value}>
                            {machine.label}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>

                {/* 第四步：显示部件信息和材料信息 */}
                <Row gutter={[16, 8]}>
                  <Col span={24}>
                    {(() => {
                      const dimensions = getPartGroupDimensions(group);
                      return (
                        <div>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            部件数: {group.parts.length} | {dimensions.description}
                          </Text>
                          <br />
                          <Text type="secondary" style={{ fontSize: '11px', color: '#999' }}>
                            {dimensions.details}
                          </Text>
                          {dimensions.partDetails && (
                            <>
                              <br />
                              <Text type="secondary" style={{ fontSize: '11px', color: '#999' }}>
                                {dimensions.partDetails}
                              </Text>
                            </>
                          )}
                          {partMaterialConfigs[group.id]?.materialSize && (
                            <>
                              <br />
                              <Text style={{ fontSize: '12px', color: '#1890ff', fontWeight: 'bold' }}>
                                材料尺寸: {partMaterialConfigs[group.id]?.materialSize?.width}×{partMaterialConfigs[group.id]?.materialSize?.height}mm
                              </Text>
                            </>
                          )}
                        </div>
                      );
                    })()}
                  </Col>
                </Row>
              </Card>
            ))}
          </div>
        )}

        {/* 灰板纸部件组材料选择 */}
        {greyPartGroups.length > 0 && (
          <div style={{ marginBottom: 24 }}>
            <Text strong>灰板纸部件材料配置：</Text>
            {greyPartGroups.map((group, index) => (
              <Card
                key={group.id}
                style={{ marginTop: 12 }}
                title={
                  <Space>
                    <Tag color="green">灰板纸组 {index + 1}</Tag>
                    <span>{group.name}</span>
                    {showValidation && (() => {
                      const validation = getPartGroupValidationStatus(group.id, 'grey');
                      return validation.isValid ? (
                        <Tag color="green">已配置</Tag>
                      ) : (
                        <Tag color="red" icon={<ExclamationCircleOutlined />}>
                          未完成
                        </Tag>
                      );
                    })()}
                  </Space>
                }
              >
                <Row gutter={[16, 16]}>
                  {/* 第一步：选择品类 */}
                  <Col span={6}>
                    <Form.Item
                      label="选择品类"
                      layout="vertical"
                      required
                      validateStatus={showValidation && !partMaterialConfigs[group.id]?.materialCategory ? 'error' : undefined}
                      help={showValidation && !partMaterialConfigs[group.id]?.materialCategory ? '请选择材料品类' : ''}
                    >
                      <Select
                        placeholder="请选择品类"
                        value={partMaterialConfigs[group.id]?.materialCategory}
                        onChange={(value) => handleMaterialCategoryChange(group.id, value)}
                      >
                        <Option value="greyBoard">灰板纸</Option>
                        <Option value="corrugated">瓦楞</Option>
                      </Select>
                    </Form.Item>
                  </Col>

                  {/* 第二步：选择具体材料 */}
                  {partMaterialConfigs[group.id]?.materialCategory === 'greyBoard' && (
                    <>
                      <Col span={6}>
                        <Form.Item
                          label="选择材料"
                          layout="vertical"
                          required
                          validateStatus={showValidation && !partMaterialConfigs[group.id]?.materialId ? 'error' : undefined}
                          help={showValidation && !partMaterialConfigs[group.id]?.materialId ? '请选择灰板纸材料' : ''}
                        >
                          <Select
                            placeholder="请选择灰板纸材料"
                            value={partMaterialConfigs[group.id]?.materialId}
                            onChange={(value) => handleGreyBoardChange(group.id, value)}
                            showSearch
                            optionFilterProp="children"
                          >
                            {(() => {
                              perfLog.debug('渲染灰板纸材料选项:', {
                                groupId: group.id,
                                greyBoardsCount: greyBoards.length,
                                greyBoards: greyBoards.map(g => ({ id: g.id, name: g.name }))
                              });
                              return greyBoards.map(greyBoard => (
                                <Option key={greyBoard.id} value={greyBoard.id}>
                                  {greyBoard.name}
                                </Option>
                              ));
                            })()}
                          </Select>
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item
                          label="选择规格"
                          layout="vertical"
                          required
                          validateStatus={showValidation && !partMaterialConfigs[group.id]?.materialSpec ? 'error' : undefined}
                          help={showValidation && !partMaterialConfigs[group.id]?.materialSpec ? '请选择灰板纸规格' : ''}
                        >
                          <Select
                            placeholder="请选择规格"
                            value={partMaterialConfigs[group.id]?.materialSpec}
                            onChange={(value) => handleGreyBoardSpecChange(group.id, value)}
                            disabled={!partMaterialConfigs[group.id]?.materialId}
                          >
                            {partMaterialConfigs[group.id]?.specOptions?.map(spec => (
                              <Option key={spec.value} value={spec.value} disabled={!spec.available}>
                                {spec.label}
                              </Option>
                            ))}
                          </Select>
                        </Form.Item>
                      </Col>
                    </>
                  )}

                  {/* 瓦楞材料选择 */}
                  {partMaterialConfigs[group.id]?.materialCategory === 'corrugated' && (
                    <>
                      {/* 面纸选择 */}
                      <Col span={6}>
                        <Form.Item
                          label="选择面纸"
                          layout="vertical"
                          required
                          validateStatus={showValidation && !partMaterialConfigs[group.id]?.facePaper ? 'error' : undefined}
                          help={showValidation && !partMaterialConfigs[group.id]?.facePaper ? '请选择面纸' : ''}
                        >
                          <Select
                            placeholder="请选择面纸"
                            value={partMaterialConfigs[group.id]?.facePaper}
                            onChange={(value) => handleFacePaperChange(group.id, value)}
                          >
                            {getFacePaperOptions(boxMaterials).map(facePaper => (
                              <Option key={facePaper} value={facePaper}>
                                {facePaper}
                              </Option>
                            ))}
                          </Select>
                        </Form.Item>
                      </Col>

                      {/* 里纸选择 */}
                      {partMaterialConfigs[group.id]?.facePaper && (
                        <Col span={6}>
                          <Form.Item
                            label="选择里纸"
                            layout="vertical"
                            required
                            validateStatus={showValidation && !partMaterialConfigs[group.id]?.linerPaper ? 'error' : undefined}
                            help={showValidation && !partMaterialConfigs[group.id]?.linerPaper ? '请选择里纸' : ''}
                          >
                            <Select
                              placeholder="请选择里纸"
                              value={partMaterialConfigs[group.id]?.linerPaper}
                              onChange={(value) => handleLinerPaperChange(group.id, value)}
                            >
                              {(() => {
                                // 根据选择的面纸，从所有纸箱材料中找到匹配的里纸选项
                                const matchingMaterials = boxMaterials.filter(m => m.facePaper === partMaterialConfigs[group.id]?.facePaper);
                                const linerOptions = [...new Set(matchingMaterials.map(m => m.linerPaper))].filter(Boolean);
                                return linerOptions.map(linerPaper => (
                                  <Option key={linerPaper} value={linerPaper}>
                                    {linerPaper}
                                  </Option>
                                ));
                              })()}
                            </Select>
                          </Form.Item>
                        </Col>
                      )}

                      {/* 结构选择 */}
                      {partMaterialConfigs[group.id]?.linerPaper && (
                        <Col span={6}>
                          <Form.Item
                            label="选择结构"
                            layout="vertical"
                            required
                            validateStatus={showValidation && !partMaterialConfigs[group.id]?.structure ? 'error' : undefined}
                            help={showValidation && !partMaterialConfigs[group.id]?.structure ? '请选择结构' : ''}
                          >
                            <Select
                              placeholder="请选择结构"
                              value={partMaterialConfigs[group.id]?.structure}
                              onChange={(value) => handleStructureChange(group.id, value)}
                            >
                              {(() => {
                                // 根据选择的面纸和里纸，找到匹配的纸箱材料并获取结构选项
                                const matchingMaterial = boxMaterials.find(m =>
                                  m.facePaper === partMaterialConfigs[group.id]?.facePaper &&
                                  m.linerPaper === partMaterialConfigs[group.id]?.linerPaper
                                );
                                if (matchingMaterial) {
                                  const structureOptions = getStructureOptions(matchingMaterial);
                                  return structureOptions.map(structure => (
                                    <Option key={structure.value} value={structure.value}>
                                      {structure.label} (¥{structure.price}/平方米)
                                    </Option>
                                  ));
                                }
                                return null;
                              })()}
                            </Select>
                          </Form.Item>
                        </Col>
                      )}
                    </>
                  )}
                </Row>
                <Row gutter={[16, 8]}>
                  <Col span={24}>
                    {(() => {
                      const dimensions = getPartGroupDimensions(group);
                      return (
                        <div>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            部件数: {group.parts.length} | {dimensions.description}
                          </Text>
                          <br />
                          <Text type="secondary" style={{ fontSize: '11px', color: '#999' }}>
                            {dimensions.details}
                          </Text>
                          {dimensions.partDetails && (
                            <>
                              <br />
                              <Text type="secondary" style={{ fontSize: '11px', color: '#999' }}>
                                {dimensions.partDetails}
                              </Text>
                            </>
                          )}
                          {partMaterialConfigs[group.id]?.materialSize && (
                            <>
                              <br />
                              <Text style={{ fontSize: '12px', color: '#1890ff', fontWeight: 'bold' }}>
                                材料尺寸: {partMaterialConfigs[group.id]?.materialSize?.width}×{partMaterialConfigs[group.id]?.materialSize?.height}mm
                              </Text>
                            </>
                          )}
                        </div>
                      );
                    })()}
                  </Col>
                </Row>
              </Card>
            ))}
          </div>
        )}



        {/* 材料配置验证状态 */}
        {showValidation && (facePartGroups.length > 0 || greyPartGroups.length > 0) && (() => {
          const allErrors: string[] = [];

          // 收集所有验证错误
          facePartGroups.forEach(group => {
            const validation = getPartGroupValidationStatus(group.id, 'face');
            if (!validation.isValid) {
              allErrors.push(`面纸组"${group.name}": ${validation.errors.join('、')}`);
            }
          });

          greyPartGroups.forEach(group => {
            const validation = getPartGroupValidationStatus(group.id, 'grey');
            if (!validation.isValid) {
              allErrors.push(`灰板纸组"${group.name}": ${validation.errors.join('、')}`);
            }
          });

          if (allErrors.length > 0) {
            return (
              <Alert
                message="材料配置未完成"
                description={
                  <div>
                    <div style={{ marginBottom: 8 }}>请完成以下配置：</div>
                    <ul style={{ margin: 0, paddingLeft: 20 }}>
                      {allErrors.map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </div>
                }
                type="warning"
                showIcon
                style={{ marginTop: 16 }}
              />
            );
          }

          return (
            <Alert
              message="材料配置完成"
              description="所有部件组的材料配置已完成，可以进行下一步操作。"
              type="success"
              showIcon
              style={{ marginTop: 16 }}
            />
          );
        })()}

        {/* 无可选材料提示 */}
        {!materialAnalysis.canSelectFacePaper && !materialAnalysis.canSelectGreyBoard && (
          <Alert
            message="无需选择材料"
            description="当前盒型配置无需选择材料。"
            type="info"
            showIcon
          />
        )}
      </Spin>
    </Card>
  );
};

// 使用 React.memo 优化组件，避免不必要的重新渲染
export const MaterialSelectionControl = React.memo(MaterialSelectionControlComponent, (prevProps, nextProps) => {
  // 自定义比较函数，返回 true 表示 props 相同（不重新渲染），返回 false 表示 props 不同（需要重新渲染）
  const isEqual = (
    prevProps.materialAnalysis.canSelectFacePaper === nextProps.materialAnalysis.canSelectFacePaper &&
    prevProps.materialAnalysis.canSelectGreyBoard === nextProps.materialAnalysis.canSelectGreyBoard &&
    prevProps.facePartGroups.length === nextProps.facePartGroups.length &&
    prevProps.greyPartGroups.length === nextProps.greyPartGroups.length &&
    JSON.stringify(prevProps.facePartGroups) === JSON.stringify(nextProps.facePartGroups) &&
    JSON.stringify(prevProps.greyPartGroups) === JSON.stringify(nextProps.greyPartGroups) &&
    // 关键修复：添加对materialData的比较
    JSON.stringify(prevProps.materialData) === JSON.stringify(nextProps.materialData) &&
    // 添加对initialPartMaterialConfigs的比较
    JSON.stringify(prevProps.initialPartMaterialConfigs) === JSON.stringify(nextProps.initialPartMaterialConfigs)
  );

  perfLog.debug('MaterialSelectionControl React.memo 比较结果:', {
    isEqual,
    prevCanSelectFacePaper: prevProps.materialAnalysis.canSelectFacePaper,
    nextCanSelectFacePaper: nextProps.materialAnalysis.canSelectFacePaper,
    prevCanSelectGreyBoard: prevProps.materialAnalysis.canSelectGreyBoard,
    nextCanSelectGreyBoard: nextProps.materialAnalysis.canSelectGreyBoard,
    prevFaceGroupsLength: prevProps.facePartGroups.length,
    nextFaceGroupsLength: nextProps.facePartGroups.length,
    prevGreyGroupsLength: prevProps.greyPartGroups.length,
    nextGreyGroupsLength: nextProps.greyPartGroups.length,
    // 添加materialData的比较信息
    prevMaterialDataPapers: prevProps.materialData?.papers?.length || 0,
    nextMaterialDataPapers: nextProps.materialData?.papers?.length || 0,
    prevMaterialDataSpecialPapers: prevProps.materialData?.specialPapers?.length || 0,
    nextMaterialDataSpecialPapers: nextProps.materialData?.specialPapers?.length || 0,
    materialDataChanged: JSON.stringify(prevProps.materialData) !== JSON.stringify(nextProps.materialData),
    initialConfigsChanged: JSON.stringify(prevProps.initialPartMaterialConfigs) !== JSON.stringify(nextProps.initialPartMaterialConfigs)
  });

  return isEqual;
});

export default MaterialSelectionControl;
