import { resultApi } from '@/lib/utils/request';
import {
  LoginRequest,
  LoginResponse,
  CurrentUser,
  ChangePasswordRequest
} from '@/types/user';
import { Result } from '@/types/common';

/**
 * 用户登录
 */
export async function login(data: LoginRequest): Promise<Result<LoginResponse>> {
  return resultApi.post<LoginResponse>('/api/v1/auth/login', data)
}

/**
 * 用户登出
 */
export async function logout(): Promise<Result<null>> {
  return resultApi.post<null>('/api/v1/auth/logout');
}

/**
 * 获取当前用户信息
 */
export async function getCurrentUser(showError = true): Promise<Result<CurrentUser>> {
  const response = await resultApi.post<CurrentUser>('/api/v1/auth/profile', null, {
    showError
  });
  return response;
}

/**
 * 修改密码
 */
export async function changePassword(data: ChangePasswordRequest): Promise<Result<null>> {
  return await resultApi.post<null>('/api/v1/auth/change-password', data);
}

/**
 * 检查登录状态
 */
export async function checkAuthStatus(): Promise<boolean> {
  try {
    const result = await getCurrentUser(false);
    return result.success;
  } catch (error) {
    return false;
  }
}

/**
 * 清除本地认证信息
 */
export function clearAuthInfo(): void {
  // 清除localStorage中的用户信息（如果有的话）
  if (typeof window !== 'undefined') {
    localStorage.removeItem('user');
    localStorage.removeItem('token');
  }
}

/**
 * 设置本地认证信息
 */
export function setAuthInfo(user: CurrentUser, token?: string): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem('user', JSON.stringify(user));
    if (token) {
      localStorage.setItem('token', token);
    }
  }
}

/**
 * 获取本地用户信息
 */
export function getLocalUser(): CurrentUser | null {
  if (typeof window !== 'undefined') {
    try {
      const userStr = localStorage.getItem('user');
      return userStr ? JSON.parse(userStr) : null;
    } catch (error) {
      console.error('解析本地用户信息失败:', error);
      return null;
    }
  }
  return null;
}

/**
 * 获取本地Token
 */
export function getLocalToken(): string | null {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('token');
  }
  return null;
}
