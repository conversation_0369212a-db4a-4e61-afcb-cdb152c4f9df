import { NextRequest, NextResponse } from 'next/server';
import { verifyRequestToken, canAccessAdmin, getClientIP } from './jwt';
import { UserRole, JWTPayload } from '@/types/user';
import { errorResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

// 认证中间件类型
export interface AuthMiddlewareOptions {
  requiredRoles?: UserRole[];
  requireAuth?: boolean;
  adminOnly?: boolean;
}

// 扩展NextRequest类型，添加用户信息
export interface AuthenticatedRequest extends NextRequest {
  user?: JWTPayload;
  clientIP?: string;
}

// 认证中间件
export function withAuth(
  handler: (request: AuthenticatedRequest) => Promise<NextResponse>,
  options: AuthMiddlewareOptions = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const { requiredRoles, requireAuth = true, adminOnly = false } = options;
    
    // 获取客户端IP
    const clientIP = getClientIP(request);
    
    // 创建扩展的请求对象
    const authRequest = request as AuthenticatedRequest;
    authRequest.clientIP = clientIP;

    // 如果不需要认证，直接执行处理函数
    if (!requireAuth) {
      return handler(authRequest);
    }

    // 验证Token
    const payload = verifyRequestToken(request);
    if (!payload) {
      return NextResponse.json(
        errorResponse(ErrorCode.UNAUTHORIZED, '未授权访问，请先登录'),
        { status: 401 }
      );
    }

    // 检查Token是否过期
    if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
      return NextResponse.json(
        errorResponse(ErrorCode.TOKEN_EXPIRED, 'Token已过期，请重新登录'),
        { status: 401 }
      );
    }

    // 添加用户信息到请求对象
    authRequest.user = payload;

    // 检查管理员权限
    if (adminOnly && !canAccessAdmin(payload.role)) {
      return NextResponse.json(
        errorResponse(ErrorCode.FORBIDDEN, '权限不足，需要管理员权限'),
        { status: 403 }
      );
    }

    // 检查特定角色权限
    if (requiredRoles && requiredRoles.length > 0) {
      if (!requiredRoles.includes(payload.role)) {
        return NextResponse.json(
          errorResponse(ErrorCode.FORBIDDEN, '权限不足，无法访问此资源'),
          { status: 403 }
        );
      }
    }

    // 执行处理函数
    return handler(authRequest);
  };
}

// 管理员权限中间件
export function withAdminAuth(
  handler: (request: AuthenticatedRequest) => Promise<NextResponse>
) {
  return withAuth(handler, { adminOnly: true });
}

// 内部用户权限中间件（管理员或内部用户）
export function withInternalAuth(
  handler: (request: AuthenticatedRequest) => Promise<NextResponse>
) {
  return withAuth(handler, { 
    requiredRoles: [UserRole.ADMIN, UserRole.INTERNAL_USER] 
  });
}

// 可选认证中间件（不强制要求登录）
export function withOptionalAuth(
  handler: (request: AuthenticatedRequest) => Promise<NextResponse>
) {
  return withAuth(handler, { requireAuth: false });
}

// 获取当前用户信息的辅助函数
export function getCurrentUser(request: AuthenticatedRequest): JWTPayload | null {
  return request.user || null;
}

// 检查当前用户是否有特定权限
export function hasRole(request: AuthenticatedRequest, roles: UserRole[]): boolean {
  const user = getCurrentUser(request);
  if (!user) return false;
  return roles.includes(user.role);
}

// 检查当前用户是否是管理员
export function isCurrentUserAdmin(request: AuthenticatedRequest): boolean {
  const user = getCurrentUser(request);
  return user?.role === UserRole.ADMIN;
}

// 检查当前用户是否是内部用户
export function isCurrentUserInternal(request: AuthenticatedRequest): boolean {
  const user = getCurrentUser(request);
  return user ? canAccessAdmin(user.role) : false;
}

// 权限检查装饰器函数
export function requirePermission(roles: UserRole[]) {
  return function(
    handler: (request: AuthenticatedRequest) => Promise<NextResponse>
  ) {
    return withAuth(handler, { requiredRoles: roles });
  };
}

// 错误处理辅助函数
export function createAuthError(code: ErrorCode, message: string, status: number = 401) {
  return NextResponse.json(
    errorResponse(code, message),
    { status }
  );
}

// 认证相关的错误响应
export const AUTH_ERRORS = {
  UNAUTHORIZED: () => createAuthError(ErrorCode.UNAUTHORIZED, '未授权访问，请先登录', 401),
  TOKEN_EXPIRED: () => createAuthError(ErrorCode.TOKEN_EXPIRED, 'Token已过期，请重新登录', 401),
  FORBIDDEN: () => createAuthError(ErrorCode.FORBIDDEN, '权限不足，无法访问此资源', 403),
  ADMIN_REQUIRED: () => createAuthError(ErrorCode.FORBIDDEN, '权限不足，需要管理员权限', 403),
  INTERNAL_REQUIRED: () => createAuthError(ErrorCode.FORBIDDEN, '权限不足，需要内部用户权限', 403),
} as const;
