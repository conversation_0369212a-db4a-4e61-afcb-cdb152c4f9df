'use client';

import React from 'react';
import { Layout, Menu, Button, Row, Col, Space, ConfigProvider, theme, Dropdown, Spin } from 'antd';
import { PhoneOutlined, UserOutlined, LogoutOutlined, DownOutlined } from '@ant-design/icons';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { logout } from '@/services/auth';
import { message } from 'antd';

const { Header: AntHeader } = Layout;

/**
 * 前台页面头部组件 - 仿照参考网站设计
 */
export default function Header() {
  const pathname = usePathname();
  const router = useRouter();
  const { user, loading, isAuthenticated, clearUser } = useAuth();

  // 处理用户登出
  const handleLogout = async () => {
    try {
      await logout();
      clearUser();
      message.success('退出登录成功');
      router.push('/');
    } catch (error) {
      console.error('登出失败:', error);
      message.error('退出登录失败');
    }
  };

  // 主导航菜单项
  const mainMenuItems = [
    {
      key: 'home',
      label: <Link href="/">首页</Link>,
    },
    {
      key: 'quote',
      label: <Link href="/quote">包装报价</Link>,
    },
    {
      key: 'user',
      label: <Link href="/user">用户中心</Link>,
    },
    {
      key: 'about',
      label: <Link href="/about">关于我们</Link>,
    },
    {
      key: 'news',
      label: <Link href="/news">新闻中心</Link>,
    },
  ];

  // 获取当前选中的菜单键
  const getSelectedKeys = () => {
    if (pathname === '/') return ['home'];
    if (pathname.startsWith('/quote')) return ['quote'];
    if (pathname.startsWith('/user')) return ['user'];
    if (pathname.startsWith('/about')) return ['about'];
    if (pathname.startsWith('/news')) return ['news'];
    return [];
  };

  return (
    <div style={{ borderBottom: '4px solid #C80000' }}>
      {/* 顶部信息栏 */}
      <div style={{ backgroundColor: '#393D49', borderBottom: '1px solid #e0e0e0', padding: '8px 0' }}>
        <Row justify="space-between" align="middle" style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 20px' }}>
          <Col>
            <Space>
              <PhoneOutlined style={{ color: 'white' }} />
              <span style={{ color: 'white', fontSize: '14px' }}>
                24小时热线：18638728164
              </span>
            </Space>
          </Col>
          <Col>
            <Space size="large">
              {loading ? (
                <Spin size="small" />
              ) : isAuthenticated && user ? (
                <Dropdown
                  menu={{
                    items: [
                      {
                        key: 'profile',
                        icon: <UserOutlined />,
                        label: <Link href="/user">个人中心</Link>,
                      },
                      {
                        type: 'divider',
                      },
                      {
                        key: 'logout',
                        icon: <LogoutOutlined />,
                        label: '退出登录',
                        onClick: handleLogout,
                      },
                    ],
                  }}
                  trigger={['hover']}
                >
                  <Button
                    type="link"
                    size="small"
                    style={{ padding: 0, height: 'auto', color: 'white' }}
                  >
                    欢迎！{user.name} <DownOutlined />
                  </Button>
                </Dropdown>
              ) : (
                <Link href="/login">
                  <Button type="link" size="small" style={{ padding: 0, height: 'auto', color: 'white' }}>
                    您好，请登录
                  </Button>
                </Link>
              )}
            </Space>
          </Col>
        </Row>
      </div>

      {/* 主导航栏 */}
      <AntHeader style={{ height: '114px', backgroundColor: '#fff', borderBottom: '1px solid #e0e0e0', padding: '0' }}>
        <Row style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 20px' }}>
          {/* Logo */}
          <Col>
            <Link href="/">
              <Button type="link" style={{ padding: 0, height: 'auto' }}>
                <img
                  src="/images/logo.png"
                  alt="艺创包装"
                  style={{ height: '100%', width: 'auto' }}
                />
                <div
                  style={{
                    display: 'none',
                    fontSize: '24px',
                    fontWeight: 'bold',
                    color: '#1890ff'
                  }}
                >
                  艺创包装
                </div>
              </Button>
            </Link>
          </Col>

          {/* 主导航菜单 */}
          <Col flex="auto" style={{ display: 'flex', height: '112px', justifyContent: 'flex-end', alignItems: 'flex-end' }}>
            <Menu
              mode="horizontal"
              selectedKeys={getSelectedKeys()}
              items={mainMenuItems}
              style={{
                fontSize: '18px'
              }}
            />
          </Col>
        </Row>
      </AntHeader>
    </div>
  );
}
