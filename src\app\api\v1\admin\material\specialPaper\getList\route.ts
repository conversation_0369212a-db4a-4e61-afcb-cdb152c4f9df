import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getSpecialPaperListSchema, GetSpecialPaperListParams } from '@/lib/validations/admin/specialPaper';
import { withValidation } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { paginatedResponse } from '@/lib/utils/apiResponse';
import { Prisma } from '@prisma/client';

const handler = withValidation<GetSpecialPaperListParams>(
  getSpecialPaperListSchema,
  async (request: AuthenticatedRequest, validatedQuery: GetSpecialPaperListParams) => {
    const { page = 1, pageSize = 10, keyword, category } = validatedQuery;
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const where: Prisma.SpecialPaperWhereInput = {
      isDel: false,
    };

    if (keyword) {
      where.name = {
        contains: keyword,
      };
    }

    if (category) {
      where.category = category;
    }

    // 查询总数和列表数据
    const [total, list] = await Promise.all([
      prisma.specialPaper.count({ where }),
      prisma.specialPaper.findMany({
        where,
        select: {
          id: true,
          name: true,
          category: true,
          thickness: true,
          weight: true,
          unit: true,
          price: true,
          isRegular: true,
          isLarge: true,
          isSpecial: true,
          size1: true,
          size2: true,
          remark: true,
          createdAt: true,
          updatedAt: true,
        },
        skip,
        take: pageSize,
        orderBy: { createdAt: 'desc' },
      })
    ]);

    return paginatedResponse(
      list,
      {
        page,
        pageSize,
        total,
      },
      '获取特种纸列表成功'
    );

  }
); 
export const POST = withInternalAuth(handler);