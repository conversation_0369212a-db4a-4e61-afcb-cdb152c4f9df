import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createSpecialPaperSchema, validateSpecialSize, CreateSpecialPaperParams } from '@/lib/validations/admin/specialPaper';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation<CreateSpecialPaperParams>(
  createSpecialPaperSchema,
  async (request: AuthenticatedRequest, validatedData: CreateSpecialPaperParams) => {
    // 特规尺寸条件验证
    const specialSizeValidation = validateSpecialSize(validatedData);
    assert(
      specialSizeValidation.success,
      ErrorCode.INVALID_PARAMETERS,
      specialSizeValidation.error || '特规尺寸验证失败'
    );

    // 检查名称是否已存在
    const existingSpecialPaper = await prisma.specialPaper.findFirst({
      where: {
        name: validatedData.name,
        isDel: false,
      },
    });

    assert(
      !existingSpecialPaper,
      ErrorCode.MATERIAL_NAME_EXISTS,
      '特种纸名称已存在'
    );

    // 创建特种纸
    const specialPaper = await prisma.specialPaper.create({
      data: {
        name: validatedData.name,
        price: validatedData.price,
        unit: validatedData.unit,
        weight: validatedData.weight,
        thickness: validatedData.thickness,
        isRegular: validatedData.isRegular,
        isLarge: validatedData.isLarge,
        isSpecial: validatedData.isSpecial,
        size1: validatedData.isSpecial ? validatedData.size1 : null,
        size2: validatedData.isSpecial ? validatedData.size2 : null,
        category: validatedData.category,
        remark: validatedData.remark || null,
        isDel: false,
      },
    });

    return successResponse(specialPaper, '创建特种纸成功');
  }
); 
export const POST = withInternalAuth(handler);