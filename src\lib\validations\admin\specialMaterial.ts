import { z } from 'zod';

// 基础字段验证规则
export const specialMaterialBaseSchema = z.object({
  name: z.string()
    .min(1, '名称不能为空')
    .max(100, '名称长度不能超过100'),
  price: z.number()
    .min(0, '价格必须大于等于0')
    .refine(val => val <= 1000000, '价格不能超过1000000'),
  unit: z.string()
    .min(1, '单位不能为空')
    .max(20, '单位长度不能超过20')
    .refine(val => ['元/平方', '元/吨', '元/张'].includes(val), '单位必须是：元/平方、元/吨、元/张'),
  thickness: z.number()
    .min(0, '厚度必须大于等于0')
    .refine(val => val <= 1000, '厚度不能超过1000'),
  density: z.number()
    .min(0, '密度必须大于等于0')
    .refine(val => val <= 1000, '密度不能超过1000'),
  isStockSize: z.boolean().default(false),
  stockLength: z.number()
    .positive('现货长度必须大于0')
    .optional()
    .nullable(),
  stockWidth: z.number()
    .positive('现货宽度必须大于0')
    .optional()
    .nullable(),
  category: z.string()
    .min(1, '材料品类不能为空')
    .max(50, '材料品类长度不能超过50'),
  remark: z.string()
    .max(1000, '备注长度不能超过1000')
    .optional()
    .nullable(),
});

// 创建特殊材料的验证schema
export const createSpecialMaterialSchema = specialMaterialBaseSchema;

// 更新特殊材料的验证schema
export const updateSpecialMaterialSchema = specialMaterialBaseSchema.extend({
  id: z.number().int().positive('ID必须是正整数'),
});

// 获取特殊材料详情的验证schema
export const getSpecialMaterialDetailSchema = z.object({
  id: z.string().transform(Number).pipe(
    z.number().int().positive('特殊材料ID必须是正整数')
  ),
});

// 获取特殊材料列表的验证schema
export const getSpecialMaterialListSchema = z.object({
  page: z.number().int().min(1, '页码必须大于0').default(1),
  pageSize: z.number().int().min(1, '每页条数必须大于0').max(100, '每页条数不能超过100').default(10),
  keyword: z.string().optional(),
  category: z.string().optional(),
});

// 删除特殊材料的验证schema
export const deleteSpecialMaterialSchema = z.object({
  id: z.string().transform(Number).pipe(
    z.number().int().positive('特殊材料ID必须是正整数')
  ),
});

// 现货尺寸条件验证函数
export const validateStockSize = (data: z.infer<typeof specialMaterialBaseSchema>) => {
  if (data.isStockSize && (!data.stockLength || !data.stockWidth)) {
    return {
      success: false,
      error: '按现货尺寸选项启用时，必须提供现货长度和宽度',
    };
  }
  return { success: true };
};

// 导出类型
export type SpecialMaterialBase = z.infer<typeof specialMaterialBaseSchema>;
export type CreateSpecialMaterialParams = z.infer<typeof createSpecialMaterialSchema>;
export type UpdateSpecialMaterialParams = z.infer<typeof updateSpecialMaterialSchema>;
export type GetSpecialMaterialDetailParams = z.infer<typeof getSpecialMaterialDetailSchema>;
export type GetSpecialMaterialListParams = z.infer<typeof getSpecialMaterialListSchema>;
export type DeleteSpecialMaterialParams = z.infer<typeof deleteSpecialMaterialSchema>;

// 查询参数类型（用于中间件）
export type SpecialMaterialQueryParams = GetSpecialMaterialListParams; 