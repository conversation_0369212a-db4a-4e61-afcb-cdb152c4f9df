import { z } from 'zod';
import { SILK_SCREEN_PROCESS_UNITS } from '@/types/craftSalary';

// 丝印工艺表单验证模式
export const silkScreenProcessFormSchema = z.object({
  name: z.string()
    .min(1, '请输入丝印工艺名称')
    .max(100, '名称长度不能超过100字符'),
  
  unitPrice: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '单价必须是大于等于0的数字'),
  
  unit: z.enum(SILK_SCREEN_PROCESS_UNITS, {
    required_error: '请选择计价单位',
    invalid_type_error: '无效的计价单位',
  }),
  
  basePrice: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '起步价必须是大于等于0的数字'),
  
  materialFee: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '材料费必须是大于等于0的数字'),
  
  remark: z.string()
    .max(500, '备注长度不能超过500字符')
    .optional()
    .or(z.literal(''))
});

// 丝印工艺创建请求验证模式
export const createSilkScreenProcessSchema = z.object({
  name: z.string().min(1).max(100),
  unitPrice: z.number().min(0),
  unit: z.enum(SILK_SCREEN_PROCESS_UNITS),
  basePrice: z.number().min(0),
  materialFee: z.number().min(0),
  remark: z.string().max(500).optional()
});

// 丝印工艺更新请求验证模式
export const updateSilkScreenProcessSchema = createSilkScreenProcessSchema.extend({
  id: z.number().int().positive()
});

// 丝印工艺列表查询参数验证模式
export const silkScreenProcessListParamsSchema = z.object({
  page: z.number().int().positive().optional(),
  pageSize: z.number().int().positive().max(100).optional(),
  search: z.string().optional(),
  unit: z.enum(SILK_SCREEN_PROCESS_UNITS).optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
});

// 丝印工艺删除请求验证模式
export const deleteSilkScreenProcessSchema = z.object({
  id: z.number().int().positive(),
});


// 表单数据转换函数
export function transformSilkScreenProcessFormData(formData: z.infer<typeof silkScreenProcessFormSchema>) {
  return {
    name: formData.name,
    unitPrice: Number(formData.unitPrice),
    unit: formData.unit,
    basePrice: Number(formData.basePrice),
    materialFee: Number(formData.materialFee),
    remark: formData.remark || undefined
  };
}

export type SilkScreenProcessFormData = z.infer<typeof silkScreenProcessFormSchema>;
export type CreateSilkScreenProcessData = z.infer<typeof createSilkScreenProcessSchema>;
export type UpdateSilkScreenProcessData = z.infer<typeof updateSilkScreenProcessSchema>;
export type DeleteSilkScreenProcessData = z.infer<typeof deleteSilkScreenProcessSchema>;
