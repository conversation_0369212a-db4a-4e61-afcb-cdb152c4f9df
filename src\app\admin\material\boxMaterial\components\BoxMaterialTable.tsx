'use client';

import React from 'react';
import { Table, Button, Space, Popconfirm, Tag } from 'antd';
import { EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { BoxMaterial } from '@/types/material';

interface BoxMaterialTableProps {
  data: BoxMaterial[];
  loading: boolean;
  total: number;
  current: number;
  pageSize: number;
  onTableChange: (pagination: any) => void;
  onEdit: (record: BoxMaterial) => void;
  onDelete: (id: number) => void;
}

const BoxMaterialTable: React.FC<BoxMaterialTableProps> = ({
  data,
  loading,
  total,
  current,
  pageSize,
  onTableChange,
  onEdit,
  onDelete,
}) => {
  const columns = [
    {
      title: '编号',
      dataIndex: 'code',
      key: 'code',
      width: 100,
      align: 'center' as const,
    },
    {
      title: '面纸',
      dataIndex: 'facePaper',
      key: 'facePaper',
      width: 100,
      align: 'center' as const,
    },
    {
      title: '里纸',
      dataIndex: 'linerPaper',
      key: 'linerPaper',
      width: 100,
      align: 'center' as const,
    },
    {
      title: '三层B/E',
      dataIndex: 'threeLayerBE',
      key: 'threeLayerBE',
      render: (value: number | null) => value ? value.toFixed(2) : '-',
      width: 80,
      align: 'center' as const,
    },
    {
      title: '三层A/C',
      dataIndex: 'threeLayerAC',
      key: 'threeLayerAC',
      render: (value: number | null) => value ? value.toFixed(2) : '-',
      width: 80,
      align: 'center' as const,
    },
    {
      title: '五层AB/BC',
      dataIndex: 'fiveLayerABBC',
      key: 'fiveLayerABBC',
      render: (value: number | null) => value ? value.toFixed(2) : '-',
      width: 80,
      align: 'center' as const,
    },
    {
      title: '五层EB',
      dataIndex: 'fiveLayerEB',
      key: 'fiveLayerEB',
      render: (value: number | null) => value ? value.toFixed(2) : '-',
      width: 80,
      align: 'center' as const,
    },
    {
      title: '七层EBA',
      dataIndex: 'sevenLayerEBA',
      key: 'sevenLayerEBA',
      render: (value: number | null) => value ? value.toFixed(2) : '-',
      width: 80,
      align: 'center' as const,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      ellipsis: true,
      width: 100,
      align: 'center' as const,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      align: 'center' as const,
      render: (_: any, record: BoxMaterial) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => onEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除吗？"
            onConfirm={() => onDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              danger 
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Table
      columns={columns}
      dataSource={data}
      rowKey="id"
      loading={loading}
      pagination={{
        current,
        pageSize,
        total,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => `共 ${total} 条`,
      }}
      onChange={onTableChange}
      bordered
      size="middle"
      scroll={{ x: 1200 }}
      locale={{ emptyText: '暂无数据' }}
    />
  );
};

export default BoxMaterialTable; 