/**
 * 盒型计算工具函数统一导出
 * 提供所有工具函数的统一入口
 */

// 规格和单位转换工具
export {
  getSpecDisplayName,
  getChineseUnit,
  getMaterialTypeDisplayName,
  isValidSpec,
  isValidUnit,
  isValidMaterialType,
  getSupportedSpecs,
  getSupportedUnits,
  getSupportedMaterialTypes,
  batchGetSpecDisplayNames,
  batchGetChineseUnits,
  batchGetMaterialTypeDisplayNames
} from './specificationUtils';

// 价格计算工具
export {
  calculateBasePriceWithMinimum,
  calculateTieredPrice,
  calculateAreaBasedPrice,
  calculateCompositePrice,
  formatCurrency,
  calculateMaterialWeight,
  calculateQuantityByUnit,
  validatePriceParams,
  safeNumber
} from './priceCalculationUtils';

export type {
  BasePriceResult,
  TieredPriceConfig,
  TieredPriceResult,
  CompositePriceResult,
  AreaDimensions
} from './priceCalculationUtils';

// 材料处理工具
export {
  getMaterialPriceKey,
  determineMaterialCategory,
  buildMaterialInfo,
  extractMaterialIdReferences,
  validateMaterialConfig as validateMaterialConfigUtil,
  isCorrugatedMaterial,
  getCorrugatedStructurePrice,
  groupByMaterial
} from './materialUtils';

export type {
  MaterialPriceInfo,
  MaterialInfo,
  MaterialIdReference
} from './materialUtils';

// 数据格式化工具
export {
  formatDimensions,
  formatArea,
  formatUtilizationRate,
  formatSpecification,
  formatQuantityWithUnit,
  formatImpositionInfo,
  formatMaterialNameWithSpec,
  formatProcessParameters,
  formatTimestamp,
  formatFileSize,
  safeFormatNumber
} from './formatUtils';

export type {
  Dimensions
} from './formatUtils';

// 验证工具
export {
  validateMaterialConfig,
  validateProcessParameters,
  validateDimensions,
  validatePriceParameters,
  validateEmail,
  validatePhone,
  validateRequired,
  batchValidate
} from './validationUtils';

export type {
  ValidationResult,
  DimensionValidationParams,
  PriceValidationParams
} from './validationUtils';

// 类型定义
export type {
  ProcessDataBase,
  PrintingProcessData,
  LaminatingProcessData,
  SilkScreenProcessData,
  HotStampingProcessData,
  TexturingProcessData,
  EmbossingProcessData,
  HydraulicProcessData,
  DieCuttingProcessData,
  DieCuttingPlateFeeData,
  ProcessCalculationParams,
  ProcessCalculationResult,
  MaterialPriceCalculationParams,
  MaterialPriceCalculationResult,
  FormulaCalculationScope,
  ImpositionDetail,
  ExtendedMaterialCostDetail,
  ProcessCostDetail,
  AccessoryCostDetail,
  QuotationSummaryDetail
} from './typeDefinitions';

export {
  isValidProcessData,
  isValidMaterialPriceParams,
  isValidProcessCalculationParams
} from './typeDefinitions';

// ResizeObserver 管理器
export { default as resizeObserverManager, useResizeObserver } from './resizeObserverManager';

// 注意：commonUtils 对象已移除，请直接使用具体的函数导入
// 例如：import { getSpecDisplayName, formatCurrency } from '../util';
