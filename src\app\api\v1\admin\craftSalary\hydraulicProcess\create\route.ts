import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createHydraulicProcessSchema } from '@/lib/validations/admin/embossingProcess';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation(
  createHydraulicProcessSchema,
  async (request: AuthenticatedRequest, validatedData: any) => {
    const data = validatedData;

    // 检查名称是否重复
    const existingHydraulicProcess = await prisma.hydraulicProcess.findFirst({
      where: {
        name: data.name,
        isDel: false,
      },
    });

    assert(!existingHydraulicProcess, ErrorCode.DUPLICATE_ENTRY, '液压工艺名称已存在');

    // 创建液压工艺
    const hydraulicProcess = await prisma.hydraulicProcess.create({
      data: {
        name: data.name,
        price: data.price,
        unit: data.unit,
        basePrice: data.basePrice,
        salary: data.salary,
        salaryBasePrice: data.salaryBasePrice,
        remark: data.remark || null,
        isDel: false,
      },
    });

    return successResponse(
      hydraulicProcess,
      '创建液压工艺成功'
    );
  }
); 
export const POST = withInternalAuth(handler);