import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { deleteCustomFormulaSchema, DeleteCustomFormulaParams } from '@/lib/validations/admin/customFormula';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';


const handler = withValidation<DeleteCustomFormulaParams>(
  deleteCustomFormulaSchema,
  async (request: AuthenticatedRequest, validatedQuery: DeleteCustomFormulaParams) => {
    const data = validatedQuery;

    // 检查公式是否存在
    const existingFormula = await prisma.customFormula.findFirst({
      where: {
        id: data.id,
        isDel: false,
      },
    });

    assertExists(existingFormula, ErrorCode.FORMULA_NOT_FOUND, '自定义公式不存在');

    // 软删除公式
    const result = await prisma.customFormula.update({
      where: { id: data.id },
      data: {
        isDel: true,
      },
    });

    return successResponse(
      { id: result.id },
      '删除自定义公式成功'
    );
  }
); 
export const POST = withInternalAuth(handler);