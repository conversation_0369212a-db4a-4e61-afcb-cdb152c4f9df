import { z } from 'zod';

// 配件基础验证 schema
const accessoryBaseSchema = {
  name: z.string({
    required_error: '请输入配件名称',
    invalid_type_error: '配件名称必须是字符串',
  }).min(1, '配件名称不能为空').max(100, '配件名称不能超过100个字符'),
  
  price: z.number({
    required_error: '请输入价格',
    invalid_type_error: '价格必须是数字',
  }).min(0, '价格不能小于0'),
  
  initialPrice: z.number({
    required_error: '请输入起步价',
    invalid_type_error: '起步价必须是数字',
  }).min(0, '起步价不能小于0'),
  
  weight: z.number({
    required_error: '请输入重量',
    invalid_type_error: '重量必须是数字',
  }).min(0, '重量不能小于0'),
  
  unit: z.enum(['元/对', '元/米', '元/条', '元/平方'], {
    required_error: '请选择单位',
    invalid_type_error: '无效的单位类型',
  }),
  
  remark: z.string().max(1000, '备注不能超过1000个字符').optional(),
};

// 创建配件验证 schema
export const createAccessorySchema = z.object({
  ...accessoryBaseSchema,
});

// 更新配件验证 schema
export const updateAccessorySchema = z.object({
  id: z.string().transform(Number).pipe(
    z.number().positive('配件ID必须大于0')
  ),
  ...accessoryBaseSchema,
});

// 删除配件验证 schema
export const deleteAccessorySchema = z.object({
  id: z.string().transform(Number).pipe(
    z.number().positive('配件ID必须大于0')
  ),
});

// 查询参数验证 schema
export const accessoryQuerySchema = z.object({
  page: z.number().positive('页码必须大于0').optional(),
  pageSize: z.number().positive('每页条数必须大于0').optional(),
  keyword: z.string().optional(),
});

// 获取配件详情验证 schema
export const getAccessoryDetailSchema = z.object({
  id: z.string().transform(Number).pipe(
    z.number().positive('配件ID必须大于0')
  ),
});

// 类型定义
export type CreateAccessorySchema = z.infer<typeof createAccessorySchema>;
export type UpdateAccessorySchema = z.infer<typeof updateAccessorySchema>;
export type DeleteAccessorySchema = z.infer<typeof deleteAccessorySchema>;
export type AccessoryQuerySchema = z.infer<typeof accessoryQuerySchema>;
export type GetAccessoryDetailSchema = z.infer<typeof getAccessoryDetailSchema>;

// 导出参数类型（用于API调用）
export type CreateAccessoryParams = z.infer<typeof createAccessorySchema>;
export type UpdateAccessoryParams = z.infer<typeof updateAccessorySchema>;
export type DeleteAccessoryParams = z.infer<typeof deleteAccessorySchema>;
export type AccessoryQueryParams = z.infer<typeof accessoryQuerySchema>;
export type GetAccessoryDetailParams = z.infer<typeof getAccessoryDetailSchema>; 