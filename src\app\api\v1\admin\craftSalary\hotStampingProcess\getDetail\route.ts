import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const getHotStampingProcessDetailSchema = z.object({
  id: z.number().int().positive(),
});

const handler = withValidation(
  getHotStampingProcessDetailSchema,
  async (request: AuthenticatedRequest, validatedData: any) => {
    const { id } = validatedData;

    // 查询烫金工艺详情
    const hotStampingProcess = await prisma.hotStampingProcess.findFirst({
      where: {
        id: id,
        isDel: false,
      },
    });

    assert(!!hotStampingProcess, ErrorCode.NOT_FOUND, '烫金工艺不存在');

    return successResponse(
      hotStampingProcess,
      '查询烫金工艺详情成功'
    );
  }
); 
export const POST = withInternalAuth(handler);