import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { CreateDieCuttingPlateFeeData, createDieCuttingPlateFeeSchema } from '@/lib/validations/admin/dieCuttingPlateFee';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation(
  createDieCuttingPlateFeeSchema,
  async (request: AuthenticatedRequest, validatedData: CreateDieCuttingPlateFeeData) => {
    const data = validatedData;

    // 检查刀版费名称是否重复
    const existingPlateFee = await prisma.dieCuttingPlateFee.findFirst({
      where: {
        name: data.name,
        isDel: false,
      },
    });

    assert(!existingPlateFee, ErrorCode.DUPLICATE_ENTRY, '刀版费名称已存在');

    // 创建刀版费数据
    const dieCuttingPlateFee = await prisma.dieCuttingPlateFee.create({
      data: {
        name: data.name,
        price: data.price,
        unit: data.unit,
        basePrice: data.basePrice,
        impositionQuantity: data.impositionQuantity,
        remark: data.remark,
      },
    });

    return successResponse(dieCuttingPlateFee, '创建刀版费成功');
  }
);

export const POST = withInternalAuth(handler);