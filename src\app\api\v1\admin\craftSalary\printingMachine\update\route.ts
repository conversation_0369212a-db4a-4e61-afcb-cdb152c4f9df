import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { UpdatePrintingMachineData, updatePrintingMachineSchema } from '@/lib/validations/admin/printing';
import { withValidation, assert, assertExists } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation(
  updatePrintingMachineSchema,
  async (request: AuthenticatedRequest, validatedData: UpdatePrintingMachineData) => {
    const data = validatedData;

    // 检查印刷机数据是否存在
    const existingMachine = await prisma.printingMachine.findFirst({
      where: {
        id: data.id,
        isDel: false,
      },
    });

    assertExists(existingMachine, ErrorCode.RESOURCE_NOT_FOUND, '印刷机数据不存在');

    // 如果修改了机器名称，检查名称是否重复
    if (data.machineName && data.machineName !== existingMachine!.machineName) {
      const duplicateMachine = await prisma.printingMachine.findFirst({
        where: {
          machineName: data.machineName,
          id: { not: data.id },
          isDel: false,
        },
      });

      assert(!duplicateMachine, ErrorCode.DUPLICATE_ENTRY, '印刷机名称已存在');
    }

    // 移除ID字段，避免更新时包含ID
    const { id: _, ...updateData } = data;

    // 更新印刷机数据
    const result = await prisma.printingMachine.update({
      where: { id: data.id },
      data: updateData,
    });

    return successResponse(result, '更新印刷机成功');
  }
); 
export const POST = withInternalAuth(handler);