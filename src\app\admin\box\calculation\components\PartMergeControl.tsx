/**
 * 部件合并控制组件（简化版）
 * 让用户选择是否合并部件，提供简单的合并选项
 */

import React, { useEffect } from 'react';
import {
  Card,
  Switch,
  Space,
  Tag,
  Alert,
  Typography,
  Row,
  Col,
  Checkbox
} from 'antd';
import {
  GroupOutlined
} from '@ant-design/icons';
import { BoxPart } from '@/types/box';
import { PartMergeGroup } from '../types/calculation';

const { Text } = Typography;

// 材料类型枚举
export enum MaterialType {
  FACE_PAPER = 'face_paper',     // 面纸
  GREY_BOARD = 'grey_board'      // 灰板纸
}

// 部件组
export interface PartGroup {
  materialType: MaterialType;
  parts: BoxPart[];
  totalQuantity: number;
  description: string;
}

// 合并结果
export interface MergingResult {
  canMerge: boolean;
  groups: PartGroup[];
  mergingStrategy: string;
  benefits: string[];
  warnings: string[];
}

interface PartMergeControlProps {
  parts: BoxPart[];
  mergingAnalysis?: MergingResult;
  enablePartMerging?: boolean;
  selectedMergeGroups?: PartMergeGroup[];
  onEnableChange: (enabled: boolean) => void;
  onMergeGroupsChange: (groups: PartMergeGroup[]) => void;
}

export const PartMergeControl: React.FC<PartMergeControlProps> = ({
  parts,
  mergingAnalysis,
  enablePartMerging = false,
  selectedMergeGroups = [],
  onEnableChange,
  onMergeGroupsChange
}) => {

  // 分析部件材料类型
  const analyzePartMaterials = () => {
    const facePaperParts: BoxPart[] = [];
    const greyBoardParts: BoxPart[] = [];

    parts.forEach(part => {
      const hasFacePaper = part.formulas?.some(f =>
        ['面纸长度', '面纸宽度'].includes(f.name) && f.expression?.trim()
      );
      const hasGreyBoard = part.formulas?.some(f =>
        ['灰板纸长度', '灰板纸宽度'].includes(f.name) && f.expression?.trim()
      );

      if (hasFacePaper) {
        facePaperParts.push(part);
      }
      if (hasGreyBoard) {
        greyBoardParts.push(part);
      }
    });

    return { facePaperParts, greyBoardParts };
  };

  const { facePaperParts, greyBoardParts } = analyzePartMaterials();

  // 生成默认合并组
  const generateDefaultMergeGroups = (): PartMergeGroup[] => {
    const groups: PartMergeGroup[] = [];

    // 面纸合并组
    if (facePaperParts.length > 1) {
      groups.push({
        id: 'face_paper_group',
        name: '面纸合并组',
        materialType: 'face_paper',
        partIds: facePaperParts.map(p => p.id!),
        partNames: facePaperParts.map(p => p.name),
        enabled: true,
        description: `合并 ${facePaperParts.length} 个使用面纸的部件`
      });
    }

    // 灰板纸合并组
    if (greyBoardParts.length > 1) {
      groups.push({
        id: 'grey_board_group',
        name: '灰板纸合并组',
        materialType: 'grey_board',
        partIds: greyBoardParts.map(p => p.id!),
        partNames: greyBoardParts.map(p => p.name),
        enabled: true,
        description: `合并 ${greyBoardParts.length} 个使用灰板纸的部件`
      });
    }

    return groups;
  };

  // 初始化默认合并组
  useEffect(() => {
    if (enablePartMerging && selectedMergeGroups.length === 0) {
      const defaultGroups = generateDefaultMergeGroups();
      if (defaultGroups.length > 0) {
        onMergeGroupsChange(defaultGroups);
      }
    }
  }, [enablePartMerging, parts]);

  // 切换合并组启用状态
  const toggleGroupEnabled = (groupId: string, enabled: boolean) => {
    const updatedGroups = selectedMergeGroups.map(group =>
      group.id === groupId ? { ...group, enabled } : group
    );
    onMergeGroupsChange(updatedGroups);
  };



  // 切换部件在合并组中的状态
  const togglePartInGroup = (groupId: string, partId: number, partName: string, include: boolean) => {
    const updatedGroups = selectedMergeGroups.map(group => {
      if (group.id === groupId) {
        if (include) {
          return {
            ...group,
            partIds: [...group.partIds, partId],
            partNames: [...group.partNames, partName]
          };
        } else {
          return {
            ...group,
            partIds: group.partIds.filter(id => id !== partId),
            partNames: group.partNames.filter(name => name !== partName)
          };
        }
      }
      return group;
    });
    onMergeGroupsChange(updatedGroups);
  };

  // 渲染合并控制面板
  const renderMeringControl = () => {
    return (
      <Card
        title={
          <Space>
            <GroupOutlined />
            <span>部件合并分析与选择</span>
          </Space>
        }
        size="small"
        style={{ marginBottom: 16 }}
      >
        <Row gutter={[24, 16]}>
         
          {/* 右侧：部件合并选择 */}
          <Col span={12}>
            {(
              <div>
                <div style={{ marginBottom: 12 }}>
                  <Text strong>部件合并设置：</Text>
                  <div style={{ marginTop: 8 }}>
                    <Switch
                      checked={enablePartMerging}
                      onChange={onEnableChange}
                      checkedChildren="启用合并"
                      unCheckedChildren="禁用合并"
                    />
                    <Text type="secondary" style={{ marginLeft: 8, fontSize: '12px' }}>
                      {enablePartMerging ? '已启用部件合并' : '默认不进行合并'}
                    </Text>
                  </div>
                </div>

                {/* 合并选项 */}
                {enablePartMerging && (
                  <>
                    {/* 面纸合并组 */}
                    {facePaperParts.length > 1 && (
                      <div style={{ marginBottom: 12 }}>
                        <div style={{ marginBottom: 8 }}>
                          <Tag color="blue">面纸部件合并</Tag>
                          <Switch
                            checked={selectedMergeGroups.some(g => g.id === 'face_paper_group' && g.enabled)}
                            onChange={(checked) => toggleGroupEnabled('face_paper_group', checked)}
                            size="small"
                            style={{ marginLeft: 8 }}
                          />
                        </div>
                        {selectedMergeGroups.some(g => g.id === 'face_paper_group' && g.enabled) && (
                          <div style={{ paddingLeft: 8 }}>
                            {facePaperParts.map(part => (
                              <div key={part.id} style={{ marginBottom: 4 }}>
                                <Checkbox
                                  checked={selectedMergeGroups.some(g =>
                                    g.id === 'face_paper_group' && g.partIds.includes(part.id!)
                                  )}
                                  onChange={(e) => togglePartInGroup('face_paper_group', part.id!, part.name, e.target.checked)}
                                  style={{ fontSize: '12px' }}
                                >
                                  {part.name}
                                </Checkbox>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    )}

                    {/* 灰板纸合并组 */}
                    {greyBoardParts.length > 1 && (
                      <div style={{ marginBottom: 12 }}>
                        <div style={{ marginBottom: 8 }}>
                          <Tag color="orange">灰板纸部件合并</Tag>
                          <Switch
                            checked={selectedMergeGroups.some(g => g.id === 'grey_board_group' && g.enabled)}
                            onChange={(checked) => toggleGroupEnabled('grey_board_group', checked)}
                            size="small"
                            style={{ marginLeft: 8 }}
                          />
                        </div>
                        {selectedMergeGroups.some(g => g.id === 'grey_board_group' && g.enabled) && (
                          <div style={{ paddingLeft: 8 }}>
                            {greyBoardParts.map(part => (
                              <div key={part.id} style={{ marginBottom: 4 }}>
                                <Checkbox
                                  checked={selectedMergeGroups.some(g =>
                                    g.id === 'grey_board_group' && g.partIds.includes(part.id!)
                                  )}
                                  onChange={(e) => togglePartInGroup('grey_board_group', part.id!, part.name, e.target.checked)}
                                  style={{ fontSize: '12px' }}
                                >
                                  {part.name}
                                </Checkbox>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    )}
                  </>
                )}
              </div>
            )}
          </Col>
        </Row>
      </Card>
    );
  };

  return (
    <>
      {renderMeringControl()}
    </>
  );
};

export default PartMergeControl;
