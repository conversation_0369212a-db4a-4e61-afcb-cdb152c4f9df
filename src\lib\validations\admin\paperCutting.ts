import { z } from 'zod';

// 分切尺寸查询参数验证 schema
export const paperCuttingQuerySchema = z.object({
  page: z.number().positive('页码必须大于0').optional(),
  pageSize: z.number().positive('每页条数必须大于0').optional(),
  keyword: z.string().optional(),
});

// 分切尺寸详情查询验证 schema
export const getPaperCuttingDetailSchema = z.object({
  id: z.string().transform(Number).pipe(
    z.number().positive('分切尺寸ID必须大于0')
  ),
});

// 删除分切尺寸验证 schema
export const deletePaperCuttingSchema = z.object({
  id: z.string().transform(Number).pipe(
    z.number().positive('分切尺寸ID必须大于0')
  ),
});

// 分切尺寸基础验证 schema
const paperCuttingBaseSchema = {
  name: z.string({
    required_error: '请输入名称',
    invalid_type_error: '名称必须是字符串',
  }).min(1, '名称不能为空').max(100, '名称不能超过100个字符'),
  
  initialCutPrice: z.number({
    required_error: '请输入分切起步金额',
    invalid_type_error: '分切起步金额必须是数字',
  }).min(0, '分切起步金额不能小于0'),
  
  sizes: z.array(
    z.number({
      required_error: '请输入分切尺寸',
      invalid_type_error: '分切尺寸必须是数字',
    }).min(0, '分切尺寸不能小于0')
  ).min(1, '至少需要一个分切尺寸'),
};

// 创建分切尺寸验证 schema
export const createPaperCuttingSchema = z.object({
  ...paperCuttingBaseSchema,
});

// 更新分切尺寸验证 schema
export const updatePaperCuttingSchema = z.object({
  id: z.number({
    required_error: '请提供分切尺寸ID',
    invalid_type_error: '分切尺寸ID必须是数字',
  }).positive('分切尺寸ID必须大于0'),
  ...paperCuttingBaseSchema,
});

// 类型定义
export type PaperCuttingQueryParams = z.infer<typeof paperCuttingQuerySchema>;
export type GetPaperCuttingDetailParams = z.infer<typeof getPaperCuttingDetailSchema>;
export type DeletePaperCuttingParams = z.infer<typeof deletePaperCuttingSchema>;
export type CreatePaperCuttingParams = z.infer<typeof createPaperCuttingSchema>;
export type UpdatePaperCuttingParams = z.infer<typeof updatePaperCuttingSchema>; 