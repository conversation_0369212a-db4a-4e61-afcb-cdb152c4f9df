'use client';

import React, { useState, useEffect } from 'react';
import { 
  Table, Button, Space, Modal, Form, Input, 
  Select, InputNumber, Popconfirm, Card, Typography,
  message, Row, Col
} from 'antd';
import { 
  PlusOutlined, EditOutlined, DeleteOutlined, 
  SearchOutlined, ReloadOutlined
} from '@ant-design/icons';
import { stickerApi } from '@/services/adminApi';
import { Sticker } from '@/types/material';
import { createStickerSchema, updateStickerSchema } from '@/lib/validations/admin/sticker';
import { useAsyncError } from '@/lib/hooks/useErrorHandler';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;

// 不干胶数据库管理页面
export default function StickerManagementPage() {
  // 使用错误处理Hook
  const { execute: executeSticker, loading: stickerLoading } = useAsyncError();

  // 不干胶数据相关状态
  const [stickerList, setStickerList] = useState<Sticker[]>([]);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [keyword, setKeyword] = useState('');
  const [category, setCategory] = useState('');
  const [categoryList, setCategoryList] = useState<string[]>([]);

  // 模态框相关状态
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [form] = Form.useForm();

  // 初始加载数据
  useEffect(() => {
    fetchList();
    fetchCategoryList();
  }, []);

  // 获取不干胶列表
  const fetchList = async (
    page = current,
    ps = pageSize,
    kw = keyword,
    cat = category
  ) => {
    const result = await executeSticker(async () => {
      return await stickerApi.getList({
        page,
        pageSize: ps,
        keyword: kw,
        category: cat
      });
    }, '获取不干胶列表');

    if (result) {
      setStickerList(result.list);
      setTotal(result.pagination.total);
    } else {
      setStickerList([]);
      setTotal(0);
    }
  };

  // 获取材料品类列表
  const fetchCategoryList = async () => {
    const result = await executeSticker(async () => {
      return await stickerApi.getCategoryList();
    }, '获取材料品类列表');

    if (result) {
      setCategoryList(result);
    } else {
      setCategoryList([]);
    }
  };

  // 处理分页变化
  const handleTableChange = (pagination: any) => {
    setCurrent(pagination.current);
    setPageSize(pagination.pageSize);
    fetchList(pagination.current, pagination.pageSize);
  };

  // 打开添加不干胶模态框
  const showAddModal = () => {
    setModalTitle('添加不干胶');
    setEditingRecord(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 打开编辑不干胶模态框
  const showEditModal = (record: Sticker) => {
    setModalTitle('编辑不干胶');
    setEditingRecord(record);
    form.setFieldsValue({
      name: record.name,
      price: record.price,
      unit: record.unit,
      weight: record.weight,
      category: record.category,
      remark: record.remark
    });
    setModalVisible(true);
  };

  // 处理表单提交
  const handleFormSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      // 处理品类数据类型问题 - mode="tags"会返回数组，需要转换为字符串
      if (Array.isArray(values.category) && values.category.length > 0) {
        values.category = values.category[0];
      }
      
      // 使用通用校验组件进行验证
      const schema = editingRecord ? updateStickerSchema : createStickerSchema;
      const validationResult = schema.safeParse({
        ...values,
        ...(editingRecord ? { id: editingRecord.id } : {})
      });

      if (!validationResult.success) {
        const errors = validationResult.error.format();
        const firstError = Object.values(errors)[0];
        if (firstError && typeof firstError === 'object' && '_errors' in firstError) {
          message.error(firstError._errors[0] || '表单验证失败');
        } else {
          message.error('表单验证失败');
        }
        return;
      }
      
      if (editingRecord) {
        // 更新不干胶
        const result = await executeSticker(async () => {
          return await stickerApi.update({
            id: editingRecord.id,
            ...values
          });
        }, '更新不干胶');
        
        if (result) {
          message.success('不干胶更新成功');
          setModalVisible(false);
          fetchList();
          fetchCategoryList();
        }
      } else {
        // 创建不干胶
        const result = await executeSticker(async () => {
          return await stickerApi.create(values);
        }, '创建不干胶');
        
        if (result) {
          message.success('不干胶添加成功');
          setModalVisible(false);
          fetchList();
          fetchCategoryList();
        }
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || '表单提交失败';
      message.error(errorMessage);
      console.error('表单提交失败:', error);
    }
  };

  // 删除不干胶
  const handleDelete = async (id: number) => {
    const result = await executeSticker(async () => {
      return await stickerApi.delete(id);
    }, '删除不干胶');
    
    if (result) {
      message.success('不干胶删除成功');
      fetchList();
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center' as const,
      width: 150,
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      align: 'center' as const,
      width: 100,
      render: (text: number) => `${text} 元`,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      align: 'center' as const,
      width: 100,
    },
    {
      title: '克重',
      dataIndex: 'weight',
      key: 'weight',
      align: 'center' as const,
      width: 100,
      render: (text: number) => `${text} g/m²`,
    },
    {
      title: '材料品类',
      dataIndex: 'category',
      key: 'category',
      align: 'center' as const,
      width: 120,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      align: 'center' as const,
      width: 200,
      ellipsis: true,
      render: (remark: string) => remark || '-',
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      align: 'center' as const,
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'action',
      align: 'center' as const,
      fixed: 'right' as const,
      width: 150,
      render: (_: any, record: Sticker) => (
        <Space size="middle">
          <Button 
            type="primary" 
            icon={<EditOutlined />} 
            size="small"
            onClick={() => showEditModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此不干胶吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="是"
            cancelText="否"
          >
            <Button
              danger 
              icon={<DeleteOutlined />} 
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>不干胶数据库</Title>
      
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Row gutter={16} align="middle">
            <Col span={4}>
              <Input
                placeholder="搜索名称"
                prefix={<SearchOutlined />}
                allowClear
                value={keyword}
                onChange={(e) => setKeyword(e.target.value)}
                onPressEnter={() => fetchList(1, pageSize, keyword, category)}
              />
            </Col>
            <Col span={4}>
              <Select
                placeholder="搜索材料品类"
                allowClear
                showSearch
                loading={stickerLoading}
                style={{ width: '100%' }}
                value={category || undefined}
                onChange={(value) => setCategory(value || '')}
                optionFilterProp="children"
                filterOption={(input, option) =>
                  (option?.children as unknown as string).toLowerCase().includes(input.toLowerCase())
                }
              >
                {categoryList.map((cat) => (
                  <Option key={cat} value={cat}>
                    {cat}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col>
              <Button 
                type="primary" 
                icon={<SearchOutlined />}
                onClick={() => fetchList(1, pageSize, keyword, category)}
              >
                搜索
              </Button>
            </Col>
            <Col>
              <Button 
                icon={<ReloadOutlined />}
                onClick={() => {
                  setKeyword('');
                  setCategory('');
                  fetchList(1, pageSize, '', '');
                }}
              >
                重置
              </Button>
            </Col>
            <Col flex="auto" style={{ textAlign: 'right' }}>
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={showAddModal}
              >
                添加不干胶
              </Button>
            </Col>
          </Row>
        </div>
        
        <Table
          columns={columns}
          dataSource={stickerList}
          rowKey="id"
          pagination={{
            current,
            pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
          }}
          loading={stickerLoading}
          onChange={handleTableChange}
          bordered
          size="middle"
          scroll={{ x: 1200 }}
          locale={{ emptyText: '暂无数据' }}
        />
      </Card>

      {/* 表单模态框 */}
      <Modal
        title={modalTitle}
        open={modalVisible}
        onOk={handleFormSubmit}
        onCancel={() => setModalVisible(false)}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="不干胶名称"
                rules={[
                  { required: true, message: '请输入不干胶名称' },
                  { max: 100, message: '名称不能超过100个字符' }
                ]}
              >
                <Input placeholder="请输入不干胶名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="category"
                label="材料品类"
                rules={[
                  { required: true, message: '请选择或输入材料品类' },
                ]}
              >
                <Select
                  placeholder="请选择或输入材料品类"
                  mode="tags"
                  loading={stickerLoading}
                >
                  {categoryList.map(category => (
                    <Option key={category} value={category}>
                      {category}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="price"
                label="价格"
                rules={[
                  { required: true, message: '请输入价格' },
                  { type: 'number', min: 0, message: '价格必须大于等于0' }
                ]}
              >
                <InputNumber
                  placeholder="请输入价格"
                  style={{ width: '100%' }}
                  precision={2}
                  min={0}
                  addonAfter="元"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="unit"
                label="单位"
                rules={[{ required: true, message: '请选择单位' }]}
              >
                <Select placeholder="请选择单位">
                  <Option value="元/平方">元/平方</Option>
                  <Option value="元/张">元/张</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="weight"
                label="克重"
                rules={[
                  { required: true, message: '请输入克重' },
                  { type: 'number', min: 0, message: '克重必须大于0' }
                ]}
              >
                <InputNumber
                  placeholder="请输入克重"
                  style={{ width: '100%' }}
                  min={0}
                  addonAfter="g/m²"
                />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="remark"
            label="备注"
            rules={[{ max: 1000, message: '备注不能超过1000个字符' }]}
          >
            <Input.TextArea
              placeholder="请输入备注信息"
              rows={4}
              showCount
              maxLength={1000}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
} 