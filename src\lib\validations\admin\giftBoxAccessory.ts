import { z } from 'zod';

// 礼盒配件基础验证 schema
const giftBoxAccessoryBaseSchema = {
  name: z.string({
    required_error: '请输入礼盒配件名称',
    invalid_type_error: '礼盒配件名称必须是字符串',
  }).min(1, '礼盒配件名称不能为空').max(100, '礼盒配件名称不能超过100个字符'),
  
  price: z.number({
    required_error: '请输入价格',
    invalid_type_error: '价格必须是数字',
  }).min(0, '价格不能小于0'),
  
  unit: z.enum(['元/立方', '元/平方'], {
    required_error: '请选择单位',
    invalid_type_error: '无效的单位类型',
  }),
  
  isStockSize: z.boolean({
    invalid_type_error: '现货尺寸标识必须是布尔值',
  }).optional(),
  
  stockLength: z.number({
    invalid_type_error: '现货长度必须是数字',
  }).min(0, '现货长度不能小于0').optional(),
  
  stockWidth: z.number({
    invalid_type_error: '现货宽度必须是数字',
  }).min(0, '现货宽度不能小于0').optional(),
  
  remark: z.string().max(1000, '备注不能超过1000个字符').optional(),
};

// 创建礼盒配件验证 schema
export const createGiftBoxAccessorySchema = z.object({
  ...giftBoxAccessoryBaseSchema,
});

// 更新礼盒配件验证 schema
export const updateGiftBoxAccessorySchema = z.object({
  id: z.string().transform(Number).pipe(
    z.number().positive('礼盒配件ID必须大于0')
  ),
  ...giftBoxAccessoryBaseSchema,
});

// 删除礼盒配件验证 schema
export const deleteGiftBoxAccessorySchema = z.object({
  id: z.string().transform(Number).pipe(
    z.number().positive('礼盒配件ID必须大于0')
  ),
});

// 查询参数验证 schema
export const giftBoxAccessoryQuerySchema = z.object({
  page: z.number().positive('页码必须大于0').optional(),
  pageSize: z.number().positive('每页条数必须大于0').optional(),
  keyword: z.string().optional(),
});

// 获取礼盒配件详情验证 schema
export const getGiftBoxAccessoryDetailSchema = z.object({
  id: z.string().transform(Number).pipe(
    z.number().positive('礼盒配件ID必须大于0')
  ),
});

// 现货尺寸验证函数
export const validateStockSize = (data: any) => {
  if (data.isStockSize && (!data.stockLength || !data.stockWidth)) {
    return { 
      success: false, 
      error: '按现货尺寸时，必须提供现货长度和宽度' 
    };
  }
  return { success: true };
};

// 类型定义
export type CreateGiftBoxAccessorySchema = z.infer<typeof createGiftBoxAccessorySchema>;
export type UpdateGiftBoxAccessorySchema = z.infer<typeof updateGiftBoxAccessorySchema>;
export type DeleteGiftBoxAccessorySchema = z.infer<typeof deleteGiftBoxAccessorySchema>;
export type GiftBoxAccessoryQuerySchema = z.infer<typeof giftBoxAccessoryQuerySchema>;
export type GetGiftBoxAccessoryDetailSchema = z.infer<typeof getGiftBoxAccessoryDetailSchema>;

// 导出参数类型（用于API调用）
export type CreateGiftBoxAccessoryParams = z.infer<typeof createGiftBoxAccessorySchema>;
export type UpdateGiftBoxAccessoryParams = z.infer<typeof updateGiftBoxAccessorySchema>;
export type DeleteGiftBoxAccessoryParams = z.infer<typeof deleteGiftBoxAccessorySchema>;
export type GiftBoxAccessoryQueryParams = z.infer<typeof giftBoxAccessoryQuerySchema>;
export type GetGiftBoxAccessoryDetailParams = z.infer<typeof getGiftBoxAccessoryDetailSchema>; 