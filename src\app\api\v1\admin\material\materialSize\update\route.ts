import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { materialSizeUpdateSchema } from '@/lib/validations/admin/materialSize';
import { withValidation } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { MaterialSizeType } from '@/types/material';

export const POST = withValidation(
  materialSizeUpdateSchema,
  async (request: NextRequest, validatedData: any) => {
    const { config } = validatedData;

    // 添加事务
    await prisma.$transaction(async (tx) => {
      await Promise.all([
        tx.materialSize.updateMany({
          where: { type: MaterialSizeType.REGULAR_LENGTH },
          data: { size: config.regularLength, updatedAt: new Date() },
        }),
        tx.materialSize.updateMany({
          where: { type: MaterialSizeType.REGULAR_WIDTH },
          data: { size: config.regularWidth, updatedAt: new Date() },
        }),
        tx.materialSize.updateMany({
          where: { type: MaterialSizeType.LARGE_LENGTH },
          data: { size: config.largeLength, updatedAt: new Date() },
        }),
        tx.materialSize.updateMany({
          where: { type: MaterialSizeType.LARGE_WIDTH },
          data: { size: config.largeWidth, updatedAt: new Date() },
        }),
        tx.materialSize.updateMany({
          where: { type: MaterialSizeType.SPECIAL_LENGTH },
          data: { size: config.specialLength, updatedAt: new Date() },
        }),
        tx.materialSize.updateMany({
          where: { type: MaterialSizeType.SPECIAL_WIDTH },
          data: { size: config.specialWidth, updatedAt: new Date() },
        })
      ]);
    });

    return successResponse(config, '材料尺寸配置更新成功');
  }
); 