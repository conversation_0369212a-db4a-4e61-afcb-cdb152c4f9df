import { NextRequest } from 'next/server';
import { DeleteBoxMaterialData, deleteBoxMaterialSchema } from '@/lib/validations/admin/boxMaterial';
import { prisma } from '@/lib/prisma';
import { withValidation } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';

const handler = withValidation(
  deleteBoxMaterialSchema,
  async (request: AuthenticatedRequest, data: DeleteBoxMaterialData) => {
    const { id } = data;

    // 软删除纸箱材料
    await prisma.boxMaterial.update({
      where: { id },
      data: {
        isDel: true,
      },
    });

    return successResponse(true, '删除纸箱材料成功');
  }
); 
export const POST = withInternalAuth(handler);