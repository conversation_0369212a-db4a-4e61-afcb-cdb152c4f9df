import { NextRequest } from 'next/server';
import { DeleteBoxMaterialData, deleteBoxMaterialSchema } from '@/lib/validations/admin/boxMaterial';
import { prisma } from '@/lib/prisma';
import { withValidation } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';

export const POST = withValidation(
  deleteBoxMaterialSchema,
  async (request: NextRequest, data: DeleteBoxMaterialData) => {
    const { id } = data;

    // 软删除纸箱材料
    await prisma.boxMaterial.update({
      where: { id },
      data: {
        isDel: true,
      },
    });

    return successResponse(true, '删除纸箱材料成功');
  }
); 