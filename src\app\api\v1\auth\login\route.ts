import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { loginSchema, LoginParams } from '@/lib/validations/user';
import { withValidation } from '@/lib/middleware/errorHandler';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';
import { verifyPassword } from '@/lib/auth/password';
import { generateToken, setTokenCookie, getClientIP, AUTH_CONFIG } from '@/lib/auth/jwt';
import { UserRole, UserState, LoginResponse } from '@/types/user';

export const POST = withValidation<LoginParams>(
  loginSchema,
  async (request: NextRequest, validatedData: LoginParams) => {
    const { username, password, rememberMe } = validatedData;

    try {
      // 获取客户端IP
      const clientIP = getClientIP(request);

      // 查找用户（支持手机号、邮箱登录）
      const user = await prisma.user.findFirst({
        where: {
          OR: [
            { phone: username },
            { email: username }
          ],
          isDel: false
        }
      });

      if (!user) {
        return errorResponse(ErrorCode.INVALID_CREDENTIALS, '用户名或密码错误', null, 401)
      }

      // 检查用户状态
      if (user.state === UserState.DISABLED) {
        return errorResponse(ErrorCode.USER_DISABLED, '账户已被禁用，请联系管理员', null, 403)
      }

      // 检查超级用户是否过期
      if (user.role === UserRole.SUPER_USER && user.expiresAt) {
        if (new Date() > user.expiresAt) {
          return errorResponse(ErrorCode.USER_EXPIRED, '账户已过期，请联系管理员', null, 403)
        }
      }

      // 验证密码
      const isPasswordValid = await verifyPassword(password, user.password);
      if (!isPasswordValid) {
        return errorResponse(ErrorCode.INVALID_CREDENTIALS, '用户名或密码错误', null, 401)
      }

      // 生成JWT Token
      const token = generateToken({
        userId: user.id,
        role: user.role as UserRole,
        phone: user.phone
      });

      // 更新用户登录信息
      const now = new Date();
      await prisma.user.update({
        where: { id: user.id },
        data: {
          lastLoginIp: user.currentLoginIp,
          lastLoginAt: user.currentLoginAt,
          currentLoginIp: clientIP,
          currentLoginAt: now,
          currentLoginToken: token
        }
      });

      // 准备响应数据
      const loginResponse: LoginResponse = {
        user: {
          id: user.id,
          name: user.name,
          phone: user.phone,
          email: user.email,
          role: user.role as UserRole,
          expiresAt: user.expiresAt
        },
        token,
        expiresIn: AUTH_CONFIG.JWT_EXPIRES_IN
      };


      // 创建响应
      const response = successResponse(loginResponse, '登录成功');

      // 设置Cookie（如果选择记住我或默认设置）
      if (rememberMe !== false) {
        response.headers.set('Set-Cookie', setTokenCookie(token));
      }

      return response;
    } catch (error) {
      console.error('登录失败:', error);
      return errorResponse(ErrorCode.INTERNAL_ERROR, '登录失败，请稍后重试')
    }
  }
);
