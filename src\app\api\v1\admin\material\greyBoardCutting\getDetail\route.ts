import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getGreyBoardCuttingDetailSchema, GetGreyBoardCuttingDetailParams } from '@/lib/validations/admin/greyBoardCutting';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation<GetGreyBoardCuttingDetailParams>(
  getGreyBoardCuttingDetailSchema,
  async (request: NextRequest, validatedQuery: GetGreyBoardCuttingDetailParams) => {
    // 查询灰板分切尺寸详细信息
    const greyBoardCutting = await prisma.greyBoardCutting.findFirst({
      where: {
        id: validatedQuery.id,
        isDel: false,
      },
      select: {
        id: true,
        name: true,
        initialCutPrice: true,
        sizes: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    assertExists(greyBoardCutting, ErrorCode.MATERIAL_NOT_FOUND, '灰板分切尺寸不存在');

    // 处理sizes字段，确保返回的是数组
    const formattedData = {
      ...greyBoardCutting,
      sizes: greyBoardCutting.sizes ? (Array.isArray(greyBoardCutting.sizes) ? greyBoardCutting.sizes : JSON.parse(String(greyBoardCutting.sizes))) : []
    };

    return successResponse(formattedData, '获取灰板分切尺寸详情成功');
  }
); 