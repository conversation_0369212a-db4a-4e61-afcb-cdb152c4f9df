import { z } from 'zod';

// 基础字段验证规则
export const stickerBaseSchema = z.object({
  name: z.string()
    .min(1, '名称不能为空')
    .max(100, '名称长度不能超过100'),
  price: z.number()
    .positive('价格必须大于0')
    .refine(val => val <= 1000000, '价格不能超过1000000'),
  unit: z.string()
    .min(1, '单位不能为空')
    .max(20, '单位长度不能超过20')
    .refine(val => ['元/平方', '元/张'].includes(val), '单位必须是：元/平方、元/张'),
  weight: z.number()
    .min(0, '克重必须大于等于0')
    .refine(val => val <= 1000, '克重不能超过1000'),
  category: z.string()
    .min(1, '材料品类不能为空')
    .max(50, '材料品类长度不能超过50'),
  remark: z.string()
    .max(1000, '备注长度不能超过1000')
    .optional()
    .nullable(),
});

// 创建不干胶的验证schema
export const createStickerSchema = stickerBaseSchema;

// 更新不干胶的验证schema
export const updateStickerSchema = stickerBaseSchema.extend({
  id: z.number().int().positive('ID必须是正整数'),
});

// 获取不干胶详情的验证schema
export const getStickerDetailSchema = z.object({
  id: z.string().transform(Number).pipe(
    z.number().int().positive('不干胶ID必须是正整数')
  ),
});

// 获取不干胶列表的验证schema
export const getStickerListSchema = z.object({
  page: z.number().int().min(1, '页码必须大于0').default(1),
  pageSize: z.number().int().min(1, '每页条数必须大于0').max(100, '每页条数不能超过100').default(10),
  keyword: z.string().optional(),
  category: z.string().optional(),
});

// 删除不干胶的验证schema
export const deleteStickerSchema = z.object({
  id: z.string().transform(Number).pipe(
    z.number().int().positive('不干胶ID必须是正整数')
  ),
});

// 导出类型
export type StickerBase = z.infer<typeof stickerBaseSchema>;
export type CreateStickerParams = z.infer<typeof createStickerSchema>;
export type UpdateStickerParams = z.infer<typeof updateStickerSchema>;
export type GetStickerDetailParams = z.infer<typeof getStickerDetailSchema>;
export type GetStickerListParams = z.infer<typeof getStickerListSchema>;
export type DeleteStickerParams = z.infer<typeof deleteStickerSchema>;

// 查询参数类型（用于中间件）
export type StickerQueryParams = GetStickerListParams; 