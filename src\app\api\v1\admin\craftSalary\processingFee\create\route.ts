import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createProcessingFeeSchema } from '@/lib/validations/admin/processingFee';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation(
  createProcessingFeeSchema,
  async (request: AuthenticatedRequest, validatedData: any) => {
    // 检查名称是否已存在
    const existingFee = await prisma.processingFee.findFirst({
      where: {
        name: validatedData.name,
        isDel: false
      }
    });

    assert(!existingFee, ErrorCode.DUPLICATE_ENTRY, '该加工费名称已存在');

    // 创建加工费记录
    const processingFee = await prisma.processingFee.create({
      data: {
        name: validatedData.name,
        unitPrice: validatedData.unitPrice,
        unit: validatedData.unit,
        basePrice: validatedData.basePrice,
        remark: validatedData.remark || ''
      }
    });

    return successResponse(processingFee, '创建加工费成功');
  }
); 
export const POST = withInternalAuth(handler);