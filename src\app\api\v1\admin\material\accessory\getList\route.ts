import { NextRequest } from 'next/server';
import { accessoryQuerySchema, AccessoryQueryParams } from '@/lib/validations/admin/accessory';
import { prisma } from '@/lib/prisma';
import { withValidation } from '@/lib/middleware/errorHandler';
import { paginatedResponse } from '@/lib/utils/apiResponse';

export const POST = withValidation<AccessoryQueryParams>(
  accessoryQuerySchema,
  async (request: NextRequest, validatedQuery: AccessoryQueryParams) => {
      const { page = 1, pageSize = 10, keyword = '' } = validatedQuery;

      // 构建查询条件
      const where: any = {
        isDel: false,
      };

      if (keyword) {
        where.name = {
          contains: keyword,
        };
      }

      // 查询总数和数据
      const [total, list] = await Promise.all([
        prisma.accessory.count({ where }),
        prisma.accessory.findMany({
          where,
          skip: (page - 1) * pageSize,
          take: pageSize,
          orderBy: {
            createdAt: 'desc',
          },
        }),
      ]);

      const pagination = {
        total,
        page,
        pageSize,
      };

    return paginatedResponse(list, pagination, '获取配件列表成功');
  }
); 