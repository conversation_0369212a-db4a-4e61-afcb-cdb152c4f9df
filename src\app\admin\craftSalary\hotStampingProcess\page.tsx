'use client';

import React, { useState, useEffect } from 'react';
import {
  Table, Button, Space, Modal, Form, Input,
  Select, InputNumber, Popconfirm, Typography,
  message, Row, Col, Tabs, Card
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined,
  SearchOutlined, ReloadOutlined
} from '@ant-design/icons';
import { hotStampingProcessApi, hotStampingPlateFeeApi } from '@/services/adminApi';
import {
  HotStampingProcess, HotStampingPlateFee,
  HOT_STAMPING_SALARY_UNITS, HOT_STAMPING_MATERIAL_UNITS, HOT_STAMPING_PLATE_FEE_UNITS,
  HotStampingSalaryUnit, HotStampingMaterialUnit, HotStampingPlateFeeUnit
} from '@/types/craftSalary';
import { useAsyncError } from '@/lib/hooks/useErrorHandler';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

/**
 * 烫金工艺管理页面
 */
export default function HotStampingProcessManagementPage() {
  // 错误处理Hook
  const { execute: executeHotStampingProcess, loading: hotStampingProcessLoading } = useAsyncError();
  const { execute: executeHotStampingPlateFee, loading: hotStampingPlateFeeLoading } = useAsyncError();

  // Tab相关状态
  const [activeTab, setActiveTab] = useState('hotStampingProcess');

  // 烫金工艺相关状态
  const [hotStampingProcessData, setHotStampingProcessData] = useState<HotStampingProcess[]>([]);
  const [hotStampingProcessModalVisible, setHotStampingProcessModalVisible] = useState(false);
  const [hotStampingProcessEditingRecord, setHotStampingProcessEditingRecord] = useState<HotStampingProcess | null>(null);
  const [hotStampingProcessForm] = Form.useForm();
  const [hotStampingProcessTotal, setHotStampingProcessTotal] = useState(0);
  const [hotStampingProcessCurrent, setHotStampingProcessCurrent] = useState(1);
  const [hotStampingProcessPageSize, setHotStampingProcessPageSize] = useState(10);
  const [hotStampingProcessKeyword, setHotStampingProcessKeyword] = useState('');

  // 烫金版费相关状态
  const [hotStampingPlateFeeData, setHotStampingPlateFeeData] = useState<HotStampingPlateFee[]>([]);
  const [hotStampingPlateFeeModalVisible, setHotStampingPlateFeeModalVisible] = useState(false);
  const [hotStampingPlateFeeEditingRecord, setHotStampingPlateFeeEditingRecord] = useState<HotStampingPlateFee | null>(null);
  const [hotStampingPlateFeeForm] = Form.useForm();
  const [hotStampingPlateFeeTotal, setHotStampingPlateFeeTotal] = useState(0);
  const [hotStampingPlateFeeCurrent, setHotStampingPlateFeeCurrent] = useState(1);
  const [hotStampingPlateFeePageSize, setHotStampingPlateFeePageSize] = useState(10);
  const [hotStampingPlateFeeKeyword, setHotStampingPlateFeeKeyword] = useState('');

  // ===========================================
  // 烫金工艺相关方法
  // ===========================================

  // 获取烫金工艺列表
  const fetchHotStampingProcessList = async (page = hotStampingProcessCurrent, pageSize_ = hotStampingProcessPageSize, search = hotStampingProcessKeyword) => {
    const requestParams: any = {
      page,
      pageSize: pageSize_,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    };

    if (search) {
      requestParams.search = search;
    }

    const result = await executeHotStampingProcess(async () => {
      return await hotStampingProcessApi.getList(requestParams);
    }, '获取烫金工艺列表');

    if (result) {
      setHotStampingProcessData(result.list || []);
      setHotStampingProcessTotal(result.pagination?.total || 0);
    } else {
      setHotStampingProcessData([]);
      setHotStampingProcessTotal(0);
    }
  };

  // 处理烫金工艺搜索
  const handleHotStampingProcessSearch = () => {
    setHotStampingProcessCurrent(1);
    fetchHotStampingProcessList(1, hotStampingProcessPageSize, hotStampingProcessKeyword);
  };

  // 重置烫金工艺搜索
  const handleHotStampingProcessReset = () => {
    setHotStampingProcessKeyword('');
    setHotStampingProcessCurrent(1);
    fetchHotStampingProcessList(1, hotStampingProcessPageSize, '');
  };

  // 处理烫金工艺分页变化
  const handleHotStampingProcessTableChange = (pagination: any) => {
    setHotStampingProcessCurrent(pagination.current);
    setHotStampingProcessPageSize(pagination.pageSize);
    fetchHotStampingProcessList(pagination.current, pagination.pageSize);
  };

  // 添加烫金工艺
  const handleHotStampingProcessAdd = () => {
    setHotStampingProcessEditingRecord(null);
    setHotStampingProcessModalVisible(true);
    hotStampingProcessForm.resetFields();
    hotStampingProcessForm.setFieldsValue({
      salaryUnit: '元/张',
      materialUnit: '元/平方',
      salary: 0,
      materialPrice: 0,
      basePrice: 0,
    });
  };

  // 编辑烫金工艺
  const handleHotStampingProcessEdit = (record: HotStampingProcess) => {
    setHotStampingProcessEditingRecord(record);
    setHotStampingProcessModalVisible(true);
    hotStampingProcessForm.setFieldsValue({
      name: record.name,
      salary: record.salary,
      salaryUnit: record.salaryUnit,
      materialPrice: record.materialPrice,
      materialUnit: record.materialUnit,
      basePrice: record.basePrice,
      remark: record.remark || '',
    });
  };

  // 删除烫金工艺
  const handleHotStampingProcessDelete = async (id: number) => {
    const result = await executeHotStampingProcess(async () => {
      return await hotStampingProcessApi.delete(id);
    }, '删除烫金工艺');

    if (result) {
      message.success('删除烫金工艺成功');
      fetchHotStampingProcessList();
    }
  };

  // 提交烫金工艺表单
  const handleHotStampingProcessSubmit = async () => {
    try {
      const values = await hotStampingProcessForm.validateFields();
      const formData = {
        name: values.name,
        salary: Number(values.salary),
        salaryUnit: values.salaryUnit as HotStampingSalaryUnit,
        materialPrice: Number(values.materialPrice),
        materialUnit: values.materialUnit as HotStampingMaterialUnit,
        basePrice: Number(values.basePrice),
        remark: values.remark || undefined,
      };

      const result = await executeHotStampingProcess(async () => {
        if (hotStampingProcessEditingRecord) {
          return await hotStampingProcessApi.update({ id: hotStampingProcessEditingRecord.id, ...formData });
        } else {
          return await hotStampingProcessApi.create(formData);
        }
      }, hotStampingProcessEditingRecord ? '更新烫金工艺' : '创建烫金工艺');

      if (result) {
        message.success(hotStampingProcessEditingRecord ? '更新烫金工艺成功' : '创建烫金工艺成功');
        setHotStampingProcessModalVisible(false);
        fetchHotStampingProcessList();
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // ===========================================
  // 烫金版费相关方法
  // ===========================================

  // 获取烫金版费列表
  const fetchHotStampingPlateFeeList = async (page = hotStampingPlateFeeCurrent, pageSize_ = hotStampingPlateFeePageSize, search = hotStampingPlateFeeKeyword) => {
    const requestParams: any = {
      page,
      pageSize: pageSize_,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    };

    if (search) {
      requestParams.search = search;
    }

    const result = await executeHotStampingPlateFee(async () => {
      return await hotStampingPlateFeeApi.getList(requestParams);
    }, '获取烫金版费列表');

    if (result) {
      setHotStampingPlateFeeData(result.list || []);
      setHotStampingPlateFeeTotal(result.pagination?.total || 0);
    } else {
      setHotStampingPlateFeeData([]);
      setHotStampingPlateFeeTotal(0);
    }
  };

  // 处理烫金版费搜索
  const handleHotStampingPlateFeeSearch = () => {
    setHotStampingPlateFeeCurrent(1);
    fetchHotStampingPlateFeeList(1, hotStampingPlateFeePageSize, hotStampingPlateFeeKeyword);
  };

  // 重置烫金版费搜索
  const handleHotStampingPlateFeeReset = () => {
    setHotStampingPlateFeeKeyword('');
    setHotStampingPlateFeeCurrent(1);
    fetchHotStampingPlateFeeList(1, hotStampingPlateFeePageSize, '');
  };

  // 处理烫金版费分页变化
  const handleHotStampingPlateFeeTableChange = (pagination: any) => {
    setHotStampingPlateFeeCurrent(pagination.current);
    setHotStampingPlateFeePageSize(pagination.pageSize);
    fetchHotStampingPlateFeeList(pagination.current, pagination.pageSize);
  };

  // 添加烫金版费
  const handleHotStampingPlateFeeAdd = () => {
    setHotStampingPlateFeeEditingRecord(null);
    setHotStampingPlateFeeModalVisible(true);
    hotStampingPlateFeeForm.resetFields();
    hotStampingPlateFeeForm.setFieldsValue({
      price: 0,
      basePrice: 0,
      unit: '元/个',
    });
  };

  // 编辑烫金版费
  const handleHotStampingPlateFeeEdit = (record: HotStampingPlateFee) => {
    setHotStampingPlateFeeEditingRecord(record);
    setHotStampingPlateFeeModalVisible(true);
    hotStampingPlateFeeForm.setFieldsValue({
      name: record.name,
      price: record.price,
      unit: record.unit,
      basePrice: record.basePrice,
      remark: record.remark || '',
    });
  };

  // 删除烫金版费
  const handleHotStampingPlateFeeDelete = async (id: number) => {
    const result = await executeHotStampingPlateFee(async () => {
      return await hotStampingPlateFeeApi.delete(id);
    }, '删除烫金版费');

    if (result) {
      message.success('删除烫金版费成功');
      fetchHotStampingPlateFeeList();
    }
  };

  // 提交烫金版费表单
  const handleHotStampingPlateFeeSubmit = async () => {
    try {
      const values = await hotStampingPlateFeeForm.validateFields();
      const formData = {
        name: values.name,
        price: Number(values.price),
        unit: values.unit as HotStampingPlateFeeUnit,
        basePrice: Number(values.basePrice),
        remark: values.remark || undefined,
      };

      const result = await executeHotStampingPlateFee(async () => {
        if (hotStampingPlateFeeEditingRecord) {
          return await hotStampingPlateFeeApi.update({ id: hotStampingPlateFeeEditingRecord.id, ...formData });
        } else {
          return await hotStampingPlateFeeApi.create(formData);
        }
      }, hotStampingPlateFeeEditingRecord ? '更新烫金版费' : '创建烫金版费');

      if (result) {
        message.success(hotStampingPlateFeeEditingRecord ? '更新烫金版费成功' : '创建烫金版费成功');
        setHotStampingPlateFeeModalVisible(false);
        fetchHotStampingPlateFeeList();
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // ===========================================
  // Tab切换处理
  // ===========================================
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    if (key === 'hotStampingProcess' && hotStampingProcessData.length === 0) {
      fetchHotStampingProcessList();
    } else if (key === 'hotStampingPlateFee' && hotStampingPlateFeeData.length === 0) {
      fetchHotStampingPlateFeeList();
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchHotStampingProcessList();
  }, []);

  // ===========================================
  // 烫金工艺表格列定义
  // ===========================================
  const hotStampingProcessColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center' as const,
      width: 150,
    },
    {
      title: '工资',
      dataIndex: 'salary',
      key: 'salary',
      align: 'center' as const,
      width: 120,
      render: (salary: number, record: HotStampingProcess) => `${salary} ${record.salaryUnit}`,
    },
    {
      title: '材料价格',
      dataIndex: 'materialPrice',
      key: 'materialPrice',
      align: 'center' as const,
      width: 140,
      render: (materialPrice: number, record: HotStampingProcess) => `${materialPrice} ${record.materialUnit}`,
    },
    {
      title: '起步价',
      dataIndex: 'basePrice',
      key: 'basePrice',
      align: 'center' as const,
      width: 100,
      render: (basePrice: number) => basePrice > 0 ? `¥${basePrice.toFixed(2)}` : '-',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      align: 'center' as const,
      width: 200,
      ellipsis: true,
      render: (remark: string) => remark || '-',
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      align: 'center' as const,
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      key: 'action',
      align: 'center' as const,
      width: 150,
      render: (_: any, record: HotStampingProcess) => (
        <Space size="middle">
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleHotStampingProcessEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除"
            description="确定要删除这个烫金工艺吗？"
            onConfirm={() => handleHotStampingProcessDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              danger
              size="small"
              icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // ===========================================
  // 烫金版费表格列定义
  // ===========================================
  const hotStampingPlateFeeColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center' as const,
      width: 150,
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      align: 'center' as const,
      width: 120,
      render: (price: number, record: HotStampingPlateFee) => `${price} ${record.unit}`,
    },
    {
      title: '起步价',
      dataIndex: 'basePrice',
      key: 'basePrice',
      align: 'center' as const,
      width: 100,
      render: (basePrice: number) => basePrice > 0 ? `¥${basePrice.toFixed(2)}` : '-',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      align: 'center' as const,
      width: 200,
      ellipsis: true,
      render: (remark: string) => remark || '-',
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      align: 'center' as const,
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      key: 'action',
      align: 'center' as const,
      width: 150,
      render: (_: any, record: HotStampingPlateFee) => (
        <Space size="middle">
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleHotStampingPlateFeeEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除"
            description="确定要删除这个烫金版费吗？"
            onConfirm={() => handleHotStampingPlateFeeDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              danger
              size="small"
              icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>烫金工艺管理</Title>

      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        items={[
          {
            key: 'hotStampingProcess',
            label: '烫金工艺',
            children: (
              <Card>
                {/* 搜索和操作栏 */}
                <div style={{ marginBottom: 16 }}>
                  <Row gutter={16} align="middle">
                    <Col span={4}>
                      <Input
                        placeholder="搜索名称或备注"
                        prefix={<SearchOutlined />}
                        allowClear
                        value={hotStampingProcessKeyword}
                        onChange={(e) => setHotStampingProcessKeyword(e.target.value)}
                        onPressEnter={handleHotStampingProcessSearch}
                      />
                    </Col>
                    <Col>
                      <Button
                        type="primary"
                        icon={<SearchOutlined />}
                        onClick={handleHotStampingProcessSearch}
                      >
                        搜索
                      </Button>
                    </Col>
                    <Col>
                      <Button
                        icon={<ReloadOutlined />}
                        onClick={handleHotStampingProcessReset}
                      >
                        重置
                      </Button>
                    </Col>
                    <Col flex="auto" style={{ textAlign: 'right' }}>
                      <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={handleHotStampingProcessAdd}
                      >
                        添加烫金工艺
                      </Button>
                    </Col>
                  </Row>
                </div>

                {/* 表格 */}
                <Table
                  columns={hotStampingProcessColumns}
                  dataSource={hotStampingProcessData}
                  rowKey="id"
                  loading={hotStampingProcessLoading}
                  pagination={{
                    current: hotStampingProcessCurrent,
                    pageSize: hotStampingProcessPageSize,
                    total: hotStampingProcessTotal,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                  }}
                  onChange={handleHotStampingProcessTableChange}
                  bordered
                  size="middle"
                  scroll={{ x: 1000 }}
                  locale={{ emptyText: '暂无数据' }}
                />
              </Card>
            ),
          },
          {
            key: 'hotStampingPlateFee',
            label: '烫金版费',
            children: (
              <Card>
                {/* 搜索和操作栏 */}
                <div style={{ marginBottom: 16 }}>
                  <Row gutter={16} align="middle">
                    <Col span={4}>
                      <Input
                        placeholder="搜索名称或备注"
                        prefix={<SearchOutlined />}
                        allowClear
                        value={hotStampingPlateFeeKeyword}
                        onChange={(e) => setHotStampingPlateFeeKeyword(e.target.value)}
                        onPressEnter={handleHotStampingPlateFeeSearch}
                      />
                    </Col>
                    <Col>
                      <Button
                        type="primary"
                        icon={<SearchOutlined />}
                        onClick={handleHotStampingPlateFeeSearch}
                      >
                        搜索
                      </Button>
                    </Col>
                    <Col>
                      <Button
                        icon={<ReloadOutlined />}
                        onClick={handleHotStampingPlateFeeReset}
                      >
                        重置
                      </Button>
                    </Col>
                    <Col flex="auto" style={{ textAlign: 'right' }}>
                      <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={handleHotStampingPlateFeeAdd}
                      >
                        添加烫金版费
                      </Button>
                    </Col>
                  </Row>
                </div>

                {/* 表格 */}
                <Table
                  columns={hotStampingPlateFeeColumns}
                  dataSource={hotStampingPlateFeeData}
                  rowKey="id"
                  loading={hotStampingPlateFeeLoading}
                  pagination={{
                    current: hotStampingPlateFeeCurrent,
                    pageSize: hotStampingPlateFeePageSize,
                    total: hotStampingPlateFeeTotal,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                  }}
                  onChange={handleHotStampingPlateFeeTableChange}
                  bordered
                  size="middle"
                  scroll={{ x: 800 }}
                  locale={{ emptyText: '暂无数据' }}
                />
              </Card>
            ),
          },
        ]}
      />

      {/* 烫金工艺表单弹窗 */}
      <Modal
        title={hotStampingProcessEditingRecord ? '编辑烫金工艺' : '新增烫金工艺'}
        open={hotStampingProcessModalVisible}
        onOk={handleHotStampingProcessSubmit}
        onCancel={() => setHotStampingProcessModalVisible(false)}
        width={600}
        okText="确定"
        cancelText="取消"
      >
        <Form
          form={hotStampingProcessForm}
          layout="vertical"
          requiredMark={false}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="名称"
                name="name"
                rules={[{ required: true, message: '请输入烫金工艺名称' }]}
              >
                <Input placeholder="请输入烫金工艺名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="起步价"
                name="basePrice"
                rules={[{ required: true, message: '请输入起步价' }]}
              >
                <InputNumber
                  placeholder="请输入起步价"
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="工资"
                name="salary"
                rules={[{ required: true, message: '请输入工资' }]}
              >
                <InputNumber
                  placeholder="请输入工资"
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  addonAfter={
                    <Form.Item name="salaryUnit" noStyle>
                      <Select style={{ width: 90 }}>
                        {HOT_STAMPING_SALARY_UNITS.map(unit => (
                          <Option key={unit} value={unit}>{unit}</Option>
                        ))}
                      </Select>
                    </Form.Item>
                  }
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="材料价格"
                name="materialPrice"
                rules={[{ required: true, message: '请输入材料价格' }]}
              >
                <InputNumber
                  placeholder="请输入材料价格"
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  addonAfter={
                    <Form.Item name="materialUnit" noStyle>
                      <Select style={{ width: 100 }}>
                        {HOT_STAMPING_MATERIAL_UNITS.map(unit => (
                          <Option key={unit} value={unit}>{unit}</Option>
                        ))}
                      </Select>
                    </Form.Item>
                  }
                />
              </Form.Item>
            </Col>

          </Row>
          <Form.Item label="备注" name="remark">
            <TextArea placeholder="请输入备注" rows={3} />
          </Form.Item>
        </Form>
      </Modal>

      {/* 烫金版费表单弹窗 */}
      <Modal
        title={hotStampingPlateFeeEditingRecord ? '编辑烫金版费' : '新增烫金版费'}
        open={hotStampingPlateFeeModalVisible}
        onOk={handleHotStampingPlateFeeSubmit}
        onCancel={() => setHotStampingPlateFeeModalVisible(false)}
        width={600}
        okText="确定"
        cancelText="取消"
      >
        <Form
          form={hotStampingPlateFeeForm}
          layout="vertical"
          requiredMark={false}
        >
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="名称"
                name="name"
                rules={[{ required: true, message: '请输入烫金版费名称' }]}
              >
                <Input placeholder="请输入烫金版费名称" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="价格"
                name="price"
                rules={[{ required: true, message: '请输入价格' }]}
              >
                <InputNumber
                  placeholder="请输入价格"
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  addonAfter={
                    <Form.Item name="unit" noStyle>
                      <Select style={{ width: 100 }}>
                        {HOT_STAMPING_PLATE_FEE_UNITS.map(unit => (
                          <Option key={unit} value={unit}>{unit}</Option>
                        ))}
                      </Select>
                    </Form.Item>
                  }
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="起步价"
                name="basePrice"
                rules={[{ required: true, message: '请输入起步价' }]}
              >
                <InputNumber
                  placeholder="请输入起步价"
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item label="备注" name="remark">
            <TextArea placeholder="请输入备注" rows={3} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
} 