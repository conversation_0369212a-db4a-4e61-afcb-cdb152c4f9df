import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { updateSpecialPaperSchema, validateSpecialSize, UpdateSpecialPaperParams } from '@/lib/validations/admin/specialPaper';
import { withValidation, assert, assertExists } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation<UpdateSpecialPaperParams>(
  updateSpecialPaperSchema,
  async (request: NextRequest, validatedData: UpdateSpecialPaperParams) => {
    // 特规尺寸条件验证
    const specialSizeValidation = validateSpecialSize(validatedData);
    assert(
      specialSizeValidation.success,
      ErrorCode.INVALID_PARAMETERS,
      specialSizeValidation.error || '特规尺寸验证失败'
    );

    // 检查记录是否存在
    const existingMaterial = await prisma.specialPaper.findUnique({
      where: { id: validatedData.id, isDel: false },
    });
    assertExists(existingMaterial, ErrorCode.MATERIAL_NOT_FOUND, '特种纸不存在');

    // 检查名称重复（排除自己）
    const duplicateName = await prisma.specialPaper.findFirst({
      where: {
        name: validatedData.name,
        id: { not: validatedData.id },
        isDel: false,
      },
    });
    assert(!duplicateName, ErrorCode.MATERIAL_NAME_EXISTS, '特种纸名称已存在');

    // 更新特种纸
    const specialPaper = await prisma.specialPaper.update({
      where: { id: validatedData.id },
      data: {
        name: validatedData.name,
        price: validatedData.price,
        unit: validatedData.unit,
        weight: validatedData.weight,
        thickness: validatedData.thickness,
        isRegular: validatedData.isRegular,
        isLarge: validatedData.isLarge,
        isSpecial: validatedData.isSpecial,
        size1: validatedData.isSpecial ? validatedData.size1 : null,
        size2: validatedData.isSpecial ? validatedData.size2 : null,
        category: validatedData.category,
        remark: validatedData.remark || null,
        updatedAt: new Date(),
      },
    });

    return successResponse(specialPaper, '更新特种纸成功');
  }
); 