/**
 * PDF导出和打印工具
 * 用于生成报价单PDF文件和打印功能
 */

import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { CalculationState } from '../types/calculation';
import { perfLog } from '@/lib/utils/perfLog';
import {
  getSpecDisplayName,
  getChineseUnit,
  formatDimensions,
  getMaterialTypeDisplayName
} from '../util';

export interface PDFExportOptions {
  filename?: string;
  format?: 'a4' | 'letter';
  orientation?: 'portrait' | 'landscape';
  margin?: number;
  quality?: number;
}

/**
 * 导出报价单为PDF
 */
export const exportQuotationToPDF = async (
  state: CalculationState,
  options: PDFExportOptions = {}
): Promise<void> => {
  try {
    const {
      filename = `报价单_${state.basicInfo.name || '未命名项目'}_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.pdf`,
      format = 'a4',
      orientation = 'portrait',
      margin = 20,
      quality = 1.0
    } = options;

    perfLog.debug('开始生成PDF:', { filename, format, orientation });

    // 创建PDF文档
    const pdf = new jsPDF({
      orientation,
      unit: 'mm',
      format
    });

    // 设置中文字体支持
    pdf.setFont('helvetica');
    
    // 添加标题
    pdf.setFontSize(20);
    pdf.text('盒型计算报价单', margin, margin + 10);
    
    let yPosition = margin + 25;
    
    // 基础信息
    pdf.setFontSize(14);
    pdf.text('基础信息', margin, yPosition);
    yPosition += 10;
    
    pdf.setFontSize(10);
    pdf.text(`项目名称: ${state.basicInfo.name || '未命名项目'}`, margin + 5, yPosition);
    yPosition += 6;
    pdf.text(`数量: ${state.basicInfo.quantity.toLocaleString()} 个`, margin + 5, yPosition);
    yPosition += 6;
    
    yPosition += 10;
    
    // 费用汇总
    pdf.setFontSize(14);
    pdf.text('费用汇总', margin, yPosition);
    yPosition += 10;
    
    pdf.setFontSize(10);
    const costItems = [
      { name: '材料费用', amount: state.quotation.materialCost },
      { name: '工艺费用', amount: state.quotation.processCost },
      { name: '配件费用', amount: state.quotation.accessoryCost },
      { name: '加工费费用', amount: state.quotation.processingFeeCost },
      { name: '自定义费用', amount: state.quotation.formulaCost }
    ];
    
    costItems.forEach(item => {
      if (item.amount > 0) {
        pdf.text(`${item.name}: ¥${item.amount.toFixed(2)}`, margin + 5, yPosition);
        yPosition += 6;
      }
    });
    
    yPosition += 5;
    
    // 总计
    pdf.setFontSize(12);
    pdf.text(`总计: ¥${state.quotation.totalCost.toFixed(2)}`, margin + 5, yPosition);
    yPosition += 8;
    pdf.text(`单价: ¥${(state.quotation.totalCost / state.basicInfo.quantity).toFixed(2)}/个`, margin + 5, yPosition);
    
    // 添加生成时间
    yPosition += 20;
    pdf.setFontSize(8);
    pdf.text(`生成时间: ${new Date().toLocaleString('zh-CN')}`, margin, yPosition);
    
    // 保存PDF
    pdf.save(filename);
    
    perfLog.debug('PDF生成完成:', filename);
    
  } catch (error) {
    perfLog.error('PDF导出失败:', error);
    throw new Error('PDF导出失败');
  }
};

/**
 * 使用HTML转换方式导出PDF（更好的样式支持）
 */
export const exportQuotationToPDFFromHTML = async (
  htmlContent: string,
  options: PDFExportOptions = {}
): Promise<void> => {
  try {
    const {
      filename = `报价单_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.pdf`,
      format = 'a4',
      orientation = 'portrait',
      quality = 1.0
    } = options;

    // 创建临时容器
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;
    tempDiv.style.position = 'absolute';
    tempDiv.style.left = '-9999px';
    tempDiv.style.top = '-9999px';
    tempDiv.style.width = '210mm'; // A4宽度
    tempDiv.style.backgroundColor = 'white';
    tempDiv.style.padding = '20mm';
    document.body.appendChild(tempDiv);

    try {
      // 使用html2canvas转换为图片
      const canvas = await html2canvas(tempDiv, {
        scale: quality,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
      });

      // 创建PDF
      const pdf = new jsPDF({
        orientation,
        unit: 'mm',
        format
      });

      const imgData = canvas.toDataURL('image/png');
      const imgWidth = format === 'a4' ? 210 : 216; // A4 or Letter width in mm
      const pageHeight = format === 'a4' ? 297 : 279; // A4 or Letter height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;

      let position = 0;

      // 添加第一页
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      // 如果内容超过一页，添加更多页面
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      // 保存PDF
      pdf.save(filename);
      
      perfLog.debug('HTML转PDF完成:', filename);
      
    } finally {
      // 清理临时元素
      document.body.removeChild(tempDiv);
    }
    
  } catch (error) {
    perfLog.error('HTML转PDF失败:', error);
    throw new Error('PDF导出失败');
  }
};

/**
 * 生成报价单的详细HTML内容（用于PDF导出）
 */
export const generateDetailedQuotationHTML = (state: CalculationState): string => {
  return generateSharedQuotationHTML(state, PDF_FORMAT_CONFIG, generatePDFStyles);
};

/**
 * 共享的报价单生成工具函数
 */

// 格式配置接口
interface QuotationFormatConfig {
  specFontSize: string;  // 规格参数字体大小
  unitConverter: (unit: string) => string;  // 单位转换函数
}

// PDF格式配置
const PDF_FORMAT_CONFIG: QuotationFormatConfig = {
  specFontSize: '9px',
  unitConverter: (unit: string) => {
    if (unit.includes('元/')) {
      const quantityUnit = unit.replace('元/', '');
      const unitMap: Record<string, string> = {
        '张': '张',
        '平方': '㎡',
        '个': '个',
        '条': '条',
        '对': '对',
        '米': '米',
        '立方': '㎥'
      };
      return unitMap[quantityUnit] || quantityUnit;
    }
    return unit;
  }
};

// 打印格式配置
const PRINT_FORMAT_CONFIG: QuotationFormatConfig = {
  specFontSize: '10px',
  unitConverter: getChineseUnit
};

// 材料尺寸本地化函数
export const formatMaterialSize = (size: { width: number; height: number } | undefined): string => {
  if (!size) return '未知尺寸';
  return formatDimensions(size, 'mm', 0);
};

// 材料规格本地化函数
export const formatMaterialSpec = (spec: string): string => {
  return getSpecDisplayName(spec);
};

// 材料类型本地化函数
export const formatMaterialType = (type: string): string => {
  return getMaterialTypeDisplayName(type);
};

// 统一的部件组名称获取函数
export const getPartGroupDisplayName = (state: CalculationState, key: string): string => {
  // 优先从实际配置的部件组中获取名称
  const facePartGroups = state.packagingConfig.facePartGroups || [];
  const greyPartGroups = state.packagingConfig.greyPartGroups || [];
  const allPartGroups = [...facePartGroups, ...greyPartGroups];

  const partGroup = allPartGroups.find(pg => pg.id === key);
  if (partGroup && partGroup.name) {
    return partGroup.name;
  }

  // 其次从工艺配置中获取部件组名称
  const processConfig = state.processConfig.partGroupProcessConfigs?.[key];
  if (processConfig?.partGroupName) {
    return processConfig.partGroupName;
  }

  // 最后使用预定义的中文映射表作为fallback
  const keyToChineseMap: Record<string, string> = {
    'face_merged_group': '面纸合并组',
    'grey_merged_group': '灰板合并组',
    'face_group': '面纸组',
    'grey_group': '灰板组',
    'face_part_group': '面纸部件组',
    'grey_part_group': '灰板部件组',
    'merged_face_group': '合并面纸组',
    'merged_grey_group': '合并灰板组'
  };

  return keyToChineseMap[key] || key;
};

/**
 * 共享的材料费用详情生成函数
 */
export const generateSharedMaterialDetails = (state: CalculationState, config: QuotationFormatConfig): string => {
  const existingMaterialCosts = (state.packagingConfig as any)?.materialCostDetails;
  if (existingMaterialCosts && Array.isArray(existingMaterialCosts) && existingMaterialCosts.length > 0) {
    return existingMaterialCosts.map((detail: any, index: number) => `
      <tr>
        <td>${index + 1}</td>
        <td>${detail.materialName}</td>
        <td>${formatMaterialSpec(detail.specification || 'unknown')}</td>
        <td>${(detail.quantity || 0).toLocaleString()}</td>
        <td>${config.unitConverter(detail.unit || '张')}</td>
        <td>¥${(detail.unitPrice || 0).toFixed(2)}</td>
        <td class="amount">¥${(detail.totalCost || 0).toFixed(2)}</td>
      </tr>
    `).join('');
  }
  return '<tr><td colspan="7" style="text-align: center; color: #999;">暂无材料费用详情</td></tr>';
};

/**
 * 共享的工艺费用详情生成函数
 */
export const generateSharedProcessDetails = (state: CalculationState, config: QuotationFormatConfig): string => {
  if (!state.processConfig.partGroupProcessConfigs) {
    return '<tr><td colspan="8" style="text-align: center; color: #999;">暂无工艺费用详情</td></tr>';
  }

  const processRows: string[] = [];
  let processIndex = 1;

  Object.entries(state.processConfig.partGroupProcessConfigs).forEach(([partGroupKey, configData]) => {
    const processTypes = ['printing', 'laminating', 'surfaceProcess', 'silkScreen', 'hotStamping', 'embossing', 'dieCutting', 'dieCuttingPlateFee'];
    const processNames: Record<string, string> = {
      printing: '印刷',
      laminating: '对裱',
      surfaceProcess: '覆膜',
      silkScreen: '丝印',
      hotStamping: '烫金',
      embossing: '凹凸',
      dieCutting: '模切',
      dieCuttingPlateFee: '刀版费'
    };

    // 获取部件组的中文显示名称
    const partGroupDisplayName = getPartGroupDisplayName(state, partGroupKey);

    processTypes.forEach(processType => {
      const configAsAny = configData as any;
      const processes = configAsAny[processType];
      if (processes && Array.isArray(processes) && processes.length > 0) {
        processes.forEach((process: any) => {
          // 获取工艺名称和规格
          let processName = '未知工艺';
          let processSpec = '';

          if (processType === 'printing') {
            processName = process.name || process.machineModel || '未知印刷机型';
            const specs: string[] = [];
            if (process.parameters?.machineModel) {
              specs.push(`机型: ${process.parameters.machineModel}`);
            }
            if (process.parameters?.colorCount && process.parameters.colorCount > 0) {
              specs.push(`${process.parameters.colorCount}色`);
            }
            if (process.parameters?.ctpPlateFee && process.parameters.ctpPlateFee > 0) {
              specs.push(`CTP板费: ¥${process.parameters.ctpPlateFee.toFixed(2)}`);
            }
            processSpec = specs.join('; ');
          } else if (processType === 'hotStamping') {
            processName = process.name || '未知烫金类型';
            const specs: string[] = [];
            if (process.hotStampingDimensions) {
              const dims = process.hotStampingDimensions;
              specs.push(`尺寸: ${dims.length}×${dims.width}mm`);
              specs.push(`面积: ${((dims.length * dims.width) / 1000000).toFixed(4)}㎡`);
            }
            if (process.parameters?.materialPrice && process.parameters.materialPrice > 0) {
              specs.push(`材料价: ¥${process.parameters.materialPrice.toFixed(2)}/㎡`);
            }
            processSpec = specs.join('; ');
          } else if (processType === 'embossing') {
            processName = process.name || '未知凹凸工艺';
            const specs: string[] = [];
            if (process.embossingDimensions) {
              const dims = process.embossingDimensions;
              specs.push(`凹凸: ${dims.length}×${dims.width}mm`);
            }
            if (process.hydraulicDimensions) {
              const dims = process.hydraulicDimensions;
              specs.push(`液压: ${dims.length}×${dims.width}mm`);
            }
            processSpec = specs.join('; ');
          } else if (processType === 'laminating') {
            processName = process.name || '未知对裱工艺';
            const specs: string[] = [];
            if (process.parameters?.materialType) {
              specs.push(`材料: ${process.parameters.materialType}`);
            }
            if (process.parameters?.thickness) {
              specs.push(`厚度: ${process.parameters.thickness}mm`);
            }
            processSpec = specs.join('; ');
          } else if (processType === 'surfaceProcess') {
            processName = process.name || '未知覆膜工艺';
            const specs: string[] = [];
            if (process.parameters?.filmType) {
              specs.push(`膜类型: ${process.parameters.filmType}`);
            }
            if (process.parameters?.thickness) {
              specs.push(`厚度: ${process.parameters.thickness}mm`);
            }
            processSpec = specs.join('; ');
          } else if (processType === 'silkScreen') {
            processName = process.name || '未知丝印工艺';
            const specs: string[] = [];
            if (process.parameters?.inkType) {
              specs.push(`油墨: ${process.parameters.inkType}`);
            }
            if (process.parameters?.colorCount && process.parameters.colorCount > 0) {
              specs.push(`${process.parameters.colorCount}色`);
            }
            processSpec = specs.join('; ');
          } else if (processType === 'dieCutting') {
            processName = process.name || '未知模切工艺';
            const specs: string[] = [];
            if (process.parameters?.machineType) {
              specs.push(`机型: ${process.parameters.machineType}`);
            }
            if (process.parameters?.difficulty) {
              specs.push(`难度: ${process.parameters.difficulty}`);
            }
            processSpec = specs.join('; ');
          } else if (processType === 'dieCuttingPlateFee') {
            processName = process.name || '未知刀版费';
            const specs: string[] = [];
            if (process.parameters?.plateType) {
              specs.push(`版型: ${process.parameters.plateType}`);
            }
            if (process.parameters?.area && process.parameters.area > 0) {
              specs.push(`面积: ${process.parameters.area.toFixed(2)}㎡`);
            }
            processSpec = specs.join('; ');
          } else {
            processName = process.name || process.processName || process.machineModel || processNames[processType];
            // 通用参数提取
            const specs: string[] = [];
            if (process.parameters?.setupFee && process.parameters.setupFee > 0) {
              specs.push(`开机费: ¥${process.parameters.setupFee.toFixed(2)}`);
            }
            processSpec = specs.join('; ');
          }

          const quantity = process.quantity || 0;
          const unitPrice = process.unitPrice || 0;
          const totalPrice = process.totalPrice || 0;
          const originalUnit = process.unit || '个';
          const quantityUnit = config.unitConverter(originalUnit);

          processRows.push(`
            <tr>
              <td>${processIndex}</td>
              <td>${partGroupDisplayName}</td>
              <td>${processNames[processType]}</td>
              <td>${processName}</td>
              <td style="font-size: ${config.specFontSize};">${processSpec || '-'}</td>
              <td>${quantity.toLocaleString()} ${quantityUnit}</td>
              <td>¥${unitPrice.toFixed(2)}/${quantityUnit}</td>
              <td class="amount">¥${totalPrice.toFixed(2)}</td>
            </tr>
          `);
          processIndex++;
        });
      }
    });
  });

  return processRows.length > 0 ? processRows.join('') : '<tr><td colspan="8" style="text-align: center; color: #999;">暂无工艺费用详情</td></tr>';
};

/**
 * 共享的配件费用详情生成函数
 */
export const generateSharedAccessoryDetails = (state: CalculationState, config: QuotationFormatConfig): string => {
  const accessoryRows: string[] = [];
  let accessoryIndex = 1;

  // 普通配件
  if (state.accessoryConfig.accessories && state.accessoryConfig.accessories.length > 0) {
    state.accessoryConfig.accessories.forEach((accessory: any) => {
      const materialName = accessory.materialName || accessory.name || accessory.material?.name || accessory.material?.materialName || accessory.processName || '未知配件';
      const quantity = accessory.quantity || 0;
      const originalUnit = accessory.unit || '个';
      const unitPrice = accessory.unitPrice || 0;
      const totalPrice = accessory.totalPrice || 0;
      const quantityUnit = config.unitConverter(originalUnit);

      // 提取配件规格参数
      let accessorySpec = '';
      if (accessory.parameters) {
        const params = accessory.parameters;
        const specs: string[] = [];

        if (params.length && params.width) {
          specs.push(`规格: ${params.length}×${params.width}mm`);
        }
        if (params.metersPerBox) {
          specs.push(`每盒: ${params.metersPerBox}米`);
        }
        if (params.thickness) {
          specs.push(`厚度: ${params.thickness}mm`);
        }
        if (params.materialType) {
          specs.push(`材料: ${params.materialType}`);
        }

        accessorySpec = specs.join('; ');
      }

      accessoryRows.push(`
        <tr>
          <td>${accessoryIndex}</td>
          <td>普通配件</td>
          <td>${materialName}</td>
          <td style="font-size: ${config.specFontSize};">${accessorySpec || '-'}</td>
          <td>${quantity.toLocaleString()} ${quantityUnit}</td>
          <td>¥${unitPrice.toFixed(2)}/${quantityUnit}</td>
          <td class="amount">¥${totalPrice.toFixed(2)}</td>
        </tr>
      `);
      accessoryIndex++;
    });
  }

  // 礼盒配件
  if (state.accessoryConfig.giftBoxAccessories && state.accessoryConfig.giftBoxAccessories.length > 0) {
    state.accessoryConfig.giftBoxAccessories.forEach((accessory: any) => {
      const materialName = accessory.materialName || accessory.name || accessory.material?.name || accessory.material?.materialName || accessory.processName || '未知配件';
      const quantity = accessory.quantity || 0;
      const originalUnit = accessory.unit || '个';
      const unitPrice = accessory.unitPrice || 0;
      const totalPrice = accessory.totalPrice || 0;
      const quantityUnit = config.unitConverter(originalUnit);

      // 提取礼盒配件规格参数
      let giftBoxSpec = '';
      const specs: string[] = [];

      // 根据单位推断计算方式
      const unit = accessory.unit || '个';
      if (unit === '元/立方') {
        specs.push('按体积计算');

        // 显示计算数量（体积）
        if (accessory.quantity) {
          specs.push(`材料体积: ${accessory.quantity.toFixed(4)}立方米`);
        }

        // 显示库存尺寸（如果使用现货尺寸）
        if (accessory.material?.isStockSize && accessory.material?.stockLength && accessory.material?.stockWidth) {
          specs.push(`库存: ${accessory.material.stockLength}×${accessory.material.stockWidth}mm`);
        }
      } else if (unit === '元/平方') {
        specs.push('按表面积计算');

        // 显示计算数量（表面积）
        if (accessory.quantity) {
          specs.push(`表面积: ${accessory.quantity.toFixed(4)}平方米`);
        }
      }

      // 显示用户输入的盒子尺寸
      if (accessory.parameters) {
        const params = accessory.parameters;
        if (params.length && params.width && params.height) {
          specs.push(`盒子: ${params.length}×${params.width}×${params.height}mm`);
        }
        if (params.spacing) {
          specs.push(`间距: ${params.spacing}mm`);
        }
      }

      // 显示基础配件信息
      if (accessory.material?.isStockSize) {
        specs.push('现货尺寸');
      }

      giftBoxSpec = specs.join('; ');

      accessoryRows.push(`
        <tr>
          <td>${accessoryIndex}</td>
          <td>礼盒配件</td>
          <td>${materialName}</td>
          <td style="font-size: ${config.specFontSize};">${giftBoxSpec || '-'}</td>
          <td>${quantity.toLocaleString()} ${quantityUnit}</td>
          <td>¥${unitPrice.toFixed(2)}/${quantityUnit}</td>
          <td class="amount">¥${totalPrice.toFixed(2)}</td>
        </tr>
      `);
      accessoryIndex++;
    });
  }

  return accessoryRows.length > 0 ? accessoryRows.join('') : '<tr><td colspan="8" style="text-align: center; color: #999;">暂无配件费用详情</td></tr>';
};

/**
 * 共享的加工费费用详情生成函数
 */
export const generateSharedProcessingFeeDetails = (state: CalculationState, config: QuotationFormatConfig): string => {
  const processingFeeRows: string[] = [];
  let processingFeeIndex = 1;

  // 可选加工费
  if (state.processingFeeConfig?.customFees && state.processingFeeConfig.customFees.length > 0) {
    state.processingFeeConfig.customFees.forEach((fee) => {
      const quantityUnit = fee.unit.replace('元/', '');
      const chineseUnit = config.unitConverter(quantityUnit);

      processingFeeRows.push(`
        <tr>
          <td>${processingFeeIndex}</td>
          <td>${fee.name}</td>
          <td>${fee.quantity.toLocaleString()} ${chineseUnit}</td>
          <td>¥${fee.unitPrice.toFixed(2)}/${chineseUnit}</td>
          <td class="amount">¥${fee.totalPrice.toFixed(2)}</td>
        </tr>
      `);
      processingFeeIndex++;
    });
  }

  // 固定参数选项加工费
  if (state.processingFeeConfig?.fixedFees && state.processingFeeConfig.fixedFees.length > 0) {
    state.processingFeeConfig.fixedFees.forEach((fee) => {
      const quantityUnit = fee.unit.replace('元/', '');
      const chineseUnit = config.unitConverter(quantityUnit);

      processingFeeRows.push(`
        <tr>
          <td>${processingFeeIndex}</td>
          <td>${fee.name}</td>
          <td>${fee.quantity.toLocaleString()} ${chineseUnit}</td>
          <td>¥${fee.unitPrice.toFixed(2)}/${chineseUnit}</td>
          <td class="amount">¥${fee.totalPrice.toFixed(2)}</td>
        </tr>
      `);
      processingFeeIndex++;
    });
  }

  return processingFeeRows.length > 0 ? processingFeeRows.join('') : '<tr><td colspan="7" style="text-align: center; color: #999;">暂无加工费费用详情</td></tr>';
};

/**
 * 生成PDF样式的CSS
 */
const generatePDFStyles = (): string => `
  <style>
    body {
      font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif;
      font-size: 12px;
      line-height: 1.4;
      color: #333;
      margin: 0;
      padding: 20px;
      background: white;
    }
    .header {
      text-align: center;
      border-bottom: 2px solid #1890ff;
      padding: 20px 0;
      margin-bottom: 25px;
    }
    .header h1 {
      color: #1890ff;
      margin: 0 0 10px 0;
      font-size: 24px;
      font-weight: bold;
    }
    .header .subtitle {
      color: #666;
      font-size: 12px;
      margin: 5px 0;
    }
    .section {
      margin-bottom: 20px;
    }
    .section-title {
      font-weight: bold;
      font-size: 14px;
      color: #1890ff;
      border-left: 4px solid #1890ff;
      padding: 8px 12px;
      margin-bottom: 12px;
      background: #f0f8ff;
    }
    .basic-info {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
      margin-bottom: 15px;
    }
    .info-item {
      padding: 10px;
      background: #f9f9f9;
      border-radius: 3px;
      border-left: 3px solid #1890ff;
    }
    .info-label {
      font-weight: bold;
      color: #666;
      margin-bottom: 3px;
      font-size: 11px;
    }
    .info-value {
      color: #333;
      font-size: 12px;
    }
    .detail-table {
      width: 100%;
      border-collapse: collapse;
      margin: 12px 0;
      font-size: 10px;
    }
    .detail-table th,
    .detail-table td {
      border: 1px solid #ddd;
      padding: 6px 4px;
      text-align: left;
    }
    .detail-table th {
      background-color: #1890ff;
      color: white;
      font-weight: bold;
      text-align: center;
      font-size: 10px;
    }
    .detail-table .amount {
      text-align: right;
      font-weight: bold;
      color: #1890ff;
    }
    .detail-table .center {
      text-align: center;
    }
    .summary-table {
      width: 100%;
      border-collapse: collapse;
      margin: 15px 0;
      font-size: 11px;
    }
    .summary-table th,
    .summary-table td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    .summary-table th {
      background-color: #f5f5f5;
      font-weight: bold;
    }
    .summary-table .amount {
      text-align: right;
      font-weight: bold;
      color: #1890ff;
    }
    .total-section {
      border: 2px solid #1890ff;
      border-radius: 6px;
      padding: 15px;
      margin-top: 25px;
      background: #f0f8ff;
    }
    .total-amount {
      font-size: 18px;
      font-weight: bold;
      color: #1890ff;
      text-align: center;
      margin: 10px 0;
      padding: 12px;
      border: 2px dashed #1890ff;
      border-radius: 4px;
      background: white;
    }
    .footer {
      margin-top: 30px;
      text-align: center;
      font-size: 10px;
      color: #666;
      border-top: 1px solid #ddd;
      padding-top: 15px;
    }
  </style>
`;

/**
 * 生成打印样式的CSS
 */
const generatePrintStyles = (): string => `
  <style>
    @media print {
      @page {
        margin: 12mm;
        size: A4 portrait;
      }
      body {
        font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif;
        font-size: 12px;
        line-height: 1.4;
        color: #000;
        margin: 0;
        padding: 0;
        background: white;
      }
      .no-print {
        display: none !important;
      }
      .page-break {
        page-break-before: always;
      }
      .avoid-break {
        page-break-inside: avoid;
      }
      .header {
        background: none !important;
        border-radius: 0 !important;
        padding: 15px 0 !important;
        margin-bottom: 20px !important;
      }
      .header h1 {
        font-size: 22px !important;
        color: #000 !important;
      }
      .header .subtitle {
        font-size: 12px !important;
        color: #333 !important;
      }
      .section-title {
        background: none !important;
        border-radius: 0 !important;
        color: #000 !important;
        font-size: 14px !important;
        padding: 8px 0 !important;
        border-left: 3px solid #000 !important;
        padding-left: 10px !important;
      }
      .detail-table {
        font-size: 10px !important;
      }
      .detail-table th {
        background-color: #f0f0f0 !important;
        color: #000 !important;
      }
      .total-amount {
        color: #000 !important;
        font-size: 16px !important;
      }
      .summary-table {
        font-size: 11px !important;
      }
    }

    /* 屏幕显示样式 */
    body {
      font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif;
      font-size: 13px;
      line-height: 1.6;
      color: #333;
      margin: 20px;
      padding: 0;
      background: white;
    }
    .header {
      text-align: center;
      border-bottom: 3px solid #1890ff;
      padding: 25px;
      margin-bottom: 30px;
      background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%);
      border-radius: 8px;
    }
    .header h1 {
      color: #1890ff;
      margin: 0 0 15px 0;
      font-size: 28px;
      font-weight: bold;
    }
    .header .subtitle {
      color: #666;
      font-size: 14px;
      margin: 8px 0;
    }
    .section {
      margin-bottom: 25px;
    }
    .section-title {
      font-weight: bold;
      font-size: 16px;
      color: #1890ff;
      border-left: 5px solid #1890ff;
      padding: 10px 15px;
      margin-bottom: 15px;
      background: #f0f8ff;
      border-radius: 4px;
    }
    .basic-info {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;
      margin-bottom: 20px;
    }
    .info-item {
      padding: 12px;
      background: #f9f9f9;
      border-radius: 4px;
      border-left: 3px solid #1890ff;
    }
    .info-label {
      font-weight: bold;
      color: #666;
      margin-bottom: 5px;
    }
    .info-value {
      color: #333;
      font-size: 14px;
    }
    .detail-table {
      width: 100%;
      border-collapse: collapse;
      margin: 15px 0;
      font-size: 11px;
    }
    .detail-table th,
    .detail-table td {
      border: 1px solid #ddd;
      padding: 8px 6px;
      text-align: left;
    }
    .detail-table th {
      background-color: #1890ff;
      color: white;
      font-weight: bold;
      text-align: center;
    }
    .detail-table .amount {
      text-align: right;
      font-weight: bold;
      color: #1890ff;
    }
    .detail-table .center {
      text-align: center;
    }
    .summary-table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
      font-size: 12px;
    }
    .summary-table th,
    .summary-table td {
      border: 1px solid #ddd;
      padding: 12px;
      text-align: left;
    }
    .summary-table th {
      background-color: #f5f5f5;
      font-weight: bold;
    }
    .summary-table .amount {
      text-align: right;
      font-weight: bold;
      color: #1890ff;
    }
    .total-section {
      border: 2px solid #1890ff;
      border-radius: 8px;
      padding: 20px;
      margin-top: 30px;
      background: #f0f8ff;
    }
    .total-amount {
      font-size: 20px;
      font-weight: bold;
      color: #1890ff;
      text-align: center;
      margin: 15px 0;
      padding: 15px;
      border: 2px dashed #1890ff;
      border-radius: 6px;
      background: white;
    }
    .footer {
      margin-top: 40px;
      text-align: center;
      font-size: 11px;
      color: #666;
      border-top: 1px solid #ddd;
      padding-top: 20px;
    }
  </style>
`;

/**
 * 共享的基础HTML生成函数
 */
export const generateSharedQuotationHTML = (
  state: CalculationState,
  config: QuotationFormatConfig,
  styleGenerator: () => string
): string => {
  return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>盒型计算报价单 - ${state.basicInfo.name || '未命名项目'}</title>
    ${styleGenerator()}
</head>
<body>
    <div class="header avoid-break">
        <h1>盒型计算报价单</h1>
        <div class="subtitle">项目名称：${state.basicInfo.name || '未命名项目'}</div>
        <div class="subtitle">生产数量：${state.basicInfo.quantity.toLocaleString()} 个</div>
        <div class="subtitle">生成时间：${new Date().toLocaleString('zh-CN')}</div>
    </div>

    <div class="section avoid-break">
        <div class="section-title">项目基础信息</div>
        <div class="basic-info">
            <div class="info-item">
                <div class="info-label">项目名称</div>
                <div class="info-value">${state.basicInfo.name || '未命名项目'}</div>
            </div>
            <div class="info-item">
                <div class="info-label">生产数量</div>
                <div class="info-value">${state.basicInfo.quantity.toLocaleString()} 个</div>
            </div>
            <div class="info-item">
                <div class="info-label">总费用</div>
                <div class="info-value">¥${state.quotation.totalCost.toFixed(2)}</div>
            </div>
            <div class="info-item">
                <div class="info-label">单价</div>
                <div class="info-value">¥${(state.quotation.totalCost / state.basicInfo.quantity).toFixed(2)}/个</div>
            </div>
        </div>
    </div>

    <div class="section avoid-break">
        <div class="section-title">费用汇总</div>
        <table class="summary-table">
            <thead>
                <tr>
                    <th>费用项目</th>
                    <th>金额</th>
                    <th>占比</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>材料费用</td>
                    <td class="amount">¥${state.quotation.materialCost.toFixed(2)}</td>
                    <td class="center">${((state.quotation.materialCost / state.quotation.totalCost) * 100).toFixed(1)}%</td>
                    <td>包含面纸、灰板、瓦楞等材料成本</td>
                </tr>
                <tr>
                    <td>工艺费用</td>
                    <td class="amount">¥${state.quotation.processCost.toFixed(2)}</td>
                    <td class="center">${((state.quotation.processCost / state.quotation.totalCost) * 100).toFixed(1)}%</td>
                    <td>包含印刷、覆膜、模切等工艺成本</td>
                </tr>
                <tr>
                    <td>配件费用</td>
                    <td class="amount">¥${state.quotation.accessoryCost.toFixed(2)}</td>
                    <td class="center">${((state.quotation.accessoryCost / state.quotation.totalCost) * 100).toFixed(1)}%</td>
                    <td>包含普通配件和礼盒配件成本</td>
                </tr>
                <tr>
                    <td>加工费费用</td>
                    <td class="amount">¥${state.quotation.processingFeeCost.toFixed(2)}</td>
                    <td class="center">${((state.quotation.processingFeeCost / state.quotation.totalCost) * 100).toFixed(1)}%</td>
                    <td>包含可选加工费和固定参数选项加工费</td>
                </tr>
                <tr>
                    <td>自定义费用</td>
                    <td class="amount">¥${state.quotation.formulaCost.toFixed(2)}</td>
                    <td class="center">${((state.quotation.formulaCost / state.quotation.totalCost) * 100).toFixed(1)}%</td>
                    <td>自定义计算的额外费用</td>
                </tr>
            </tbody>
        </table>
    </div>

    ${state.quotation.materialCost > 0 ? `
    <div class="section page-break">
        <div class="section-title">材料费用明细</div>
        <table class="detail-table">
            <thead>
                <tr>
                    <th>序号</th>
                    <th>材料名称</th>
                    <th>规格</th>
                    <th>数量</th>
                    <th>单位</th>
                    <th>单价</th>
                    <th>小计</th>
                </tr>
            </thead>
            <tbody>
                ${generateSharedMaterialDetails(state, config)}
            </tbody>
        </table>
    </div>
    ` : ''}

    ${state.quotation.processCost > 0 ? `
    <div class="section">
        <div class="section-title">工艺费用明细</div>
        <table class="detail-table">
            <thead>
                <tr>
                    <th>序号</th>
                    <th>部件组</th>
                    <th>工艺类型</th>
                    <th>工艺名称</th>
                    <th>规格参数</th>
                    <th>数量</th>
                    <th>单价</th>
                    <th>小计</th>
                </tr>
            </thead>
            <tbody>
                ${generateSharedProcessDetails(state, config)}
            </tbody>
        </table>
    </div>
    ` : ''}

    ${state.quotation.accessoryCost > 0 ? `
    <div class="section">
        <div class="section-title">配件费用明细</div>
        <table class="detail-table">
            <thead>
                <tr>
                    <th>序号</th>
                    <th>配件类型</th>
                    <th>配件名称</th>
                    <th>规格参数</th>
                    <th>数量</th>
                    <th>单价</th>
                    <th>小计</th>
                </tr>
            </thead>
            <tbody>
                ${generateSharedAccessoryDetails(state, config)}
            </tbody>
        </table>
    </div>
    ` : ''}

    ${state.quotation.processingFeeCost > 0 ? `
    <div class="section">
        <div class="section-title">加工费费用明细</div>
        <table class="detail-table">
            <thead>
                <tr>
                    <th>序号</th>
                    <th>费用名称</th>
                    <th>数量</th>
                    <th>单价</th>
                    <th>小计</th>
                </tr>
            </thead>
            <tbody>
                ${generateSharedProcessingFeeDetails(state, config)}
            </tbody>
        </table>
    </div>
    ` : ''}

    <div class="total-section avoid-break">
        <div class="total-amount">
            总计：¥${state.quotation.totalCost.toFixed(2)}
        </div>
        <div style="text-align: center; margin-top: 15px; font-size: 14px;">
            单价：¥${(state.quotation.totalCost / state.basicInfo.quantity).toFixed(2)}/个
        </div>
        <div style="text-align: center; margin-top: 20px; font-size: 12px; color: #666;">
            <p>备注：此报价仅供参考，最终价格以实际订单为准</p>
        </div>
    </div>

    <div class="footer">
        <p>此报价单由盒型计算报价系统自动生成</p>
        <p>生成时间：${new Date().toLocaleString('zh-CN')} | 如有疑问请联系相关业务人员</p>
    </div>
</body>
</html>`;
};

/**
 * 生成可打印的HTML内容（优化版本，类似PDF格式）
 */
export const generatePrintableHTML = (state: CalculationState): string => {
  return generateSharedQuotationHTML(state, PRINT_FORMAT_CONFIG, generatePrintStyles);
};

/**
 * 执行打印报价单功能
 */
export const executePrintQuotation = (state: CalculationState): Promise<{ success: boolean; message: string }> => {
  return new Promise((resolve) => {
    try {
      const printContent = generatePrintableHTML(state);
      const printWindow = window.open('', '_blank');

      if (printWindow) {
        printWindow.document.write(printContent);
        printWindow.document.close();
        printWindow.focus();

        // 等待内容加载完成后打印
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 500);

        resolve({ success: true, message: '正在准备打印...' });
      } else {
        resolve({ success: false, message: '无法打开打印窗口，请检查浏览器设置' });
      }
    } catch (error) {
      perfLog.error('打印失败:', error);
      resolve({ success: false, message: '打印失败，请稍后重试' });
    }
  });
};
