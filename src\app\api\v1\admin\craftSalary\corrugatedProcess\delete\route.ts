import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';
import { DeleteCorrugatedProcessData, deleteCorrugatedProcessSchema } from '@/lib/validations/admin/corrugatedProcess';

export const POST = withValidation(
  deleteCorrugatedProcessSchema,
  async (request: NextRequest, validatedData: DeleteCorrugatedProcessData) => {
    const { id } = validatedData;

    // 检查瓦楞工艺是否存在
    const existingCorrugatedProcess = await prisma.corrugatedProcess.findFirst({
      where: {
        id: id,
        isDel: false,
      },
    });

    assert(!!existingCorrugatedProcess, ErrorCode.NOT_FOUND, '瓦楞工艺不存在');

    // 软删除瓦楞工艺
    await prisma.corrugatedProcess.update({
      where: { id },
      data: { isDel: true },
    });

    return successResponse({ id }, '删除瓦楞工艺成功');
  }
); 