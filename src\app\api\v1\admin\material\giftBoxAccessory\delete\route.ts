import { NextRequest } from 'next/server';
import { deleteGiftBoxAccessorySchema, DeleteGiftBoxAccessoryParams } from '@/lib/validations/admin/giftBoxAccessory';
import { prisma } from '@/lib/prisma';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation<DeleteGiftBoxAccessoryParams>(
  deleteGiftBoxAccessorySchema,
  async (request: NextRequest, validatedData: DeleteGiftBoxAccessoryParams) => {
    const { id } = validatedData;

    // 检查礼盒配件是否存在
    const existingGiftBoxAccessory = await prisma.giftBoxAccessory.findFirst({
      where: {
        id,
        isDel: false,
      },
    });

    assertExists(existingGiftBoxAccessory, ErrorCode.MATERIAL_NOT_FOUND, '礼盒配件不存在');

    // 软删除礼盒配件
    await prisma.giftBoxAccessory.update({
      where: { id },
      data: {
        isDel: true,
      },
    });

    return successResponse(true, '删除礼盒配件成功');
  }
); 