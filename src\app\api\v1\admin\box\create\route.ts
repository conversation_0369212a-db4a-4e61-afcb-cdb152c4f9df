import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { CreateBoxParams, createBoxSchema } from '@/lib/validations/admin/box';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation<CreateBoxParams>(
  createBoxSchema,
  async (request: NextRequest, validatedData: CreateBoxParams) => {
      const data = validatedData;
      
      // 检查盒型名称是否重复
      const existingBox = await prisma.box.findFirst({
        where: {
          name: data.name,
          isDel: false,
        },
      });
      
      assert(!existingBox, ErrorCode.BOX_NAME_EXISTS, '盒型名称已存在');
      
      // 开始数据库事务
      const result = await prisma.$transaction(async (tx) => {
        // 创建盒型基本信息
        const box = await tx.box.create({
          data: {
            name: data.name,
            status: data.status,
            description: data.description,
            processingFee: data.processingFee,
            processingBasePrice: data.processingBasePrice,
          },
        });
        
        // 创建属性
        if (data.attributes && data.attributes.length > 0) {
          await tx.boxAttribute.createMany({
            data: data.attributes.map((attr: any, index: number) => ({
              boxId: box.id,
              name: attr.name,
              code: attr.code,
              value: attr.value,
              sortOrder: attr.sortOrder !== undefined ? attr.sortOrder : index, // 保持顺序
            })),
          });
        }

        // 创建部件和公式
        if (data.parts && data.parts.length > 0) {
          for (const part of data.parts) {
            const createdPart = await tx.boxPart.create({
              data: {
                boxId: box.id,
                name: part.name,
              },
            });

            if (part.formulas && part.formulas.length > 0) {
              await tx.boxFormula.createMany({
                data: part.formulas.map((formula: any) => ({
                  boxId: box.id,
                  partId: createdPart.id,
                  name: formula.name,
                  expression: formula.expression || '',
                })),
              });
            }
          }
        }

        // 创建打包公式
        if (data.packaging) {
          await tx.boxPackaging.create({
            data: {
              boxId: box.id,
              lengthFormula: data.packaging.lengthFormula,
              widthFormula: data.packaging.widthFormula,
              heightFormula: data.packaging.heightFormula,
            },
          });
        }

        // 创建图片
        if (data.images && data.images.length > 0) {
          for (const image of data.images) {
            // 验证图片数据
            assert(!!image.imageData, ErrorCode.BOX_IMAGE_INVALID, '图片数据不能为空');
            assert(!!image.mimeType, ErrorCode.BOX_IMAGE_INVALID, '图片类型不能为空');
            
            await tx.boxImage.create({
              data: {
                boxId: box.id,
                name: image.name,
                imageData: Buffer.from(image.imageData, 'base64'),
                mimeType: image.mimeType,
                sortOrder: image.sortOrder || 0,
              },
            });
          }
        }

        return box;
      });

      return successResponse(result, '创建盒型成功');
  }
); 