import { useState, useCallback, useRef } from 'react';
import { message } from 'antd';
import { Result } from '@/types/common';
import { ErrorCode } from '@/lib/constants/errorCodes';

// 错误信息接口
export interface ErrorInfo {
  code: number;
  message: string;
  details?: any;
  timestamp: number;
  action?: string; // 触发错误的操作
}

// 错误处理器配置
export interface ErrorHandlerConfig {
  /** 是否自动显示错误消息 */
  autoShowError?: boolean;
  /** 错误消息显示时长（毫秒） */
  messageDuration?: number;
  /** 错误回调函数 */
  onError?: (error: ErrorInfo) => void;
  /** 自定义错误消息格式化函数 */
  formatErrorMessage?: (error: ErrorInfo) => string;
  /** 是否启用错误重试 */
  enableRetry?: boolean;
  /** 最大重试次数 */
  maxRetries?: number;
}

// 错误处理状态
export interface ErrorState {
  /** 当前错误信息 */
  error: ErrorInfo | null;
  /** 历史错误列表 */
  errorHistory: ErrorInfo[];
  /** 是否有错误 */
  hasError: boolean;
  /** 重试次数 */
  retryCount: number;
  /** 是否可以重试 */
  canRetry: boolean;
  /** 最后一次操作 */
  lastAction: string | null;
}

// 错误处理器返回值
export interface ErrorHandler {
  /** 错误状态 */
  errorState: ErrorState;
  /** 处理Result类型的响应 */
  handleResult: <T>(result: Result<T>, action?: string) => T | null;
  /** 手动设置错误 */
  setError: (error: ErrorInfo) => void;
  /** 清除错误 */
  clearError: () => void;
  /** 清除所有错误历史 */
  clearErrorHistory: () => void;
  /** 重试上次操作 */
  retry: () => void;
  /** 设置重试回调 */
  setRetryCallback: (callback: () => void | Promise<void>) => void;
  /** 获取格式化的错误消息 */
  getFormattedError: (error?: ErrorInfo) => string;
  /** 检查是否为特定类型的错误 */
  isErrorType: (errorCode: ErrorCode | number) => boolean;
}

const defaultConfig: Required<ErrorHandlerConfig> = {
  autoShowError: true,
  messageDuration: 4000,
  onError: () => {},
  formatErrorMessage: (error) => error.message,
  enableRetry: true,
  maxRetries: 3,
};

/**
 * 错误处理Hook
 * @param config 配置选项
 */
export function useErrorHandler(config: ErrorHandlerConfig = {}): ErrorHandler {
  const finalConfig = { ...defaultConfig, ...config };
  
  // 错误状态
  const [errorState, setErrorState] = useState<ErrorState>({
    error: null,
    errorHistory: [],
    hasError: false,
    retryCount: 0,
    canRetry: false,
    lastAction: null,
  });

  // 重试回调函数引用
  const retryCallbackRef = useRef<(() => void | Promise<void>) | null>(null);

  // 错误消息显示函数
  const showErrorMessage = useCallback((error: ErrorInfo) => {
    if (finalConfig.autoShowError) {
      const formattedMessage = finalConfig.formatErrorMessage(error);
      message.error({
        content: formattedMessage,
        duration: finalConfig.messageDuration / 1000,
      });
    }
  }, [finalConfig]);

  // 设置错误
  const setError = useCallback((error: ErrorInfo) => {
    const errorWithTimestamp = {
      ...error,
      timestamp: error.timestamp || Date.now(),
    };

    setErrorState(prev => ({
      ...prev,
      error: errorWithTimestamp,
      errorHistory: [...prev.errorHistory.slice(-9), errorWithTimestamp], // 保留最近10条错误
      hasError: true,
      canRetry: finalConfig.enableRetry && prev.retryCount < finalConfig.maxRetries,
    }));

    // 显示错误消息
    showErrorMessage(errorWithTimestamp);

    // 调用错误回调
    finalConfig.onError(errorWithTimestamp);
  }, [finalConfig, showErrorMessage]);

  // 清除错误
  const clearError = useCallback(() => {
    setErrorState(prev => ({
      ...prev,
      error: null,
      hasError: false,
      retryCount: 0,
      canRetry: false,
    }));
  }, []);

  // 清除错误历史
  const clearErrorHistory = useCallback(() => {
    setErrorState(prev => ({
      ...prev,
      errorHistory: [],
    }));
  }, []);

  // 处理Result类型的响应
  const handleResult = useCallback(<T>(result: Result<T>, action?: string): T | null => {
    if (result.success) {
      // 成功时清除错误状态
      if (errorState.hasError) {
        clearError();
      }
      
      // 更新最后操作
      if (action) {
        setErrorState(prev => ({
          ...prev,
          lastAction: action,
        }));
      }
      
      return result.data || null;
    } else {
      // 失败时设置错误
      if (result.error) {
        const error: ErrorInfo = {
          code: result.error.code,
          message: result.error.message,
          details: result.error.details,
          timestamp: Date.now(),
          action,
        };
        setError(error);
      }
      
      return null;
    }
  }, [errorState.hasError, clearError, setError]);

  // 重试功能
  const retry = useCallback(async () => {
    if (!errorState.canRetry || !retryCallbackRef.current) {
      return;
    }

    setErrorState(prev => ({
      ...prev,
      retryCount: prev.retryCount + 1,
      canRetry: prev.retryCount + 1 < finalConfig.maxRetries,
    }));

    try {
      await retryCallbackRef.current();
    } catch (error) {
      console.error('重试失败:', error);
    }
  }, [errorState.canRetry, finalConfig.maxRetries]);

  // 设置重试回调
  const setRetryCallback = useCallback((callback: () => void | Promise<void>) => {
    retryCallbackRef.current = callback;
  }, []);

  // 获取格式化的错误消息
  const getFormattedError = useCallback((error?: ErrorInfo): string => {
    const targetError = error || errorState.error;
    if (!targetError) return '';
    
    return finalConfig.formatErrorMessage(targetError);
  }, [errorState.error, finalConfig]);

  // 检查是否为特定类型的错误
  const isErrorType = useCallback((errorCode: ErrorCode | number): boolean => {
    return errorState.error?.code === errorCode;
  }, [errorState.error]);

  return {
    errorState,
    handleResult,
    setError,
    clearError,
    clearErrorHistory,
    retry,
    setRetryCallback,
    getFormattedError,
    isErrorType,
  };
}

/**
 * 全局错误处理Hook
 * 用于处理应用级别的错误状态
 */
export function useGlobalErrorHandler() {
  const [globalErrors, setGlobalErrors] = useState<ErrorInfo[]>([]);

  // 添加全局错误
  const addGlobalError = useCallback((error: ErrorInfo) => {
    setGlobalErrors(prev => [...prev, { ...error, timestamp: Date.now() }]);
  }, []);

  // 移除全局错误
  const removeGlobalError = useCallback((timestamp: number) => {
    setGlobalErrors(prev => prev.filter(error => error.timestamp !== timestamp));
  }, []);

  // 清除所有全局错误
  const clearGlobalErrors = useCallback(() => {
    setGlobalErrors([]);
  }, []);

  // 获取最新的全局错误
  const latestGlobalError = globalErrors[globalErrors.length - 1] || null;

  return {
    globalErrors,
    latestGlobalError,
    addGlobalError,
    removeGlobalError,
    clearGlobalErrors,
    hasGlobalErrors: globalErrors.length > 0,
  };
}

/**
 * 错误边界数据Hook
 * 用于React错误边界组件
 */
export function useErrorBoundary() {
  const [error, setError] = useState<Error | null>(null);
  const [errorInfo, setErrorInfo] = useState<{ componentStack: string } | null>(null);

  // 重置错误状态
  const resetError = useCallback(() => {
    setError(null);
    setErrorInfo(null);
  }, []);

  // 手动触发错误
  const throwError = useCallback((error: Error) => {
    setError(error);
  }, []);

  return {
    error,
    errorInfo,
    hasError: error !== null,
    resetError,
    throwError,
    setError,
    setErrorInfo,
  };
}

/**
 * 异步操作错误处理Hook
 * 用于处理异步操作的加载状态和错误状态
 */
export function useAsyncError<T = any>() {
  const [loading, setLoading] = useState(false);
  const errorHandler = useErrorHandler();

  // 执行异步操作
  const execute = useCallback(async (
    asyncFn: () => Promise<Result<T>>,
    action?: string
  ): Promise<T | null> => {
    setLoading(true);
    errorHandler.clearError();

    try {
      const result = await asyncFn();
      return errorHandler.handleResult(result, action);
    } finally {
      setLoading(false);
    }
  }, [errorHandler]);

  return {
    loading,
    execute,
    ...errorHandler,
  };
} 