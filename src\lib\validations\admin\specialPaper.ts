import { z } from 'zod';

// 基础字段验证规则
export const specialPaperBaseSchema = z.object({
  name: z.string()
    .min(1, '名称不能为空')
    .max(100, '名称长度不能超过100'),
  price: z.number()
    .positive('价格必须大于0')
    .refine(val => val <= 1000000, '价格不能超过1000000'),
  unit: z.string()
    .min(1, '单位不能为空')
    .max(20, '单位长度不能超过20')
    .refine(val => ['元/吨', '元/张', '元/平方'].includes(val), '单位必须是：元/吨、元/张、元/平方'),
  weight: z.number()
    .min(0, '克重必须大于等于0')
    .refine(val => val <= 1000, '克重不能超过1000'),
  thickness: z.number()
    .min(0, '厚度必须大于等于0')
    .refine(val => val <= 10, '厚度不能超过10'),
  isRegular: z.boolean().default(false),
  isLarge: z.boolean().default(false),
  isSpecial: z.boolean().default(false),
  size1: z.number()
    .positive('特规尺寸1必须大于0')
    .refine(val => val <= 10000, '特规尺寸1不能超过10000')
    .optional()
    .nullable(),
  size2: z.number()
    .positive('特规尺寸2必须大于0')
    .refine(val => val <= 10000, '特规尺寸2不能超过10000')
    .optional()
    .nullable(),
  category: z.string()
    .min(1, '材料品类不能为空')
    .max(50, '材料品类长度不能超过50'),
  remark: z.string()
    .max(1000, '备注长度不能超过1000')
    .optional()
    .nullable(),
});

// 创建特种纸的验证schema
export const createSpecialPaperSchema = specialPaperBaseSchema;

// 更新特种纸的验证schema
export const updateSpecialPaperSchema = specialPaperBaseSchema.extend({
  id: z.number().int().positive('ID必须是正整数'),
});

// 获取特种纸详情的验证schema
export const getSpecialPaperDetailSchema = z.object({
  id: z.number().int().positive('ID必须是正整数'),
});

// 获取特种纸列表的验证schema
export const getSpecialPaperListSchema = z.object({
  page: z.number().int().min(1, '页码必须大于0').default(1),
  pageSize: z.number().int().min(1, '每页条数必须大于0').max(100, '每页条数不能超过100').default(10),
  keyword: z.string().optional(),
  category: z.string().optional(),
});

// 删除特种纸的验证schema
export const deleteSpecialPaperSchema = z.object({
  id: z.number().int().positive('ID必须是正整数'),
});

// 特规尺寸条件验证函数
export const validateSpecialSize = (data: z.infer<typeof specialPaperBaseSchema>) => {
  if (data.isSpecial && (!data.size1 || !data.size2)) {
    return {
      success: false,
      error: '特规选项启用时，必须提供特规尺寸'
    };
  }
  return { success: true };
};

// 导出类型
export type SpecialPaperBase = z.infer<typeof specialPaperBaseSchema>;
export type CreateSpecialPaperParams = z.infer<typeof createSpecialPaperSchema>;
export type UpdateSpecialPaperParams = z.infer<typeof updateSpecialPaperSchema>;
export type GetSpecialPaperDetailParams = z.infer<typeof getSpecialPaperDetailSchema>;
export type GetSpecialPaperListParams = z.infer<typeof getSpecialPaperListSchema>;
export type DeleteSpecialPaperParams = z.infer<typeof deleteSpecialPaperSchema>; 