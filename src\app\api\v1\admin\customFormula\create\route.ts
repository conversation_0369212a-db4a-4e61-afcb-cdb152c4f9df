import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createCustomFormulaSchema, CreateCustomFormulaParams } from '@/lib/validations/admin/customFormula';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';


export const POST = withValidation<CreateCustomFormulaParams>(
  createCustomFormulaSchema,
  async (request: NextRequest, validatedData: CreateCustomFormulaParams) => {
    const data = validatedData;

    // 检查公式名称是否重复
    const existingFormula = await prisma.customFormula.findFirst({
      where: {
        name: data.name,
        isDel: false,
      },
    });

    assert(!existingFormula, ErrorCode.FORMULA_NAME_EXISTS, '公式名称已存在');

    // 开始数据库事务
    const result = await prisma.$transaction(async (tx) => {
      // 创建自定义公式基本信息
      const formula = await tx.customFormula.create({
        data: {
          name: data.name,
          initialAmount: data.initialAmount,
          expression: data.expression || '',
          status: data.status,
        },
      });

      // 创建属性
      if (data.attributes && data.attributes.length > 0) {
        await tx.customFormulaAttribute.createMany({
          data: data.attributes.map((attr) => ({
            formulaId: formula.id,
            name: attr.name,
            value: attr.value,
          })),
        });
      }

      return formula;
    });

    return successResponse(result, '创建自定义公式成功');
  }
); 