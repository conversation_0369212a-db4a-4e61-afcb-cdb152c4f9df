import { NextRequest } from 'next/server';
import { createGiftBoxAccessorySchema, CreateGiftBoxAccessoryParams, validateStockSize } from '@/lib/validations/admin/giftBoxAccessory';
import { prisma } from '@/lib/prisma';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation<CreateGiftBoxAccessoryParams>(
  createGiftBoxAccessorySchema,
  async (request: NextRequest, validatedData: CreateGiftBoxAccessoryParams) => {
    const { name, price, unit, isStockSize, stockLength, stockWidth, remark } = validatedData;

    // 现货尺寸验证
    const stockSizeValidation = validateStockSize(validatedData);
    assert(stockSizeValidation.success, ErrorCode.INVALID_PARAMETERS, stockSizeValidation.error);

    // 检查名称是否已存在
    const existingGiftBoxAccessory = await prisma.giftBoxAccessory.findFirst({
      where: {
        name,
        isDel: false,
      },
    });

    assert(!existingGiftBoxAccessory, ErrorCode.MATERIAL_NAME_EXISTS, '礼盒配件名称已存在');

    // 创建礼盒配件
    const giftBoxAccessory = await prisma.giftBoxAccessory.create({
      data: {
        name,
        price,
        unit,
        isStockSize,
        stockLength: isStockSize ? stockLength : null,
        stockWidth: isStockSize ? stockWidth : null,
        remark: remark || null,
        isDel: false,
      },
    });

    return successResponse(giftBoxAccessory, '创建礼盒配件成功');
  }
); 