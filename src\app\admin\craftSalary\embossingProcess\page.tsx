'use client';

import React, { useState, useEffect } from 'react';
import {
  Table, Button, Space, Modal, Form, Input,
  Select, InputNumber, Popconfirm, Typography,
  message, Row, Col, Tag, Tabs, Card
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined,
  SearchOutlined, ReloadOutlined, SettingOutlined
} from '@ant-design/icons';
import { texturingProcessApi, embossingProcess<PERSON>pi, hydraulicProcessApi } from '@/services/adminApi';
import {
  TexturingProcess, EmbossingProcess, HydraulicProcess,
  TEXTURING_PROCESS_UNITS, EMBOSSING_HYDRAULIC_PROCESS_UNITS,
  TexturingProcessUnit, EmbossingHydraulicProcessUnit
} from '@/types/craftSalary';
import { useAsyncError } from '@/lib/hooks/useErrorHandler';
import dayjs from 'dayjs';

const { Title } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

type ProcessType = 'texturing' | 'embossing' | 'hydraulic';
type ProcessRecord = TexturingProcess | EmbossingProcess | HydraulicProcess;

/**
 * 凹凸工艺管理主页面
 */
export default function EmbossingProcessManagementPage() {
  // 错误处理Hook
  const { execute: executeTexturing, loading: texturingLoading } = useAsyncError();
  const { execute: executeEmbossing, loading: embossingLoading } = useAsyncError();
  const { execute: executeHydraulic, loading: hydraulicLoading } = useAsyncError();

  // 当前激活的工艺类型
  const [activeTab, setActiveTab] = useState<ProcessType>('texturing');

  // 压纹工艺数据相关状态
  const [texturingList, setTexturingList] = useState<TexturingProcess[]>([]);
  const [texturingTotal, setTexturingTotal] = useState(0);
  const [texturingCurrent, setTexturingCurrent] = useState(1);
  const [texturingPageSize, setTexturingPageSize] = useState(10);
  const [texturingKeyword, setTexturingKeyword] = useState('');

  // 凹凸工艺数据相关状态
  const [embossingList, setEmbossingList] = useState<EmbossingProcess[]>([]);
  const [embossingTotal, setEmbossingTotal] = useState(0);
  const [embossingCurrent, setEmbossingCurrent] = useState(1);
  const [embossingPageSize, setEmbossingPageSize] = useState(10);
  const [embossingKeyword, setEmbossingKeyword] = useState('');
  const [embossingUnit, setEmbossingUnit] = useState<EmbossingHydraulicProcessUnit | ''>('');

  // 液压工艺数据相关状态
  const [hydraulicList, setHydraulicList] = useState<HydraulicProcess[]>([]);
  const [hydraulicTotal, setHydraulicTotal] = useState(0);
  const [hydraulicCurrent, setHydraulicCurrent] = useState(1);
  const [hydraulicPageSize, setHydraulicPageSize] = useState(10);
  const [hydraulicKeyword, setHydraulicKeyword] = useState('');
  const [hydraulicUnit, setHydraulicUnit] = useState<EmbossingHydraulicProcessUnit | ''>('');

  // 模态框相关状态
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [form] = Form.useForm();

  // 初始加载数据
  useEffect(() => {
    fetchTexturingList();
    fetchEmbossingList();
    fetchHydraulicList();
  }, []);

  // 监听模态框状态变化，设置表单值
  useEffect(() => {
    if (modalVisible && editingRecord) {
      // 延迟设置表单值，确保Modal和Form已经渲染
      setTimeout(() => {
        if (activeTab === 'texturing') {
          form.setFieldsValue({
            name: editingRecord.name,
            textureVersion: editingRecord.textureVersion,
            unit: editingRecord.unit,
            priceBelow1000: editingRecord.priceBelow1000,
            price1000_1999: editingRecord.price1000_1999,
            price2000_3999: editingRecord.price2000_3999,
            price4000Plus: editingRecord.price4000Plus,
            remark: editingRecord.remark || ''
          });
        } else if (activeTab === 'embossing') {
          form.setFieldsValue({
            name: editingRecord.name,
            price: editingRecord.price,
            unit: editingRecord.unit,
            basePrice: editingRecord.basePrice,
            salary: editingRecord.salary,
            salaryBasePrice: editingRecord.salaryBasePrice,
            remark: editingRecord.remark || ''
          });
        } else if (activeTab === 'hydraulic') {
          form.setFieldsValue({
            name: editingRecord.name,
            price: editingRecord.price,
            unit: editingRecord.unit,
            basePrice: editingRecord.basePrice,
            salary: editingRecord.salary,
            salaryBasePrice: editingRecord.salaryBasePrice,
            remark: editingRecord.remark || ''
          });
        }
      }, 0);
    }
  }, [modalVisible, editingRecord, activeTab, form]);

  // 获取压纹工艺数据列表
  const fetchTexturingList = async (page = texturingCurrent, pageSize = texturingPageSize, search = texturingKeyword) => {
    const requestParams: any = {
      page,
      pageSize,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    };

    if (search) {
      requestParams.search = search;
    }

    const result = await executeTexturing(async () => {
      return await texturingProcessApi.getList(requestParams);
    }, '获取压纹工艺数据列表');

    if (result) {
      setTexturingList(result.list || []);
      setTexturingTotal(result.pagination?.total || 0);
    } else {
      setTexturingList([]);
      setTexturingTotal(0);
    }
  };

  // 获取凹凸工艺数据列表
  const fetchEmbossingList = async (page = embossingCurrent, pageSize = embossingPageSize, search = embossingKeyword, unit = embossingUnit) => {
    const requestParams: any = {
      page,
      pageSize,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    };

    if (search) {
      requestParams.search = search;
    }

    if (unit) {
      requestParams.unit = unit;
    }

    const result = await executeEmbossing(async () => {
      return await embossingProcessApi.getList(requestParams);
    }, '获取凹凸工艺数据列表');

    if (result) {
      setEmbossingList(result.list || []);
      setEmbossingTotal(result.pagination?.total || 0);
    } else {
      setEmbossingList([]);
      setEmbossingTotal(0);
    }
  };

  // 获取液压工艺数据列表
  const fetchHydraulicList = async (page = hydraulicCurrent, pageSize = hydraulicPageSize, search = hydraulicKeyword, unit = hydraulicUnit) => {
    const requestParams: any = {
      page,
      pageSize,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    };

    if (search) {
      requestParams.search = search;
    }

    if (unit) {
      requestParams.unit = unit;
    }

    const result = await executeHydraulic(async () => {
      return await hydraulicProcessApi.getList(requestParams);
    }, '获取液压工艺数据列表');

    if (result) {
      setHydraulicList(result.list || []);
      setHydraulicTotal(result.pagination?.total || 0);
    } else {
      setHydraulicList([]);
      setHydraulicTotal(0);
    }
  };

  // 处理分页变化
  const handleTableChange = (pagination: any) => {
    if (activeTab === 'texturing') {
      setTexturingCurrent(pagination.current);
      setTexturingPageSize(pagination.pageSize);
      fetchTexturingList(pagination.current, pagination.pageSize);
    } else if (activeTab === 'embossing') {
      setEmbossingCurrent(pagination.current);
      setEmbossingPageSize(pagination.pageSize);
      fetchEmbossingList(pagination.current, pagination.pageSize);
    } else if (activeTab === 'hydraulic') {
      setHydraulicCurrent(pagination.current);
      setHydraulicPageSize(pagination.pageSize);
      fetchHydraulicList(pagination.current, pagination.pageSize);
    }
  };

  // 打开添加模态框
  const showAddModal = () => {
    if (activeTab === 'texturing') {
      setModalTitle('添加压纹工艺');
      form.setFieldsValue({
        textureVersion: 0,
        priceBelow1000: 0,
        price1000_1999: 0,
        price2000_3999: 0,
        price4000Plus: 0.01,
        unit: '元/张'
      });
    } else if (activeTab === 'embossing') {
      setModalTitle('添加凹凸工艺');
      form.setFieldsValue({
        price: 0,
        basePrice: 0,
        salary: 0,
        salaryBasePrice: 0,
        unit: '元/个'
      });
    } else if (activeTab === 'hydraulic') {
      setModalTitle('添加液压工艺');
      form.setFieldsValue({
        price: 0,
        basePrice: 0,
        salary: 0,
        salaryBasePrice: 0,
        unit: '元/个'
      });
    }
    setEditingRecord(null);
    setModalVisible(true);
  };

  // 打开编辑模态框
  const showEditModal = (record: any) => {
    console.log(record);
    if (activeTab === 'texturing') {
      setModalTitle('编辑压纹工艺');
    } else if (activeTab === 'embossing') {
      setModalTitle('编辑凹凸工艺');
    } else if (activeTab === 'hydraulic') {
      setModalTitle('编辑液压工艺');
    }
    setEditingRecord(record);
    setModalVisible(true);
  };

  // 处理表单提交
  const handleFormSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (activeTab === 'texturing') {
        const requestData = {
          name: values.name,
          textureVersion: Number(values.textureVersion),
          unit: values.unit,
          priceBelow1000: Number(values.priceBelow1000),
          price1000_1999: Number(values.price1000_1999),
          price2000_3999: Number(values.price2000_3999),
          price4000Plus: Number(values.price4000Plus),
          remark: values.remark || ''
        };

        const result = await executeTexturing(async () => {
          if (editingRecord) {
            return await texturingProcessApi.update({ ...requestData, id: editingRecord.id });
          } else {
            return await texturingProcessApi.create(requestData);
          }
        }, editingRecord ? '更新压纹工艺' : '创建压纹工艺');

        if (result) {
          message.success('保存成功');
          setModalVisible(false);
          fetchTexturingList();
        }
      } else if (activeTab === 'embossing') {
        const requestData = {
          name: values.name,
          price: Number(values.price),
          unit: values.unit,
          basePrice: Number(values.basePrice),
          salary: Number(values.salary),
          salaryBasePrice: Number(values.salaryBasePrice),
          remark: values.remark || ''
        };

        const result = await executeEmbossing(async () => {
          if (editingRecord) {
            return await embossingProcessApi.update({ ...requestData, id: editingRecord.id });
          } else {
            return await embossingProcessApi.create(requestData);
          }
        }, editingRecord ? '更新凹凸工艺' : '创建凹凸工艺');

        if (result) {
          message.success('保存成功');
          setModalVisible(false);
          fetchEmbossingList();
        }
      } else if (activeTab === 'hydraulic') {
        const requestData = {
          name: values.name,
          price: Number(values.price),
          unit: values.unit,
          basePrice: Number(values.basePrice),
          salary: Number(values.salary),
          salaryBasePrice: Number(values.salaryBasePrice),
          remark: values.remark || ''
        };

        const result = await executeHydraulic(async () => {
          if (editingRecord) {
            return await hydraulicProcessApi.update({ ...requestData, id: editingRecord.id });
          } else {
            return await hydraulicProcessApi.create(requestData);
          }
        }, editingRecord ? '更新液压工艺' : '创建液压工艺');

        if (result) {
          message.success('保存成功');
          setModalVisible(false);
          fetchHydraulicList();
        }
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 删除记录
  const handleDelete = async (id: number) => {
    if (activeTab === 'texturing') {
      const result = await executeTexturing(async () => {
        return await texturingProcessApi.delete(id);
      }, '删除压纹工艺');

      if (result) {
        fetchTexturingList();
      }
    } else if (activeTab === 'embossing') {
      const result = await executeEmbossing(async () => {
        return await embossingProcessApi.delete(id);
      }, '删除凹凸工艺');

      if (result) {
        fetchEmbossingList();
      }
    } else if (activeTab === 'hydraulic') {
      const result = await executeHydraulic(async () => {
        return await hydraulicProcessApi.delete(id);
      }, '删除液压工艺');

      if (result) {
        fetchHydraulicList();
      }
    }
  };

  // 处理搜索
  const handleSearch = () => {
    if (activeTab === 'texturing') {
      setTexturingCurrent(1);
      fetchTexturingList(1, texturingPageSize, texturingKeyword);
    } else if (activeTab === 'embossing') {
      setEmbossingCurrent(1);
      fetchEmbossingList(1, embossingPageSize, embossingKeyword, embossingUnit);
    } else if (activeTab === 'hydraulic') {
      setHydraulicCurrent(1);
      fetchHydraulicList(1, hydraulicPageSize, hydraulicKeyword, hydraulicUnit);
    }
  };

  // 重置搜索
  const handleReset = () => {
    if (activeTab === 'texturing') {
      setTexturingKeyword('');
      setTexturingCurrent(1);
      fetchTexturingList(1, texturingPageSize, '');
    } else if (activeTab === 'embossing') {
      setEmbossingKeyword('');
      setEmbossingUnit('');
      setEmbossingCurrent(1);
      fetchEmbossingList(1, embossingPageSize, '', '');
    } else if (activeTab === 'hydraulic') {
      setHydraulicKeyword('');
      setHydraulicUnit('');
      setHydraulicCurrent(1);
      fetchHydraulicList(1, hydraulicPageSize, '', '');
    }
  };

  // 获取当前数据和状态
  const getCurrentData = () => {
    if (activeTab === 'texturing') {
      return {
        list: texturingList as ProcessRecord[],
        total: texturingTotal,
        current: texturingCurrent,
        pageSize: texturingPageSize,
        keyword: texturingKeyword,
        loading: texturingLoading,
        setKeyword: setTexturingKeyword
      };
    } else if (activeTab === 'embossing') {
      return {
        list: embossingList as ProcessRecord[],
        total: embossingTotal,
        current: embossingCurrent,
        pageSize: embossingPageSize,
        keyword: embossingKeyword,
        loading: embossingLoading,
        setKeyword: setEmbossingKeyword,
        unit: embossingUnit,
        setUnit: setEmbossingUnit
      };
    } else {
      return {
        list: hydraulicList as ProcessRecord[],
        total: hydraulicTotal,
        current: hydraulicCurrent,
        pageSize: hydraulicPageSize,
        keyword: hydraulicKeyword,
        loading: hydraulicLoading,
        setKeyword: setHydraulicKeyword,
        unit: hydraulicUnit,
        setUnit: setHydraulicUnit
      };
    }
  };

  // 获取表格列配置
  const getTableColumns = () => {
    const commonColumns = [
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        align: 'center' as const,
      },
      {
        title: '单位',
        dataIndex: 'unit',
        key: 'unit',
        width: 120,
        align: 'center' as const,
        render: (unit: string) => <Tag color="blue">{unit}</Tag>,
      }
    ];

    if (activeTab === 'texturing') {
      return [
        ...commonColumns.slice(0, 1),
        {
          title: '压纹版',
          dataIndex: 'textureVersion',
          key: 'textureVersion',
          width: 100,
          render: (value: number) => value || '0',
          align: 'center' as const,
        },
        ...commonColumns.slice(1),
        {
          title: '1000以下',
          dataIndex: 'priceBelow1000',
          key: 'priceBelow1000',
          width: 120,
          render: (value: number) => `¥${value}`,
          align: 'center' as const,
        },
        {
          title: '1000-1999',
          dataIndex: 'price1000_1999',
          key: 'price1000_1999',
          width: 120,
          render: (value: number) => `¥${value}`,
          align: 'center' as const,
        },
        {
          title: '2000-3999',
          dataIndex: 'price2000_3999',
          key: 'price2000_3999',
          width: 120,
          render: (value: number) => `¥${value}`,
          align: 'center' as const,
        },
        {
          title: '4000以上单价',
          dataIndex: 'price4000Plus',
          key: 'price4000Plus',
          width: 120,
          render: (value: number) => `¥${value}`,
          align: 'center' as const,
        },
        {
          title: '备注',
          dataIndex: 'remark',
          key: 'remark',
          ellipsis: true,
          width: 200,
          align: 'center' as const,
          render: (remark: string) => remark || '-',
        },
        {
          title: '修改时间',
          dataIndex: 'updatedAt',
          key: 'updatedAt',
          width: 180,
          render: (value: string) => dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
          align: 'center' as const,
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          align: 'center' as const,
          render: (_: any, record: ProcessRecord) => (
            <Space size="middle">
              <Button
                type="primary"
                size="small"
                icon={<EditOutlined />}
                onClick={() => showEditModal(record)}
              >
                编辑
              </Button>
              <Popconfirm
                title="确定要删除这吗？"
                onConfirm={() => handleDelete(record.id)}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                >
                  删除
                </Button>
              </Popconfirm>
            </Space>
          ),
        }
      ];
    } else {
      return [
        ...commonColumns,
        {
          title: '价格',
          dataIndex: 'price',
          key: 'price',
          width: 120,
          render: (value: number) => `¥${value}`,
          align: 'center' as const,
        },
        {
          title: '起步价',
          dataIndex: 'basePrice',
          key: 'basePrice',
          width: 120,
          render: (value: number) => `¥${value}`,
          align: 'center' as const,
        },
        {
          title: '工资单价',
          dataIndex: 'salary',
          key: 'salary',
          width: 120,
          render: (value: number) => `¥${value}`,
          align: 'center' as const,
        },
        {
          title: '工资起步价',
          dataIndex: 'salaryBasePrice',
          key: 'salaryBasePrice',
          width: 120,
          render: (value: number) => `¥${value}`,
          align: 'center' as const,
        },
        {
          title: '备注',
          dataIndex: 'remark',
          key: 'remark',
          ellipsis: true,
          width: 200,
          align: 'center' as const,
          render: (remark: string) => remark || '-',
        },
        {
          title: '修改时间',
          dataIndex: 'updatedAt',
          key: 'updatedAt',
          width: 180,
          render: (value: string) => dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
          align: 'center' as const,
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          align: 'center' as const,
          render: (_: any, record: ProcessRecord) => (
            <Space size="middle">
              <Button
                type="primary"
                size="small"
                icon={<EditOutlined />}
                onClick={() => showEditModal(record)}
              >
                编辑
              </Button>
              <Popconfirm
                title="确定要删除吗？"
                onConfirm={() => handleDelete(record.id)}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                >
                  删除
                </Button>
              </Popconfirm>
            </Space>
          ),
        }
      ];
    }
  };

  // 获取表单字段
  const getFormFields = () => {
    if (activeTab === 'texturing') {
      return (
        <>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="工艺名称"
                name="name"
                rules={[{ required: true, message: '请输入工艺名称' }]}
              >
                <Input placeholder="请输入工艺名称" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="计价单位"
                name="unit"
                rules={[{ required: true, message: '请选择计价单位' }]}
              >
                <Select placeholder="请选择计价单位">
                  {TEXTURING_PROCESS_UNITS.map(unit => (
                    <Option key={unit} value={unit}>{unit}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="压纹版"
                name="textureVersion"
                rules={[{ required: true, message: '请输入压纹版价格' }]}
              >
                <InputNumber placeholder="请输入压纹版价格" min={0} precision={2} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="1000以下总价"
                name="priceBelow1000"
                rules={[{ required: true, message: '请输入1000以下总价' }]}
              >
                <InputNumber
                  placeholder="请输入总价"
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="1000-1999总价"
                name="price1000_1999"
                rules={[{ required: true, message: '请输入1000-1999总价' }]}
              >
                <InputNumber
                  placeholder="请输入总价"
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="2000-3999总价"
                name="price2000_3999"
                rules={[{ required: true, message: '请输入2000-3999总价' }]}
              >
                <InputNumber
                  placeholder="请输入总价"
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="4000以上单价"
                name="price4000Plus"
                rules={[{ required: true, message: '请输入4000以上单价' }]}
              >
                <InputNumber
                  placeholder="请输入单价"
                  min={0.01}
                  precision={2}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="备注"
            name="remark"
          >
            <TextArea placeholder="请输入备注" rows={3} />
          </Form.Item>
        </>
      );
    } else {
      return (
        <>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="工艺名称"
                name="name"
                rules={[{ required: true, message: '请输入工艺名称' }]}
              >
                <Input placeholder="请输入工艺名称" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="价格"
                name="price"
                rules={[{ required: true, message: '请输入价格' }]}
              >
                <InputNumber
                  placeholder="请输入价格"
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="计价单位"
                name="unit"
                rules={[{ required: true, message: '请选择计价单位' }]}
              >
                <Select placeholder="请选择计价单位">
                  {EMBOSSING_HYDRAULIC_PROCESS_UNITS.map(unit => (
                    <Option key={unit} value={unit}>{unit}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="起步价"
                name="basePrice"
                rules={[{ required: true, message: '请输入起步价' }]}
              >
                <InputNumber
                  placeholder="请输入起步价"
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="工资单价"
                name="salary"
                rules={[{ required: true, message: '请输入工资单价' }]}
              >
                <InputNumber
                  placeholder="请输入工资单价"
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="工资起步价"
                name="salaryBasePrice"
                rules={[{ required: true, message: '请输入工资起步价' }]}
              >
                <InputNumber
                  placeholder="请输入工资起步价"
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="备注"
            name="remark"
          >
            <TextArea placeholder="请输入备注" rows={3} />
          </Form.Item>
        </>
      );
    }
  };

  const currentData = getCurrentData();

  return (
    <div>
      <Title level={2}>凹凸工艺管理</Title>

      <Tabs
        activeKey={activeTab}
        onChange={(key) => setActiveTab(key as ProcessType)}
        items={[
          {
            key: 'texturing',
            label: '压纹工艺',
            children: (
              <Card>
                {/* 搜索区域 */}
                <div style={{ marginBottom: 16 }}>
                  <Row gutter={16}>
                    <Col span={4}>
                      <Input
                        placeholder="搜索工艺名称或备注"
                        value={currentData.keyword}
                        onChange={(e) => currentData.setKeyword(e.target.value)}
                        onPressEnter={handleSearch}
                        prefix={<SearchOutlined />}
                      />
                    </Col>
                    <Col span={16}>
                      <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                        搜索
                      </Button>
                      <Button icon={<ReloadOutlined />} onClick={handleReset}>
                        重置
                      </Button>
                    </Col>
                    <Col flex="auto" style={{ textAlign: 'right' }}>
                      <Button type="primary" icon={<PlusOutlined />} onClick={showAddModal}>
                        添加压纹工艺
                      </Button>
                    </Col>
                  </Row>
                </div>

                {/* 表格 */}
                <Table
                  columns={getTableColumns()}
                  dataSource={currentData.list}
                  rowKey="id"
                  loading={currentData.loading}
                  pagination={{
                    current: currentData.current,
                    pageSize: currentData.pageSize,
                    total: currentData.total,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                  }}
                  onChange={handleTableChange}
                  bordered
                  size="middle"
                  scroll={{ x: 1000 }}
                  locale={{ emptyText: '暂无数据' }}
                />
              </Card>
            )
          },
          {
            key: 'embossing',
            label: '凹凸工艺',
            children: (
              <Card>
                {/* 搜索区域 */}
                <Row gutter={16} style={{ marginBottom: 16 }}>
                  <Col span={4}>
                    <Input
                      placeholder="搜索工艺名称或备注"
                      value={currentData.keyword}
                      onChange={(e) => currentData.setKeyword!(e.target.value)}
                      onPressEnter={handleSearch}
                      prefix={<SearchOutlined />}
                    />
                  </Col>
                  <Col span={16}>
                    <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                      搜索
                    </Button>
                    <Button icon={<ReloadOutlined />} onClick={handleReset}>
                      重置
                    </Button>
                  </Col>
                  <Col flex="auto" style={{ textAlign: 'right' }}>
                    <Button type="primary" icon={<PlusOutlined />} onClick={showAddModal}>
                      添加凹凸工艺
                    </Button>
                  </Col>
                </Row>

                {/* 表格 */}
                <Table
                  columns={getTableColumns()}
                  dataSource={currentData.list}
                  rowKey="id"
                  loading={currentData.loading}
                  pagination={{
                    current: currentData.current,
                    pageSize: currentData.pageSize,
                    total: currentData.total,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                  }}
                  onChange={handleTableChange}
                  bordered
                  size="middle"
                  scroll={{ x: 1000 }}
                  locale={{ emptyText: '暂无数据' }}
                />
              </Card>
            )
          },
          {
            key: 'hydraulic',
            label: '液压工艺',
            children: (
              <Card>
                {/* 搜索区域 */}
                <Row gutter={16} style={{ marginBottom: 16 }}>
                  <Col span={4}>
                    <Input
                      placeholder="搜索工艺名称或备注"
                      value={currentData.keyword}
                      onChange={(e) => currentData.setKeyword!(e.target.value)}
                      onPressEnter={handleSearch}
                      prefix={<SearchOutlined />}
                    />
                  </Col>
                  <Col span={12}>
                    <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                      搜索
                    </Button>
                    <Button icon={<ReloadOutlined />} onClick={handleReset}>
                      重置
                    </Button>
                  </Col>
                  <Col flex="auto" style={{ textAlign: 'right' }}>
                    <Button type="primary" icon={<PlusOutlined />} onClick={showAddModal}>
                      添加液压工艺
                    </Button>
                  </Col>
                </Row>

                {/* 表格 */}
                <Table
                  columns={getTableColumns()}
                  dataSource={currentData.list}
                  rowKey="id"
                  loading={currentData.loading}
                  pagination={{
                    current: currentData.current,
                    pageSize: currentData.pageSize,
                    total: currentData.total,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                  }}
                  onChange={handleTableChange}
                  bordered
                  size="middle"
                  scroll={{ x: 1000 }}
                  locale={{ emptyText: '暂无数据' }}
                />
              </Card>
            )
          }
        ]}
      />

      {/* 模态框 */}
      <Modal
        title={modalTitle}
        open={modalVisible}
        onOk={handleFormSubmit}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
        }}
        width={800}
        destroyOnHidden
      >
        <Form
          form={form}
          layout="vertical"
          preserve={false}
        >
          {getFormFields()}
        </Form>
      </Modal>
    </div>
  );
} 