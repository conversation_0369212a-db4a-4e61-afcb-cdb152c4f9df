import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withValidation } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { z } from 'zod';

const getListSchema = z.object({
  page: z.number().min(1).optional().default(1),
  pageSize: z.number().min(1).max(100).optional().default(10),
  search: z.string().optional(),
  unit: z.string().optional(),
  sortBy: z.string().optional().default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
});

const handler = withValidation(
  getListSchema,
  async (request: AuthenticatedRequest, validatedData: any) => {
    const { page, pageSize, search, unit, sortBy, sortOrder } = validatedData;

    // 构建查询条件
    const where: any = {
      isDel: false,
    };

    if (search) {
      where.OR = [
        { name: { contains: search } },
        { remark: { contains: search } },
      ];
    }

    if (unit) {
      where.unit = unit;
    }

    // 查询总数
    const total = await prisma.embossingProcess.count({ where });

    // 查询列表数据
    const list = await prisma.embossingProcess.findMany({
      where,
      orderBy: { [sortBy]: sortOrder },
      skip: (page - 1) * pageSize,
      take: pageSize,
    });

    return successResponse({
      list,
      pagination: {
        page,
        pageSize,
        total,
      },
    });
  }
); 
export const POST = withInternalAuth(handler);