import { NextRequest } from 'next/server';
import { updateAccessorySchema, UpdateAccessoryParams } from '@/lib/validations/admin/accessory';
import { prisma } from '@/lib/prisma';
import { withValidation, assertExists, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation<UpdateAccessoryParams>(
  updateAccessorySchema,
  async (request: NextRequest, validatedData: UpdateAccessoryParams) => {
    const { id, name, price, initialPrice, weight, unit, remark } = validatedData;

    // 检查配件是否存在
    const existingAccessory = await prisma.accessory.findFirst({
      where: {
        id,
        isDel: false,
      },
    });

    assertExists(existingAccessory, ErrorCode.MATERIAL_NOT_FOUND, '配件不存在');

    // 检查名称是否与其他配件重复
    const duplicateAccessory = await prisma.accessory.findFirst({
      where: {
        name,
        id: { not: id },
        isDel: false,
      },
    });

    assert(!duplicateAccessory, ErrorCode.MATERIAL_NAME_EXISTS, '配件名称已存在');

    // 更新配件
    const accessory = await prisma.accessory.update({
      where: { id },
      data: {
        name,
        price,
        initialPrice,
        weight,
        unit,
        remark: remark || null,
      },
    });

    return successResponse(accessory, '更新配件成功');
  }
); 