import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';
import { z } from 'zod';

// 查询参数验证模式
const getDetailSchema = z.object({
  id: z.number().int().positive('纸张ID必须是正整数'),
});

type GetDetailParams = z.infer<typeof getDetailSchema>;

export const POST = withValidation<GetDetailParams>(
  getDetailSchema,
  async (request: NextRequest, validatedQuery: GetDetailParams) => {
    // 查询纸张详情
    const paper = await prisma.paper.findFirst({
      where: {
        id: validatedQuery.id,
        isDel: false,
      },
    });

    assertExists(
      paper,
      ErrorCode.RESOURCE_NOT_FOUND,
      '纸张材料不存在'
    );

    return successResponse(
      paper,
      '获取纸张材料详情成功'
    );
  }
); 