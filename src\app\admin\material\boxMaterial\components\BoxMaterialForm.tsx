'use client';

import React from 'react';
import { Form, Input, InputNumber, Button, Space, Row, Col } from 'antd';
import { BoxMaterial } from '@/types/material';
import { perfLog } from '@/lib/utils/perfLog';

interface BoxMaterialFormProps {
  initialValues?: Partial<BoxMaterial>;
  onSubmit: (values: any) => void;
  onCancel: () => void;
  loading?: boolean;
}

const BoxMaterialForm: React.FC<BoxMaterialFormProps> = ({
  initialValues,
  onSubmit,
  onCancel,
  loading = false,
}) => {
  const [form] = Form.useForm();

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      onSubmit(values);
    } catch (error) {
      perfLog.error('表单验证失败:', error);
    }
  };

  return (
    <Form
      form={form}
      layout="vertical"
      initialValues={initialValues}
      onFinish={handleSubmit}
    >
      <Row gutter={16}>
        <Col span={8}>
          <Form.Item
            name="code"
            label="编号"
            rules={[{ required: true, message: '请输入编号' }]}
          >
            <Input placeholder="请输入编号" />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            name="facePaper"
            label="面纸"
            rules={[{ required: true, message: '请输入面纸' }]}
          >
            <Input placeholder="请输入面纸" />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            name="linerPaper"
            label="里纸"
            rules={[{ required: true, message: '请输入里纸' }]}
          >
            <Input placeholder="请输入里纸" />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col>
          <Form.Item
            name="threeLayerBE"
            label="三层B/E"
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入三层B/E"
              min={0}
              precision={2}
            />
          </Form.Item>
        </Col>
        <Col>
          <Form.Item
            name="threeLayerAC"
            label="三层A/C"
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入三层A/C"
              min={0}
              precision={2}
            />
          </Form.Item>
        </Col>
        <Col>
          <Form.Item
            name="fiveLayerABBC"
            label="五层AB/BC"
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入五层AB/BC"
              min={0}
              precision={2}
            />
          </Form.Item>
        </Col>
        <Col>
          <Form.Item
            name="fiveLayerEB"
            label="五层EB"
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入五层EB"
              min={0}
              precision={2}
            />
          </Form.Item>
        </Col>
        <Col>
          <Form.Item
            name="sevenLayerEBA"
            label="七层EBA"
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入七层EBA"
              min={0}
              precision={2}
            />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item
        name="remark"
        label="备注"
      >
        <Input.TextArea
          placeholder="请输入备注"
          rows={4}
          maxLength={1000}
          showCount
        />
      </Form.Item>

      <Form.Item>
        <Space>
          <Button type="primary" htmlType="submit" loading={loading}>
            确定
          </Button>
          <Button onClick={onCancel}>取消</Button>
        </Space>
      </Form.Item>
    </Form>
  );
};

export default BoxMaterialForm; 