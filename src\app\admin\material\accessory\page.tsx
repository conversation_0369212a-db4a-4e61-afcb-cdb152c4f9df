'use client';

import React, { useState, useEffect } from 'react';
import {
  Table, Button, Space, Modal, Form, Input,
  Select, InputNumber, Popconfirm, Card, Typography,
  message, Tabs, Row, Col, Switch
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined,
  SearchOutlined, ReloadOutlined
} from '@ant-design/icons';
import { accessoryApi, giftBoxaccessoryApi } from '@/services/adminApi';
import { Accessory, GiftBoxAccessory } from '@/types/material';
import { useAsyncError } from '@/lib/hooks/useErrorHandler';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;

// 配件数据库管理页面
export default function AccessoryManagementPage() {
  // 使用错误处理Hook
  const { execute: executeAccessory, loading: accessoryLoading } = useAsyncError();
  const { execute: executeGiftBox, loading: giftBoxLoading } = useAsyncError();

  // 普通配件相关状态
  const [accessoryList, setAccessoryList] = useState<Accessory[]>([]);
  const [accessoryTotal, setAccessoryTotal] = useState(0);
  const [accessoryCurrent, setAccessoryCurrent] = useState(1);
  const [accessoryPageSize, setAccessoryPageSize] = useState(10);
  const [accessoryKeyword, setAccessoryKeyword] = useState('');

  // 礼盒配件相关状态
  const [giftBoxList, setGiftBoxList] = useState<GiftBoxAccessory[]>([]);
  const [giftBoxTotal, setGiftBoxTotal] = useState(0);
  const [giftBoxCurrent, setGiftBoxCurrent] = useState(1);
  const [giftBoxPageSize, setGiftBoxPageSize] = useState(10);
  const [giftBoxKeyword, setGiftBoxKeyword] = useState('');

  // 模态框相关状态
  const [accessoryModalVisible, setAccessoryModalVisible] = useState(false);
  const [giftBoxModalVisible, setGiftBoxModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [accessoryForm] = Form.useForm();
  const [giftBoxForm] = Form.useForm();

  // 初始加载数据
  useEffect(() => {
    fetchAccessoryList();
    fetchGiftBoxList();
  }, []);

  // 获取普通配件列表
  const fetchAccessoryList = async (page = accessoryCurrent, pageSize = accessoryPageSize, keyword = accessoryKeyword) => {
    const result = await executeAccessory(async () => {
      return await accessoryApi.getList({
        page,
        pageSize,
        keyword
      });
    }, '获取配件列表');

    if (result) {
      setAccessoryList(result.list);
      setAccessoryTotal(result.pagination.total);
    } else {
      setAccessoryList([]);
      setAccessoryTotal(0);
    }
  };

  // 获取礼盒配件列表
  const fetchGiftBoxList = async (page = giftBoxCurrent, pageSize = giftBoxPageSize, keyword = giftBoxKeyword) => {
    const result = await executeGiftBox(async () => {
      return await giftBoxaccessoryApi.getList({
        page,
        pageSize,
        keyword
      });
    }, '获取礼盒配件列表');

    if (result) {
      setGiftBoxList(result.list);
      setGiftBoxTotal(result.pagination.total);
    } else {
      setGiftBoxList([]);
      setGiftBoxTotal(0);
    }
  };

  // 处理普通配件分页变化
  const handleAccessoryTableChange = (pagination: any) => {
    setAccessoryCurrent(pagination.current);
    setAccessoryPageSize(pagination.pageSize);
    fetchAccessoryList(pagination.current, pagination.pageSize);
  };

  // 处理礼盒配件分页变化
  const handleGiftBoxTableChange = (pagination: any) => {
    setGiftBoxCurrent(pagination.current);
    setGiftBoxPageSize(pagination.pageSize);
    fetchGiftBoxList(pagination.current, pagination.pageSize);
  };

  // 打开添加普通配件模态框
  const showAddAccessoryModal = () => {
    setModalTitle('添加配件');
    setEditingRecord(null);
    accessoryForm.resetFields();
    setAccessoryModalVisible(true);
  };

  // 打开编辑普通配件模态框
  const showEditAccessoryModal = (record: Accessory) => {
    setModalTitle('编辑配件');
    setEditingRecord(record);
    accessoryForm.setFieldsValue({
      name: record.name,
      price: record.price,
      initialPrice: record.initialPrice,
      weight: record.weight,
      unit: record.unit,
      remark: record.remark
    });
    setAccessoryModalVisible(true);
  };

  // 打开添加礼盒配件模态框
  const showAddGiftBoxModal = () => {
    setModalTitle('添加礼盒配件');
    setEditingRecord(null);
    giftBoxForm.resetFields();
    setGiftBoxModalVisible(true);
  };

  // 打开编辑礼盒配件模态框
  const showEditGiftBoxModal = (record: GiftBoxAccessory) => {
    setModalTitle('编辑礼盒配件');
    setEditingRecord(record);
    giftBoxForm.setFieldsValue({
      name: record.name,
      price: record.price,
      unit: record.unit,
      isStockSize: record.isStockSize,
      stockLength: record.stockLength,
      stockWidth: record.stockWidth,
      remark: record.remark
    });
    setGiftBoxModalVisible(true);
  };

  // 处理普通配件表单提交
  const handleAccessoryFormSubmit = async () => {
    try {
      const values = await accessoryForm.validateFields();

      if (editingRecord) {
        // 更新配件
        const result = await executeAccessory(async () => {
          return await accessoryApi.update({
            id: editingRecord.id,
            ...values
          });
        }, '更新配件');
        
        if (result) {
          message.success('配件更新成功');
          setAccessoryModalVisible(false);
          fetchAccessoryList();
        }
      } else {
        // 创建配件
        const result = await executeAccessory(async () => {
          return await accessoryApi.create(values);
        }, '创建配件');
        
        if (result) {
          message.success('配件添加成功');
          setAccessoryModalVisible(false);
          fetchAccessoryList();
        }
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理礼盒配件表单提交
  const handleGiftBoxFormSubmit = async () => {
    try {
      const values = await giftBoxForm.validateFields();

      if (editingRecord) {
        // 更新礼盒配件
        const result = await executeGiftBox(async () => {
          return await giftBoxaccessoryApi.update({
            id: editingRecord.id,
            ...values
          });
        }, '更新礼盒配件');
        
        if (result) {
          message.success('礼盒配件更新成功');
          setGiftBoxModalVisible(false);
          fetchGiftBoxList();
        }
      } else {
        // 创建礼盒配件
        const result = await executeGiftBox(async () => {
          return await giftBoxaccessoryApi.create(values);
        }, '创建礼盒配件');
        
        if (result) {
          message.success('礼盒配件添加成功');
          setGiftBoxModalVisible(false);
          fetchGiftBoxList();
        }
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 删除普通配件
  const handleDeleteAccessory = async (id: number) => {
    const result = await executeAccessory(async () => {
      return await accessoryApi.delete(id);
    }, '删除配件');
    
    if (result) {
      message.success('配件删除成功');
      fetchAccessoryList();
    }
  };

  // 删除礼盒配件
  const handleDeleteGiftBox = async (id: number) => {
    const result = await executeGiftBox(async () => {
      return await giftBoxaccessoryApi.delete(id);
    }, '删除礼盒配件');
    
    if (result) {
      message.success('礼盒配件删除成功');
      fetchGiftBoxList();
    }
  };

  // 普通配件表格列定义
  const accessoryColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      align: 'center' as const,
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      width: 120,
      align: 'center' as const,
      render: (text: number) => `${text} 元`,
    },
    {
      title: '起步价',
      dataIndex: 'initialPrice',
      key: 'initialPrice',
      width: 120,
      align: 'center' as const,
      render: (text: number) => `${text} 元`,
    },
    {
      title: '重量',
      dataIndex: 'weight',
      key: 'weight',
      width: 120,
      align: 'center' as const,
      render: (text: number) => `${text} g`,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      width: 120,
      align: 'center' as const,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      ellipsis: true,
      width: 200,
      align: 'center' as const,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      align: 'center' as const,
      fixed: 'right' as const,
      render: (_: any, record: Accessory) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => showEditAccessoryModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此配件吗？"
            onConfirm={() => handleDeleteAccessory(record.id)}
            okText="是"
            cancelText="否"
          >
            <Button
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 礼盒配件表格列定义
  const giftBoxColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      align: 'center' as const,
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      width: 120,
      align: 'center' as const,
      render: (text: number) => `${text} 元`,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      width: 120,
      align: 'center' as const,
    },
    {
      title: '现货尺寸',
      key: 'stockSize',
      width: 200,
      align: 'center' as const,
      render: (record: GiftBoxAccessory) => (
        record.isStockSize
          ? `${record.stockLength} × ${record.stockWidth}`
          : '否'
      ),
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      ellipsis: true,
      width: 200,
      align: 'center' as const,
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      align: 'center' as const,
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      align: 'center' as const,
      fixed: 'right' as const,
      render: (_: any, record: GiftBoxAccessory) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => showEditGiftBoxModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此礼盒配件吗？"
            onConfirm={() => handleDeleteGiftBox(record.id)}
            okText="是"
            cancelText="否"
          >
            <Button
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    }
  ];

  return (
    <div>
      <Title level={2}>配件数据库</Title>

      <Tabs
        defaultActiveKey="1"
        items={[
          {
            key: '1',
            label: '配件数据库',
            children: (
              <Card>
                <div style={{ marginBottom: 16 }}>
                  <Row gutter={16} align="middle">
                    <Col span={4}>
                      <Input
                        placeholder="搜索名称"
                        prefix={<SearchOutlined />}
                        allowClear
                        value={accessoryKeyword}
                        onChange={(e) => setAccessoryKeyword(e.target.value)}
                        onPressEnter={() => fetchAccessoryList(1, accessoryPageSize, accessoryKeyword)}
                      />
                    </Col>
                    <Col>
                      <Button
                        type="primary"
                        icon={<SearchOutlined />}
                        onClick={() => fetchAccessoryList(1, accessoryPageSize, accessoryKeyword)}
                      >
                        搜索
                      </Button>
                    </Col>
                    <Col>
                      <Button
                        icon={<ReloadOutlined />}
                        onClick={() => {
                          setAccessoryKeyword('');
                          fetchAccessoryList(1, accessoryPageSize, '');
                        }}
                      >
                        重置
                      </Button>
                    </Col>
                    <Col flex="auto" style={{ textAlign: 'right' }}>
                      <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={showAddAccessoryModal}
                      >
                        添加配件
                      </Button>
                    </Col>
                  </Row>
                </div>

                <Table
                  columns={accessoryColumns}
                  dataSource={accessoryList}
                  rowKey="id"
                  loading={accessoryLoading}
                  pagination={{
                    current: accessoryCurrent,
                    pageSize: accessoryPageSize,
                    total: accessoryTotal,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                  }}
                  onChange={handleAccessoryTableChange}
                  bordered
                  size="middle"
                  scroll={{ x: 1200 }}
                  locale={{ emptyText: '暂无数据' }}
                />
              </Card>
            ),
          },
          {
            key: '2',
            label: '礼盒配件数据库',
            children: (
              <Card>
                <div style={{ marginBottom: 16 }}>
                  <Row gutter={16} align="middle">
                    <Col span={4}>
                      <Input
                        placeholder="搜索名称"
                        prefix={<SearchOutlined />}
                        allowClear
                        value={giftBoxKeyword}
                        onChange={(e) => setGiftBoxKeyword(e.target.value)}
                        onPressEnter={() => fetchGiftBoxList(1, giftBoxPageSize, giftBoxKeyword)}
                      />
                    </Col>
                    <Col>
                      <Button
                        type="primary"
                        icon={<SearchOutlined />}
                        onClick={() => fetchGiftBoxList(1, giftBoxPageSize, giftBoxKeyword)}
                      >
                        搜索
                      </Button>
                    </Col>
                    <Col>
                      <Button
                        icon={<ReloadOutlined />}
                        onClick={() => {
                          setGiftBoxKeyword('');
                          fetchGiftBoxList(1, giftBoxPageSize, '');
                        }}
                      >
                        重置
                      </Button>
                    </Col>
                    <Col flex="auto" style={{ textAlign: 'right' }}>
                      <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={showAddGiftBoxModal}
                      >
                        添加礼盒配件
                      </Button>
                    </Col>
                  </Row>
                </div>

                <Table
                  columns={giftBoxColumns}
                  dataSource={giftBoxList}
                  rowKey="id"
                  loading={giftBoxLoading}
                  pagination={{
                    current: giftBoxCurrent,
                    pageSize: giftBoxPageSize,
                    total: giftBoxTotal,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                  }}
                  onChange={handleGiftBoxTableChange}
                  bordered
                  size="middle"
                  scroll={{ x: 1200 }}
                  locale={{ emptyText: '暂无数据' }}
                />
              </Card>
            ),
          },
        ]}
      />

      {/* 普通配件表单模态框 */}
      <Modal
        title={modalTitle}
        open={accessoryModalVisible}
        onOk={handleAccessoryFormSubmit}
        onCancel={() => setAccessoryModalVisible(false)}
        width={800}
      >
        <Form
          form={accessoryForm}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="配件名称"
                rules={[{ required: true, message: '请输入配件名称' }]}
              >
                <Input placeholder="请输入配件名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="price"
                label="价格"
                rules={[
                  { required: true, message: '请输入价格' },
                  { type: 'number', min: 0, message: '价格必须大于0' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入价格"
                  precision={2}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="initialPrice"
                label="起步价"
                rules={[
                  { required: true, message: '请输入起步价' },
                  { type: 'number', min: 0, message: '起步价必须大于0' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入起步价"
                  precision={2}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="weight"
                label="重量"
                rules={[
                  { required: true, message: '请输入重量' },
                  { type: 'number', min: 0, message: '重量必须大于0' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入重量"
                  precision={2}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="unit"
                label="单位"
                rules={[{ required: true, message: '请选择单位' }]}
              >
                <Select placeholder="请选择单位">
                  <Option value="元/对">元/对</Option>
                  <Option value="元/米">元/米</Option>
                  <Option value="元/条">元/条</Option>
                  <Option value="元/平方">元/平方</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="remark"
            label="备注"
          >
            <Input.TextArea placeholder="请输入备注" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 礼盒配件表单模态框 */}
      <Modal
        title={modalTitle}
        open={giftBoxModalVisible}
        onOk={handleGiftBoxFormSubmit}
        onCancel={() => setGiftBoxModalVisible(false)}
        width={700}
      >
        <Form
          form={giftBoxForm}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="配件名称"
                rules={[{ required: true, message: '请输入配件名称' }]}
              >
                <Input placeholder="请输入配件名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="price"
                label="价格"
                rules={[
                  { required: true, message: '请输入价格' },
                  { type: 'number', min: 0, message: '价格必须大于0' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入价格"
                  precision={2}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="unit"
                label="单位"
                rules={[{ required: true, message: '请选择单位' }]}
              >
                <Select placeholder="请选择单位">
                  <Option value="元/立方">元/立方</Option>
                  <Option value="元/平方">元/平方</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="isStockSize"
                label="是否按现货尺寸"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.isStockSize !== currentValues.isStockSize}
          >
            {({ getFieldValue }) => (
              getFieldValue('isStockSize') && (
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="stockLength"
                      label="现货长度"
                      rules={[
                        { required: true, message: '请输入现货长度' },
                        { type: 'number', min: 0, message: '长度必须大于0' }
                      ]}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="请输入现货长度"
                        precision={2}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="stockWidth"
                      label="现货宽度"
                      rules={[
                        { required: true, message: '请输入现货宽度' },
                        { type: 'number', min: 0, message: '宽度必须大于0' }
                      ]}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="请输入现货宽度"
                        precision={2}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              )
            )}
          </Form.Item>
          <Form.Item
            name="remark"
            label="备注"
          >
            <Input.TextArea placeholder="请输入备注" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
} 