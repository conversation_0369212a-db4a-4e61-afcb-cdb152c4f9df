import { z } from 'zod';
import { 
  HOT_STAMPING_SALARY_UNITS, 
  HOT_STAMPING_MATERIAL_UNITS, 
  HOT_STAMPING_PLATE_FEE_UNITS 
} from '@/types/craftSalary';

// ===========================================
// 烫金工艺验证模式
// ===========================================

// 烫金工艺表单验证模式
export const hotStampingProcessFormSchema = z.object({
  name: z.string()
    .min(1, '请输入烫金工艺名称')
    .max(100, '名称长度不能超过100字符'),
  
  salary: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '工资必须是大于等于0的数字'),
  
  salaryUnit: z.enum(HOT_STAMPING_SALARY_UNITS, {
    required_error: '请选择工资单位',
    invalid_type_error: '无效的工资单位',
  }),
  
  materialPrice: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '材料价格必须是大于等于0的数字'),
  
  materialUnit: z.enum(HOT_STAMPING_MATERIAL_UNITS, {
    required_error: '请选择材料单位',
    invalid_type_error: '无效的材料单位',
  }),
  
  basePrice: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '起步价必须是大于等于0的数字'),
  
  remark: z.string()
    .max(500, '备注长度不能超过500字符')
    .optional()
    .or(z.literal(''))
});

// 烫金工艺创建请求验证模式
export const createHotStampingProcessSchema = z.object({
  name: z.string().min(1).max(100),
  salary: z.number().min(0),
  salaryUnit: z.enum(HOT_STAMPING_SALARY_UNITS),
  materialPrice: z.number().min(0),
  materialUnit: z.enum(HOT_STAMPING_MATERIAL_UNITS),
  basePrice: z.number().min(0),
  remark: z.string().max(500).optional()
});

// 烫金工艺更新请求验证模式
export const updateHotStampingProcessSchema = createHotStampingProcessSchema.extend({
  id: z.number().int().positive()
});

// 烫金工艺列表查询验证模式
export const hotStampingProcessListParamsSchema = z.object({
  page: z.number().refine(n => n > 0).optional(),
  pageSize: z.number().refine(n => n > 0 && n <= 100).optional(),
  search: z.string().max(100).optional()
});

// 烫金工艺删除请求验证模式
export const deleteHotStampingProcessSchema = z.object({
  id: z.number().int().positive(),
});

// ===========================================
// 烫金版费验证模式
// ===========================================

// 烫金版费表单验证模式
export const hotStampingPlateFeeFormSchema = z.object({
  name: z.string()
    .min(1, '请输入烫金版费名称')
    .max(100, '名称长度不能超过100字符'),
  
  price: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '价格必须是大于等于0的数字'),
  
  unit: z.enum(HOT_STAMPING_PLATE_FEE_UNITS, {
    required_error: '请选择计价单位',
    invalid_type_error: '无效的计价单位',
  }),
  
  basePrice: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '起步价必须是大于等于0的数字'),
  
  remark: z.string()
    .max(500, '备注长度不能超过500字符')
    .optional()
    .or(z.literal(''))
});

// 烫金版费创建请求验证模式
export const createHotStampingPlateFeeSchema = z.object({
  name: z.string().min(1).max(100),
  price: z.number().min(0),
  unit: z.enum(HOT_STAMPING_PLATE_FEE_UNITS),
  basePrice: z.number().min(0),
  remark: z.string().max(500).optional()
});

// 烫金版费更新请求验证模式
export const updateHotStampingPlateFeeSchema = createHotStampingPlateFeeSchema.extend({
  id: z.number().int().positive()
});

// 烫金版费列表查询验证模式
export const hotStampingPlateFeeListParamsSchema = z.object({
  page: z.number().refine(n => n > 0).optional(),
  pageSize: z.number().refine(n => n > 0 && n <= 100).optional(),
  search: z.string().max(100).optional()
});

// 烫金版费删除请求验证模式
export const deleteHotStampingPlateFeeSchema = z.object({
  id: z.number().refine(n => n > 0)
});

// ===========================================
// 数据转换函数
// ===========================================

// 烫金工艺表单数据转换为API请求数据
export function transformHotStampingProcessFormData(formData: any) {
  return {
    name: formData.name,
    salary: Number(formData.salary),
    salaryUnit: formData.salaryUnit,
    materialPrice: Number(formData.materialPrice),
    materialUnit: formData.materialUnit,
    basePrice: Number(formData.basePrice),
    remark: formData.remark || undefined
  };
}

// 烫金版费表单数据转换为API请求数据
export function transformHotStampingPlateFeeFormData(formData: any) {
  return {
    name: formData.name,
    price: Number(formData.price),
    unit: formData.unit,
    basePrice: Number(formData.basePrice),
    remark: formData.remark || undefined
  };
}


export type CreateHotStampingProcessData = z.infer<typeof createHotStampingProcessSchema>
export type UpdateHotStampingProcessData = z.infer<typeof updateHotStampingProcessSchema>
export type HotStampingProcessListParams = z.infer<typeof hotStampingProcessListParamsSchema>
export type DeleteHotStampingProcessData = z.infer<typeof deleteHotStampingProcessSchema>

export type CreateHotStampingPlateFeeData = z.infer<typeof createHotStampingPlateFeeSchema>
export type UpdateHotStampingPlateFeeData = z.infer<typeof updateHotStampingPlateFeeSchema>
export type HotStampingPlateFeeListParams = z.infer<typeof hotStampingPlateFeeListParamsSchema>
export type DeleteHotStampingPlateFeeData = z.infer<typeof deleteHotStampingPlateFeeSchema>

