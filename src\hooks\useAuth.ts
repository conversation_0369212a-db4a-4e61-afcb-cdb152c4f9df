'use client';

import { useState, useEffect, useCallback } from 'react';
import { CurrentUser } from '@/types/user';
import { getCurrentUser, getLocalUser, setAuthInfo, clearAuthInfo } from '@/services/auth';

interface UseAuthReturn {
  user: CurrentUser | null;
  loading: boolean;
  isAuthenticated: boolean;
  refreshUser: () => Promise<void>;
  setUser: (user: CurrentUser | null) => void;
  clearUser: () => void;
}

/**
 * 用户认证状态管理 Hook
 * 提供用户信息缓存、状态管理和自动刷新功能
 */
export function useAuth(): UseAuthReturn {
  const [user, setUserState] = useState<CurrentUser | null>(null);
  const [loading, setLoading] = useState(true);

  // 从服务器获取用户信息
  const fetchUserFromServer = useCallback(async (): Promise<CurrentUser | null> => {
    try {
      const result = await getCurrentUser(false); // 不显示错误提示
      if (result.success && result.data) {
        return result.data;
      }
      return null;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  }, []);

  // 刷新用户信息
  const refreshUser = useCallback(async () => {
    setLoading(true);
    try {
      const serverUser = await fetchUserFromServer();
      if (serverUser) {
        // 更新状态和本地存储
        setUserState(serverUser);
        setAuthInfo(serverUser);
      } else {
        // 服务器返回空，清除本地信息
        setUserState(null);
        clearAuthInfo();
      }
    } catch (error) {
      console.error('刷新用户信息失败:', error);
      // 发生错误时，清除用户信息
      setUserState(null);
      clearAuthInfo();
    } finally {
      setLoading(false);
    }
  }, [fetchUserFromServer]);

  // 设置用户信息
  const setUser = useCallback((newUser: CurrentUser | null) => {
    setUserState(newUser);
    if (newUser) {
      setAuthInfo(newUser);
    } else {
      clearAuthInfo();
    }
  }, []);

  // 清除用户信息
  const clearUser = useCallback(() => {
    setUserState(null);
    clearAuthInfo();
  }, []);

  // 初始化用户状态
  useEffect(() => {
    const initializeAuth = async () => {
      setLoading(true);
      
      try {
        // 首先尝试从本地存储获取用户信息
        const localUser = getLocalUser();
        
        if (localUser) {
          // 有本地用户信息，先设置到状态中（避免闪烁）
          setUserState(localUser);
          
          // 然后从服务器验证和更新
          const serverUser = await fetchUserFromServer();
          if (serverUser) {
            // 服务器验证成功，更新用户信息
            if (JSON.stringify(serverUser) !== JSON.stringify(localUser)) {
              setUserState(serverUser);
              setAuthInfo(serverUser);
            }
          } else {
            // 服务器验证失败，清除本地信息
            setUserState(null);
            clearAuthInfo();
          }
        } else {
          // 没有本地用户信息，直接从服务器获取
          const serverUser = await fetchUserFromServer();
          if (serverUser) {
            setUserState(serverUser);
            setAuthInfo(serverUser);
          }
        }
      } catch (error) {
        console.error('初始化认证状态失败:', error);
        setUserState(null);
        clearAuthInfo();
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, [fetchUserFromServer]);

  return {
    user,
    loading,
    isAuthenticated: !!user,
    refreshUser,
    setUser,
    clearUser
  };
}
