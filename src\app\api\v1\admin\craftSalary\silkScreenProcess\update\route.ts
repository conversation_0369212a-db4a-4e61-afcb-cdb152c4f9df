import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { UpdateSilkScreenProcessData, updateSilkScreenProcessSchema } from '@/lib/validations/admin/silkScreenProcess';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation(
  updateSilkScreenProcessSchema,
  async (request: NextRequest, validatedData: UpdateSilkScreenProcessData) => {
    const { id, ...data } = validatedData;

    // 检查丝印工艺是否存在
    const existingSilkScreenProcess = await prisma.silkScreenProcess.findFirst({
      where: {
        id: id,
        isDel: false,
      },
    });

    assert(!!existingSilkScreenProcess, ErrorCode.NOT_FOUND, '丝印工艺不存在');

    // 检查名称是否与其他记录重复
    const duplicateSilkScreenProcess = await prisma.silkScreenProcess.findFirst({
      where: {
        name: data.name,
        id: { not: id },
        isDel: false,
      },
    });

    assert(!duplicateSilkScreenProcess, ErrorCode.DUPLICATE_ENTRY, '丝印工艺名称已存在');

    // 更新丝印工艺
    const silkScreenProcess = await prisma.silkScreenProcess.update({
      where: { id },
      data: {
        name: data.name,
        unitPrice: data.unitPrice,
        unit: data.unit,
        basePrice: data.basePrice,
        materialFee: data.materialFee,
        remark: data.remark,
      },
    });

    return successResponse(silkScreenProcess, '更新丝印工艺成功');
  }
); 