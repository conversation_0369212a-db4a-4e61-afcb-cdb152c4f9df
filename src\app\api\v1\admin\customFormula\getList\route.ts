import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCustomFormulaListSchema, GetCustomFormulaListParams } from '@/lib/validations/admin/customFormula';
import { withValidation } from '@/lib/middleware/errorHandler';
import { paginatedResponse } from '@/lib/utils/apiResponse';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { Prisma } from '@prisma/client';

const handler = withValidation<GetCustomFormulaListParams>(
  getCustomFormulaListSchema,
  async (request: AuthenticatedRequest, validatedQuery: GetCustomFormulaListParams) => {
    const data = validatedQuery;
    const skip = (data.page - 1) * data.pageSize;

    // 构建查询条件
    const where: Prisma.CustomFormulaWhereInput = {
      isDel: false
    };

    if (data.name) {
      where.name = { contains: data.name };
    }

    if (data.status !== undefined) {
      where.status = data.status;
    }

    // 如果有时间范围，构建时间查询条件
    if (data.startTime || data.endTime) {
      where.createdAt = {};

      if (data.startTime) {
        where.createdAt.gte = new Date(data.startTime);
      }

      if (data.endTime) {
        where.createdAt.lte = new Date(data.endTime);
      }
    }

    // 查询总数和列表数据
    const [total, list] = await Promise.all([
      prisma.customFormula.count({ where }),
      prisma.customFormula.findMany({
        where,
        select: {
          id: true,
          name: true,
          status: true,
          initialAmount: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: {
              attributes: true,
            },
          },
        },
        skip,
        take: data.pageSize,
        orderBy: { createdAt: 'desc' },
      })
    ]);

    return paginatedResponse(
      list,
      {
        page: data.page,
        pageSize: data.pageSize,
        total,
      },
      '获取自定义公式列表成功'
    );
  }
);

export const POST = withInternalAuth(handler);