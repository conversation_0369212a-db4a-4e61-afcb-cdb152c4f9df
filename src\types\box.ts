// 盒型相关类型定义

// 盒型状态
export enum BoxStatus {
  DRAFT = 0,
  PUBLISHED = 1
}

// 盒型属性
export interface BoxAttribute {
  id?: number;
  name: string;
  code: string;
  value?: number | null;
  sortOrder?: number;
}

// 盒型公式
export interface BoxFormula {
  id?: number;
  name: string;
  expression?: string;
}

// 盒型部件
export interface BoxPart {
  id?: number;
  name: string;
  formulas?: BoxFormula[];
}

// 盒型图片
export interface BoxImage {
  id?: number;
  name: string;
  imageData?: string;
  mimeType?: string;
  sortOrder?: number;
}

// 盒型打包信息
export interface BoxPackaging {
  id?: number;
  boxId: number;
  lengthFormula: string;
  widthFormula: string;
  heightFormula?: string;
}

// 盒型基本信息
export interface Box {
  id: number;
  name: string;
  status: BoxStatus;
  description?: string | null;
  processingFee: number; // 加工费
  processingBasePrice: number; // 加工费起步价
  createdAt: string;
  updatedAt: string;
  attributes?: BoxAttribute[];
  parts?: BoxPart[];
  images?: BoxImage[];
  packaging?: BoxPackaging;
  _count?: {
    attributes: number;
    parts: number;
    images: number;
  };
}

// 获取盒型列表的参数
export interface BoxListParams {
  page?: number;
  pageSize?: number;
  name?: string;
  status?: number;
  startTime?: string;
  endTime?: string;
}

// 创建盒型的参数
export interface BoxCreateParams {
  name: string;
  status: number;
  description?: string | null;
  processingFee: number; // 加工费
  processingBasePrice: number; // 加工费起步价
  attributes?: BoxAttribute[];
  parts?: BoxPart[];
  images?: BoxImage[];
  packaging?: BoxPackaging;
}

// 更新盒型的参数
export interface BoxUpdateParams {
  id: number;
  name?: string;
  status?: number;
  description?: string | null;
  processingFee: number; // 加工费
  processingBasePrice: number; // 加工费起步价
  attributes?: BoxAttribute[];
  parts?: BoxPart[];
  images?: BoxImage[];
  packaging?: BoxPackaging;
  deleteAttributes?: number[];
  deleteParts?: number[];
  deleteFormulas?: number[];
  deleteImages?: number[];
}
