import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const getLaminatingProcessDetailSchema = z.object({
  id: z.number().int().positive(),
});

const handler = withValidation(
  getLaminatingProcessDetailSchema,
  async (request: AuthenticatedRequest, validatedData: z.infer<typeof getLaminatingProcessDetailSchema>) => {
    const { id } = validatedData;

    // 查询对裱工艺详情
    const laminatingProcess = await prisma.laminatingProcess.findFirst({
      where: {
        id: id,
        isDel: false,
      },
    });

    assert(!!laminatingProcess, ErrorCode.NOT_FOUND, '对裱工艺不存在');

    return successResponse(laminatingProcess, '获取对裱工艺详情成功');
  }
); 
export const POST = withInternalAuth(handler);