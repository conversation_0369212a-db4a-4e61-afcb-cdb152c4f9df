'use client';

import React from 'react';
import { Row, Col, Typography, Divider } from 'antd';
import { CustomCard } from './CustomCard';

const { Title } = Typography;

/**
 * 产品展示组件 - 演示 CustomCard 的使用
 * 模拟用户提供的参考图片中的产品展示效果
 */
export default function ProductShowcase() {
  // 模拟产品数据，参考用户提供的图片内容
  const productData = [
    {
      id: 1,
      title: '层叠样式',
      subtitle: '人气23273',
      imageUrl: '/images/products/product-1.jpg',
      imageAlt: '层叠样式包装盒',
    },
    {
      id: 2,
      title: '大面积样式',
      subtitle: '人气16587',
      imageUrl: '/images/products/product-2.jpg',
      imageAlt: '大面积样式包装盒',
    },
    {
      id: 3,
      title: '自排样式',
      subtitle: '人气3313',
      imageUrl: '/images/products/product-3.jpg',
      imageAlt: '自排样式包装盒',
    },
    {
      id: 4,
      title: '飞机盒样式',
      subtitle: '人气4986',
      imageUrl: '/images/products/product-4.jpg',
      imageAlt: '飞机盒样式包装盒',
    },
    {
      id: 5,
      title: '手提礼盒样式',
      subtitle: '人气5576',
      imageUrl: '/images/products/product-5.jpg',
      imageAlt: '手提礼盒样式包装盒',
    },
    {
      id: 6,
      title: '手提袋样式',
      subtitle: '人气10025',
      imageUrl: '/images/products/product-6.jpg',
      imageAlt: '手提袋样式包装盒',
    },
  ];

  // 处理卡片点击事件
  const handleCardClick = (product: typeof productData[0]) => {
    console.log('点击了产品:', product.title);
    // 这里可以添加跳转到产品详情页的逻辑
  };

  return (
    <div style={{ padding: '20px', backgroundColor: '#f5f5f5' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        {/* 标题区域 */}
        <div style={{ marginBottom: '30px', textAlign: 'left' }}>
          <Title level={2} style={{ color: '#333', marginBottom: '8px' }}>
            精品定制
          </Title>
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            marginBottom: '20px'
          }}>
            <Divider style={{ flex: 1, margin: 0 }} />
            <span style={{ 
              color: '#1890ff', 
              fontSize: '14px',
              marginLeft: '20px',
              cursor: 'pointer'
            }}>
              更多+
            </span>
          </div>
        </div>

        {/* 产品卡片网格 */}
        <Row gutter={[20, 20]}>
          {productData.map((product) => (
            <Col 
              key={product.id}
              xs={12}  // 手机端 2 列
              sm={12}  // 小屏幕 2 列
              md={8}   // 中等屏幕 3 列
              lg={6}   // 大屏幕 4 列
              xl={4}   // 超大屏幕 6 列
            >
              <CustomCard
                title={product.title}
                subtitle={product.subtitle}
                imageUrl={product.imageUrl}
                imageAlt={product.imageAlt}
                onClick={() => handleCardClick(product)}
                width="100%"
                imageHeight={160}
                hoverable={true}
              />
            </Col>
          ))}
        </Row>

        {/* 左侧定制专区卡片 - 模拟参考图片中的红色区域 */}
        <div style={{ 
          marginTop: '30px',
          display: 'flex',
          gap: '20px',
          flexWrap: 'wrap'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, #FF422D 0%, #CC3B26 100%)',
            borderRadius: '8px',
            padding: '30px',
            color: 'white',
            minWidth: '280px',
            flex: '0 0 auto',
            position: 'relative',
            overflow: 'hidden'
          }}>
            <div style={{ position: 'relative', zIndex: 2 }}>
              <Title level={3} style={{ color: 'white', marginBottom: '10px' }}>
                定制礼盒专区
              </Title>
              <p style={{ fontSize: '14px', marginBottom: '15px', opacity: 0.9 }}>
                满足您的个性化需求<br />
                专业定制服务
              </p>
              <div style={{
                background: 'rgba(255, 255, 255, 0.2)',
                borderRadius: '20px',
                padding: '8px 16px',
                display: 'inline-block',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}>
                <span style={{ fontSize: '14px' }}>点击咨询</span>
              </div>
            </div>
            {/* 装饰性元素 */}
            <div style={{
              position: 'absolute',
              right: '-20px',
              bottom: '-20px',
              width: '100px',
              height: '100px',
              background: 'rgba(255, 255, 255, 0.1)',
              borderRadius: '50%',
              zIndex: 1
            }} />
          </div>
        </div>
      </div>
    </div>
  );
}
