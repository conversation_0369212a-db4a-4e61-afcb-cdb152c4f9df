import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { CreatePrintingData, createPrintingSchema } from '@/lib/validations/admin/printing';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation(
  createPrintingSchema,
  async (request: NextRequest, validatedData: CreatePrintingData) => {
    const data = validatedData;

    // 检查印刷机型是否重复
    const existingPrinting = await prisma.printing.findFirst({
      where: {
        machineModel: data.machineModel,
        isDel: false,
      },
    });

    assert(!existingPrinting, ErrorCode.DUPLICATE_ENTRY, '印刷机型已存在');

    // 创建印刷数据
    const printing = await prisma.printing.create({
      data: {
        machineModel: data.machineModel,
        basePrice: data.basePrice,
        price1000_1999: data.price1000_1999,
        price2000_2999: data.price2000_2999,
        price3000_3999: data.price3000_3999,
        price4000_4999: data.price4000_4999,
        price5000_5999: data.price5000_5999,
        price6000_6999: data.price6000_6999,
        price7000_7999: data.price7000_7999,
        price8000_8999: data.price8000_8999,
        price9000_9999: data.price9000_9999,
        price10000Plus: data.price10000Plus,
        unit: data.unit,
        ctpPlateFee: data.ctpPlateFee,
        spotColorFee: data.spotColorFee,
        remark: data.remark,
      },
    });

    return successResponse(printing, '创建印刷配置成功');
  }
); 