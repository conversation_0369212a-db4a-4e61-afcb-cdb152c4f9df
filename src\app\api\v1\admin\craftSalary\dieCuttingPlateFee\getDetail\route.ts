import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const getDieCuttingPlateFeeDetailSchema = z.object({
  id: z.coerce.number().positive('ID必须是正整数')
});

const handler = withValidation(
  getDieCuttingPlateFeeDetailSchema,
  async (request: AuthenticatedRequest, validatedQuery: any) => {
    const data = validatedQuery;

    // 查询刀版费详细信息
    const dieCuttingPlateFee = await prisma.dieCuttingPlateFee.findFirst({
      where: {
        id: data.id,
        isDel: false,
      },
    });

    assertExists(dieCuttingPlateFee, ErrorCode.RESOURCE_NOT_FOUND, '刀版费不存在');

    return successResponse(dieCuttingPlateFee, '获取刀版费详情成功');
  }
);

export const POST = withInternalAuth(handler);