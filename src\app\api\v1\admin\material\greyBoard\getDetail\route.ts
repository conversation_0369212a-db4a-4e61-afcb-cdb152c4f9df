import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getGreyBoardDetailSchema, GetGreyBoardDetailParams } from '@/lib/validations/admin/greyBoard';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation<GetGreyBoardDetailParams>(
  getGreyBoardDetailSchema,
  async (request: AuthenticatedRequest, validatedQuery: GetGreyBoardDetailParams) => {
    // 查询灰板纸详细信息
    const greyBoard = await prisma.greyBoard.findFirst({
      where: {
        id: validatedQuery.id,
        isDel: false,
      },
    });

    assertExists(greyBoard, ErrorCode.MATERIAL_NOT_FOUND, '灰板纸不存在');

    return successResponse(greyBoard, '获取灰板纸详情成功');
  }
); 
export const POST = withInternalAuth(handler);