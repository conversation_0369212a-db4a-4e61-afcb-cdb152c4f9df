/**
 * 类型定义文件
 * 提供严格的TypeScript类型定义，替换any类型
 */

/**
 * 工艺数据基础接口
 */
export interface ProcessDataBase {
  id: number;
  name: string;
  price: number;
  basePrice?: number;
  unit: string;
  remark?: string;
}

/**
 * 印刷工艺数据接口
 */
export interface PrintingProcessData extends ProcessDataBase {
  machineModel: string;
  maxWidth: number;
  maxLength: number;
  ctpPlateFee: number;
  spotColorFee: number;
  price1000_1999: number;
  price2000_2999: number;
  price3000_3999: number;
  price4000_4999: number;
  price5000_5999: number;
  price6000_6999: number;
  price7000_7999: number;
  price8000_8999: number;
  price9000_9999: number;
  price10000Plus: number;
}

/**
 * 覆膜工艺数据接口
 */
export interface LaminatingProcessData extends ProcessDataBase {
  filmType: string;
  thickness: number;
}

/**
 * 丝印工艺数据接口
 */
export interface SilkScreenProcessData extends ProcessDataBase {
  colorCount: number;
  meshCount: number;
}

/**
 * 烫金工艺数据接口
 */
export interface HotStampingProcessData extends ProcessDataBase {
  foilType: string;
  temperature: number;
}

/**
 * 压纹工艺数据接口
 */
export interface TexturingProcessData extends ProcessDataBase {
  textureType: string;
  textureVersion: number;
  priceBelow1000: number;
  price1000_1999: number;
  price2000_3999: number;
  price4000Plus: number;
}

/**
 * 凹凸工艺数据接口
 */
export interface EmbossingProcessData extends ProcessDataBase {
  salary: number;
  salaryBasePrice?: number;
  embossingType: string;
}

/**
 * 液压工艺数据接口
 */
export interface HydraulicProcessData extends ProcessDataBase {
  salary: number;
  salaryBasePrice?: number;
  pressure: number;
}

/**
 * 模切工艺数据接口
 */
export interface DieCuttingProcessData extends ProcessDataBase {
  bladeType: string;
  complexity: string;
}

/**
 * 刀版费数据接口
 */
export interface DieCuttingPlateFeeData extends ProcessDataBase {
  impositionQuantity?: number;
  plateType: string;
}

/**
 * 工艺计算参数接口
 */
export interface ProcessCalculationParams {
  quantity: number;
  unit: string;
  dimensions?: {
    length: number;
    width: number;
  };
  partGroupId?: string;
}

/**
 * 工艺计算结果接口
 */
export interface ProcessCalculationResult {
  unitPrice: number;
  totalPrice: number;
  quantity: number;
  unit: string;
  usedBasePrice?: boolean;
  priceSource?: string;
  materialCost?: number;
  salaryCost?: number;
}

/**
 * 材料价格计算参数接口
 */
export interface MaterialPriceCalculationParams {
  materialType: 'paper' | 'specialPaper' | 'greyBoard' | 'corrugated';
  materialId: number;
  materialName: string;
  specification: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  weight?: number;
  dimensions?: {
    width: number;
    height: number;
  };
  structurePrice?: number;
}

/**
 * 材料价格计算结果接口
 */
export interface MaterialPriceCalculationResult {
  materialType: string;
  materialName: string;
  specification: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  totalCost: number;
  sheetsNeeded: number;
  partGroups: string[];
}

/**
 * 公式计算作用域接口
 */
export interface FormulaCalculationScope {
  数量: number;
  材料费: number;
  工艺费: number;
  配件费: number;
  加工费: number;
  [key: string]: number;
}

/**
 * 拼版详情接口
 */
export interface ImpositionDetail {
  partGroupName: string;
  impositionX: number;
  impositionY: number;
  totalImposition: number;
  efficiency: number;
  sheetsNeeded: number;
  materialSize: {
    width: number;
    height: number;
  };
}

/**
 * 材料费用详情扩展接口
 */
export interface ExtendedMaterialCostDetail {
  materialType: string;
  materialName: string;
  specification: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  totalCost: number;
  sheetsNeeded: number;
  partGroups: string[];
  impositionDetails?: ImpositionDetail[];
}

/**
 * 工艺费用详情接口
 */
export interface ProcessCostDetail {
  partGroupName: string;
  processType: string;
  processName: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  totalPrice: number;
  parameters?: Record<string, any>;
}

/**
 * 配件费用详情接口
 */
export interface AccessoryCostDetail {
  accessoryType: 'regular' | 'giftBox';
  accessoryName: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  totalPrice: number;
  parameters?: Record<string, any>;
}

/**
 * 报价汇总详情接口
 */
export interface QuotationSummaryDetail {
  materialCostDetails: ExtendedMaterialCostDetail[];
  processCostDetails: ProcessCostDetail[];
  accessoryCostDetails: AccessoryCostDetail[];
  processingFeeCostDetails: any[];
  formulaCostDetails: any[];
  totalMaterialCost: number;
  totalProcessCost: number;
  totalAccessoryCost: number;
  totalProcessingFeeCost: number;
  totalFormulaCost: number;
  grandTotal: number;
}

/**
 * 验证工艺数据类型
 */
export const isValidProcessData = (data: any, type: string): boolean => {
  if (!data || typeof data !== 'object') return false;
  
  const requiredFields = ['id', 'name', 'price', 'unit'];
  return requiredFields.every(field => field in data);
};

/**
 * 验证材料价格计算参数
 */
export const isValidMaterialPriceParams = (params: any): params is MaterialPriceCalculationParams => {
  return params &&
    typeof params.materialType === 'string' &&
    typeof params.materialId === 'number' &&
    typeof params.materialName === 'string' &&
    typeof params.quantity === 'number' &&
    typeof params.unit === 'string' &&
    typeof params.unitPrice === 'number';
};

/**
 * 验证工艺计算参数
 */
export const isValidProcessCalculationParams = (params: any): params is ProcessCalculationParams => {
  return params &&
    typeof params.quantity === 'number' &&
    typeof params.unit === 'string' &&
    params.quantity >= 0;
};
