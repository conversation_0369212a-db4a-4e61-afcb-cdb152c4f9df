'use client';

import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Typography, message, Checkbox, Space } from 'antd';
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { login } from '@/services/auth';
import { LoginRequest } from '@/types/user';

const { Title, Paragraph } = Typography;

export default function LoginPage() {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, setUser } = useAuth();

  // 获取重定向URL
  const redirectUrl = searchParams.get('redirect') || '/admin';

  // 如果已经登录，直接重定向
  useEffect(() => {
    if (user) {
      router.replace(redirectUrl);
    }
  }, [user, router, redirectUrl]);

  const handleSubmit = async (values: LoginRequest) => {
    setLoading(true);
    try {
      const result = await login(values);
      
      if (result.success && result.data) {
        message.success('登录成功');
        setUser(result.data.user);
        
        // 延迟一下再跳转，让用户看到成功消息
        setTimeout(() => {
          router.replace(redirectUrl);
        }, 500);
      } else {
        message.error(result.message || '登录失败');
      }
    } catch (error: any) {
      console.error('登录失败:', error);
      message.error(error.message || '登录失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 如果已经登录，显示加载状态
  if (user) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '100vh',
        background: '#f0f2f5'
      }}>
        <Card style={{ width: 400, textAlign: 'center' }}>
          <Space direction="vertical" size="large">
            <LoginOutlined style={{ fontSize: 48, color: '#1890ff' }} />
            <div>
              <Title level={4}>正在跳转...</Title>
              <Paragraph type="secondary">您已登录，正在跳转到管理后台</Paragraph>
            </div>
          </Space>
        </Card>
      </div>
    );
  }

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      minHeight: '100vh',
      background: '#f0f2f5',
      padding: '20px'
    }}>
      <Card 
        style={{ 
          width: '100%', 
          maxWidth: 400,
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
        }}
      >
        <div style={{ textAlign: 'center', marginBottom: 32 }}>
          <Title level={2} style={{ margin: 0 }}>
            管理后台登录
          </Title>
          <Paragraph type="secondary" style={{ margin: '8px 0 0 0' }}>
            请使用您的账户登录管理系统
          </Paragraph>
        </div>

        <Form
          form={form}
          name="login"
          onFinish={handleSubmit}
          autoComplete="off"
          size="large"
        >
          <Form.Item
            name="username"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 1, message: '用户名不能为空' }
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="手机号或邮箱"
              autoComplete="username"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 1, message: '密码不能为空' }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="密码"
              autoComplete="current-password"
            />
          </Form.Item>

          <Form.Item name="rememberMe" valuePropName="checked">
            <Checkbox>记住我</Checkbox>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0 }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              block
              icon={<LoginOutlined />}
            >
              登录
            </Button>
          </Form.Item>
        </Form>

        <div style={{ 
          marginTop: 24, 
          padding: 16, 
          background: '#fafafa', 
          borderRadius: 6,
          textAlign: 'center'
        }}>
          <Paragraph style={{ margin: 0, fontSize: 12 }} type="secondary">
            仅限管理员和内部用户访问
            <br />
            如需账户请联系系统管理员
          </Paragraph>
        </div>
      </Card>
    </div>
  );
}
