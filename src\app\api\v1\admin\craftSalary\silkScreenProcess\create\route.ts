import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { CreateSilkScreenProcessData, createSilkScreenProcessSchema } from '@/lib/validations/admin/silkScreenProcess';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation(
  createSilkScreenProcessSchema,
  async (request: NextRequest, validatedData: CreateSilkScreenProcessData) => {
    const data = validatedData;

    // 检查丝印工艺名称是否重复
    const existingSilkScreenProcess = await prisma.silkScreenProcess.findFirst({
      where: {
        name: data.name,
        isDel: false,
      },
    });

    assert(!existingSilkScreenProcess, ErrorCode.DUPLICATE_ENTRY, '丝印工艺名称已存在');

    // 创建丝印工艺
    const silkScreenProcess = await prisma.silkScreenProcess.create({
      data: {
        name: data.name,
        unitPrice: data.unitPrice,
        unit: data.unit,
        basePrice: data.basePrice,
        materialFee: data.materialFee,
        remark: data.remark,
      },
    });

    return successResponse(silkScreenProcess, '创建丝印工艺成功');
  }
); 