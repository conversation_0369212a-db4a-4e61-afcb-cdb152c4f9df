import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const deleteLaminatingProcessSchema = z.object({
  id: z.number().int().positive(),
});

const handler = withValidation(
  deleteLaminatingProcessSchema,
  async (request: AuthenticatedRequest, validatedData: z.infer<typeof deleteLaminatingProcessSchema>) => {
    const { id } = validatedData;

    // 检查对裱工艺是否存在
    const existingLaminatingProcess = await prisma.laminatingProcess.findFirst({
      where: {
        id: id,
        isDel: false,
      },
    });

    assert(!!existingLaminatingProcess, ErrorCode.NOT_FOUND, '对裱工艺不存在');

    // 软删除对裱工艺
    await prisma.laminatingProcess.update({
      where: { id },
      data: { isDel: true },
    });

    return successResponse({ id }, '删除对裱工艺成功');
  }
); 
export const POST = withInternalAuth(handler);