'use client';

import React, { useEffect, useState } from 'react';
import { Typography, Breadcrumb } from 'antd';
import { HomeOutlined, InboxOutlined, CalculatorOutlined } from '@ant-design/icons';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { perfLog, apiCallTracker } from '@/lib/utils/perfLog';
import { boxApi } from '@/services/adminApi';
import { Box } from '@/types/box';
import BoxCalculationWizard from './components/BoxCalculationWizard';

const { Title } = Typography;

/**
 * 盒型计算主页面
 */
export default function BoxCalculationPage() {
  const [sourceBox, setSourceBox] = useState<Box | null>(null);
  const [loading, setLoading] = useState(false);
  const searchParams = useSearchParams();
  const boxId = searchParams.get('boxId');

  // 获取来源盒型信息
  useEffect(() => {
    if (boxId) {
      // 立即设置loading状态，防止子组件同时发起请求
      setLoading(true);
      fetchBoxDetail(parseInt(boxId));
    }
  }, [boxId]);

  const fetchBoxDetail = async (id: number) => {
    const apiKey = `page-box-getDetail-${id}`;

    if (!apiCallTracker.canCall(apiKey)) {
      perfLog.debug('页面级盒型详情API调用被阻止，防止重复调用');
      return;
    }

    try {
      setLoading(true);
      const result = await boxApi.getDetail(id);

      if (result.success && result.data) {
        setSourceBox(result.data);
      }
    } catch (error) {
      perfLog.error('获取盒型详情失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const breadcrumbItems = [
    {
      title: (
        <Link href="/admin" className="flex items-center">
          <HomeOutlined className="mr-1" />
          管理后台
        </Link>
      ),
    },
    {
      title: (
        <Link href="/admin/box" className="flex items-center">
          <InboxOutlined className="mr-1" />
          盒型管理
        </Link>
      ),
    },
    {
      title: (
        <span className="flex items-center">
          <CalculatorOutlined className="mr-1" />
          盒型计算
          {sourceBox && ` - ${sourceBox.name}`}
        </span>
      ),
    },
  ];

  // 如果有 boxId 但还在加载中，显示加载状态
  if (boxId && loading) {
    return (
      <div className="box-calculation-page">
        {/* 面包屑导航 */}
        <div className="bg-white px-6 py-4 mb-6 border-b border-gray-200">
          <div className="max-w-7xl mx-auto">
            <Breadcrumb items={breadcrumbItems} className="mb-2" />

            <Title level={2} className="mb-0">
              盒型计算与报价
            </Title>
            <p className="text-gray-600 mt-2">
              正在加载盒型数据...
            </p>
          </div>
        </div>

        {/* 加载状态 */}
        <div className="flex justify-center items-center min-h-96">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
            <div className="text-gray-600">正在加载盒型数据，请稍候...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="box-calculation-page">
      {/* 面包屑导航 */}
      <div className="bg-white px-6 py-4 mb-6 border-b border-gray-200">
        <div className="max-w-7xl mx-auto">
          <Breadcrumb items={breadcrumbItems} className="mb-2" />
        </div>
      </div>

      {/* 主要内容 */}
      <BoxCalculationWizard
        sourceBoxId={boxId ? parseInt(boxId) : undefined}
        sourceBox={sourceBox}
        pageLoading={loading}
      />
    </div>
  );
} 