import { PaginationParams, SearchParams } from './common';

// 用户角色枚举
export enum UserRole {
  USER = 'user',
  SUPER_USER = 'super_user', 
  INTERNAL_USER = 'internal_user',
  ADMIN = 'admin'
}

// 用户状态枚举
export enum UserState {
  DISABLED = 0,
  ENABLED = 1
}

// 用户基础信息类型
export interface User {
  id: number;
  name: string;
  phone: string;
  email?: string | null;
  role: UserRole;
  expiresAt?: Date | null;
  currentLoginIp?: string | null;
  currentLoginAt?: Date | null;
  lastLoginIp?: string | null;
  lastLoginAt?: Date | null;
  state: UserState;
  isDel: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 用户列表项类型（不包含敏感信息）
export interface UserListItem {
  id: number;
  name: string;
  phone: string;
  email?: string | null;
  role: UserRole;
  expiresAt?: Date | null;
  lastLoginAt?: Date | null;
  state: UserState;
  createdAt: Date;
  updatedAt: Date;
}

// 当前登录用户信息类型
export interface CurrentUser {
  id: number;
  name: string;
  phone: string;
  email?: string | null;
  role: UserRole;
  expiresAt?: Date | null;
}

// 用户登录请求类型
export interface LoginRequest {
  username: string; // 可以是手机号、邮箱或用户名
  password: string;
  rememberMe?: boolean;
}

// 用户登录响应类型
export interface LoginResponse {
  user: CurrentUser;
  token: string;
  expiresIn: number; // token过期时间（秒）
}

// 用户创建请求类型
export interface CreateUserRequest {
  name: string;
  phone: string;
  email?: string;
  password: string;
  role: UserRole;
  expiresAt?: string; // ISO日期字符串，仅超级用户需要
  state?: UserState;
}

// 用户更新请求类型
export interface UpdateUserRequest {
  id: number;
  name?: string;
  phone?: string;
  email?: string;
  password?: string; // 可选，不更新密码时不传
  role?: UserRole;
  expiresAt?: string | null; // ISO日期字符串或null
  state?: UserState;
}

// 用户查询参数类型
export interface QueryUserParams extends SearchParams {
  role?: UserRole;
  state?: UserState;
  phone?: string;
  email?: string;
  name?: string;
}

// 用户状态更新请求类型
export interface UpdateUserStateRequest {
  id: number;
  state: UserState;
}

// 用户删除请求类型
export interface DeleteUserRequest {
  id: number;
}

// 密码修改请求类型
export interface ChangePasswordRequest {
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// JWT Token载荷类型
export interface JWTPayload {
  userId: number;
  role: UserRole;
  phone: string;
  iat?: number; // 签发时间
  exp?: number; // 过期时间
}

// 用户角色选项类型
export interface UserRoleOption {
  label: string;
  value: UserRole;
  description?: string;
}

// 用户状态选项类型
export interface UserStateOption {
  label: string;
  value: UserState;
  color?: string;
}

// 用户角色选项数据
export const USER_ROLE_OPTIONS: UserRoleOption[] = [
  { label: '普通用户', value: UserRole.USER, description: '普通用户权限' },
  { label: '超级用户', value: UserRole.SUPER_USER, description: '超级用户权限，有到期时间限制' },
  { label: '内部用户', value: UserRole.INTERNAL_USER, description: '内部用户权限' },
  { label: '管理员', value: UserRole.ADMIN, description: '管理员权限' }
];

// 用户状态选项数据
export const USER_STATE_OPTIONS: UserStateOption[] = [
  { label: '启用', value: UserState.ENABLED, color: 'green' },
  { label: '禁用', value: UserState.DISABLED, color: 'red' }
];

// 用户角色中文映射
export const USER_ROLE_LABELS: Record<UserRole, string> = {
  [UserRole.USER]: '普通用户',
  [UserRole.SUPER_USER]: '超级用户',
  [UserRole.INTERNAL_USER]: '内部用户',
  [UserRole.ADMIN]: '管理员'
};

// 用户状态中文映射
export const USER_STATE_LABELS: Record<UserState, string> = {
  [UserState.ENABLED]: '启用',
  [UserState.DISABLED]: '禁用'
};
