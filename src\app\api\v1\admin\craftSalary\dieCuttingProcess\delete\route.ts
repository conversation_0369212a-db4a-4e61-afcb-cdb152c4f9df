import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const deleteDieCuttingProcessSchema = z.object({
  id: z.coerce.number().positive('ID必须是正整数')
});

const handler = withValidation(
  deleteDieCuttingProcessSchema,
  async (request: AuthenticatedRequest, validatedQuery: any) => {
    const data = validatedQuery;

    // 检查模切工艺是否存在
    const existingProcess = await prisma.dieCuttingProcess.findFirst({
      where: {
        id: data.id,
        isDel: false,
      },
    });

    assertExists(existingProcess, ErrorCode.RESOURCE_NOT_FOUND, '模切工艺不存在');

    // 软删除模切工艺
    const result = await prisma.dieCuttingProcess.update({
      where: { id: data.id },
      data: {
        isDel: true,
      },
    });

    return successResponse(
      { id: result.id },
      '删除模切工艺成功'
    );
  }
);

export const POST = withInternalAuth(handler);