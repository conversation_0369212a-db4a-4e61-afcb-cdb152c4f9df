import { z } from 'zod';
import { PROCESSING_FEE_UNITS } from '@/types/craftSalary';

// 加工费列表查询参数验证
export const processingFeeListParamsSchema = z.object({
  page: z.number().int().positive().optional().default(1),
  pageSize: z.number().int().positive().max(100).optional().default(10),
  search: z.string().max(100).optional(),
  unit: z.enum(PROCESSING_FEE_UNITS).optional(),
  sortBy: z.string().optional().default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc')
});

// 加工费创建验证
export const createProcessingFeeSchema = z.object({
  name: z.string().min(1, '名称不能为空').max(100, '名称长度不能超过100个字符'),
  unitPrice: z.number().min(0, '单价不能为负数'),
  unit: z.enum(PROCESSING_FEE_UNITS, {
    errorMap: () => ({ message: '请选择有效的单位' })
  }),
  basePrice: z.number().min(0, '起步价不能为负数'),
  remark: z.string().max(500, '备注长度不能超过500个字符').optional()
});

// 加工费更新验证
export const updateProcessingFeeSchema = createProcessingFeeSchema.extend({
  id: z.number().int().positive('ID必须是正数')
});

// 加工费删除验证
export const deleteProcessingFeeSchema = z.object({
  id: z.number().int().positive('ID必须是正数')
});

// 固定参数更新验证
export const updateProcessingParamsSchema = z.object({
  pvcFilm: z.number().min(0, 'PVC贴膜价格不能为负数'),
  slottingSalary: z.number().min(0, '开槽工资不能为负数'),
  slottingBasePrice: z.number().min(0, '开槽起步价不能为负数'),
  blisterPlate: z.number().min(0, '吸塑版价格不能为负数'),
  blisterBasePrice: z.number().min(0, '吸塑起步价不能为负数'),
  highFrequencyPlate: z.number().min(0, '高频机版价格不能为负数'),
  highFrequencyBasePrice: z.number().min(0, '高频机起步价不能为负数'),
  sprayCodeFee: z.number().min(0, '喷码费用不能为负数'),
  sprayCodeBasePrice: z.number().min(0, '喷码起步价不能为负数'),
  inspectionFee: z.number().min(0, '检验费用不能为负数'),
  inspectionBasePrice: z.number().min(0, '检验起步价不能为负数')
});

// 表单数据转换函数
export const convertProcessingFeeFormData = (formData: any) => {
  return {
    ...formData,
    unitPrice: parseFloat(formData.unitPrice) || 0,
    basePrice: parseFloat(formData.basePrice) || 0
  };
};

export const convertProcessingParamsFormData = (formData: any) => {
  return {
    pvcFilm: parseFloat(formData.pvcFilm) || 0,
    slottingSalary: parseFloat(formData.slottingSalary) || 0,
    slottingBasePrice: parseFloat(formData.slottingBasePrice) || 0,
    blisterPlate: parseFloat(formData.blisterPlate) || 0,
    blisterBasePrice: parseFloat(formData.blisterBasePrice) || 0,
    highFrequencyPlate: parseFloat(formData.highFrequencyPlate) || 0,
    highFrequencyBasePrice: parseFloat(formData.highFrequencyBasePrice) || 0,
    sprayCodeFee: parseFloat(formData.sprayCodeFee) || 0,
    sprayCodeBasePrice: parseFloat(formData.sprayCodeBasePrice) || 0,
    inspectionFee: parseFloat(formData.inspectionFee) || 0,
    inspectionBasePrice: parseFloat(formData.inspectionBasePrice) || 0
  };
}; 