# 印刷包装盒报价系统开发说明文档

## 1. 系统概述

印刷报价系统通过自动化计算快速生成报价单，可显著降低手工计算成本。在印刷包装行业，报价通常包含印刷加工费和原辅材料费等项目。根据行业经验，构建此类系统需先配置印刷参数和运算公式等基础数据。本系统基于已有的数据库模型，包括盒子规格（Box）、盒子属性（BoxAttribute）、盒子部件（BoxPart）、拼版规格（BoxPackaging）等表，以及印刷机（PrintingMachine）、纸张（Paper、SpecialPaper、GreyBoard）、材料尺寸（MaterialSize）等表。本说明文档将逐步介绍各阶段的计算逻辑和数据库调用方式。

## 2. 填写盒子数量与基础属性

在此阶段，前端页面应允许用户输入盒子数量及基础尺寸（长、宽、高、折边、厚度等）。前端收集的数据将写入 **Box** 表，例如 `box.length`、`box.width`、`box.height`、`box.quantity` 等字段；若有可选属性（如颜色数、纸质等级等），可在 **BoxAttribute** 表中存储（字段如 `boxAttribute.colorCount`、`boxAttribute.isGoldStamp` 等）。设计建议：使用多行输入或表单，将数量和基础属性放在同一界面，支持动态添加多组盒子（即多行 Box 记录），并允许返回修改。字段与表结构关系：Box 表为报价项目的主表，BoxAttribute 表通过外键关联 Box，用于保存每个盒子的额外属性。

## 3. 部件定义与合并逻辑

若一个盒型由多个部件组成（如盒盖、盒底、内衬等），使用 **BoxPart** 表定义各部件，字段可包括 `part.name`、`part.length`、`part.width`、`part.height`、`part.count` 等，并关联到对应的 Box。系统应允许用户手工添加多个部件，也可提供自动合并功能：如将尺寸相同的部件合并为一项统一计算面积或价格。合并后对所有部件统一计算，用于后续拼版和报价。页面设计建议：提供部件列表，支持增删改；在保存时后端可检查并自动合并相同尺寸的部件记录，简化计算。字段与表关系：Box 和 BoxPart 是一对多关系，合并逻辑可在业务层按照尺寸或类型进行分组。

## 4. 拼版逻辑

**BoxPackaging** 表用于记录排版公式和包装展开尺寸。系统需自动计算每组部件在印刷时的排版方案。\*\*拼版（Imposition）\*\*即将部件图稿在印刷大张纸上排列。开发时可根据 BoxPart 和所选材料的尺寸计算“几开”数量。例如，若选用正度纸(787×1092 mm)或大度纸(889×1194 mm)，系统需计算一整张纸能排几张部件成品。一般做法：在 BoxPackaging 表存入公式，如排版尺寸 = `(成品长 + 边距) × (成品宽 + 边距)`，例如示例公式 `(成品长 + 3) * (成品宽 + 3)` 表示每个成品留边3mm后的排版占位。系统可循环尝试将多件产品按该公式铺排在纸张上，得出每令（500张）需要的纸张令数。参考行业实践，可借助排版软件算法自动计算排版数量。需要注意，排版尺寸应考虑留边和对折折叠，如南方制版建议：“拼板一定按这些尺寸拼板上机”（例如4开正度尺寸54×39cm，大度44×59cm）。

**示例计算：** 若成品长宽分别为100×80mm，留边各3mm，则单件排版尺寸为103×83mm；对比材料全开尺寸，可算出每张纸能排约 `(1092 ÷ 103) * (787 ÷ 83)` 张，取整后作为每令数量。最终排版数量影响纸张用量和印张数。

## 5. 材料与工艺选择

### 5.1 印刷机选型

根据包装盒展开后的图稿尺寸，从 **PrintingMachine** 和 **Printing** 表中选择适用的印刷机型。不同印刷机最大幅面不同，例如 Komori LITHRONE G44 最大纸张尺寸为 840×1150mm，可满足大幅面拼版印刷需求；小型机型如 40 寸机适合小批量印刷。系统应将所需的展开尺寸与机器最大纸张尺寸比对，自动筛选可用机型并取其印刷单价和版材尺寸信息。字段说明：PrintingMachine 可包含 `machine.maxWidth`、`machine.maxHeight` 等；Printing 表关联机器型号和印刷速度/成本等参数。

### 5.2 材料选型（纸张、灰板、特种纸等）

用户根据盒型用途选择纸张（如铜版纸、白卡纸等）、灰板或特种纸等。材料表如 **Paper**、**SpecialPaper**、**GreyBoard** 存有不同材质对应的克重和单价信息。需要根据产品展开后尺寸和所选材料的开张规格（正度、大度、特规等）计算用纸开数。例如正度纸787×1092mm，大度纸889×1194mm，灰纸板常见规格亦为这两种。系统将材料尺寸(从 **MaterialSize** 表读取)与成品展平尺寸进行比对，确定“几开”。每张纸的有效印刷面积和克重可计算单张纸重量，再结合材料单价算出材料费用。纸张用量的计算可参照印刷报价公式：

```
纸价 = 开张系数 × 克重 × 吨价 ÷ 500 ÷ 开数 × 印数 × (1 + 损耗率)
```

例如**南方制版**给出：0.531×250g×8000元/T÷500÷4开×10000张×1.1%损耗 = 5310元。其中0.531为面积系数（4开在大度纸上的占比），印数为总件数。开发者可依据此思路，将开数、克重、单价、损耗率等字段写入参数表，计算出纸费。

### 5.3 加工工艺选择

用户可为盒子选择覆膜、丝网印刷、烫金、凹凸等后道工艺。系统前端列出可选工艺（对应表 **SurfaceProcess**、**SilkScreenProcess**、**HotStampingProcess** 等），每种工艺有单价和计费方式（按面积、按数量等）。计算时，将部件面积或数量乘以对应单价加入总成本。例如，覆膜一般按覆膜面积（平方米）计价，丝印按版面计价，烫金按实际烫金面积计算。最终工艺费用累加至总价。

## 6. 配件选择

* **普通配件（Accessory）：** 例如手提带、包装带等。Accessory 表记录配件名称、单位和单价。前端允许用户输入或选择需要的配件及数量（单位可为米、对、平方等）。系统计算方式：根据数量×单价累加费用。例如  ribbon（米）、扣件（对）、包装膜（平方米）等。
* **礼盒配件（GiftBoxAccessory）：** 专指礼盒内附赠物品（如衬纸、礼签等）。GiftBoxAccessory 表类似记录配件信息，但价格可能按体积或数量计算。用户可输入这些配件的长宽高，系统计算体积（L×W×H），再根据单价计算费用（如以体积或重量计价）。

页面设计建议：在这一阶段提供明细列表，配件可分组显示，支持增删。对于礼盒配件，需弹出输入框填写长、宽、高；开发者据此字段计算体积并估算费用。

## 7. 自定义计算公式（CustomFormula）

为了灵活应对特殊计算需求，系统支持用户自定义公式。在 **CustomFormula** 表中保存公式表达式字符串，并设置关联的参数名。例如表中可存 `长+宽` 之类的表达式。开发者在计算时先将中文转换为拼音再使用 MathJS 计算出结果。具体做法：取出 CustomFormula.expression（含变量占位符），再从已有参数字典中替换变量值，最后调用解析器执行，得到结果。例如可以用 JEXL 将字符串公式解析并计算。表达式字段解析：每个公式定义中，变量名对应前端输入或数据库字段的值，解析器会自动绑定参数求值。这样，系统即可根据自定义规则将计算结果动态添加到报价中。

## 8. 附加费用配置

除上述项目外，还需考虑设计费、打样费、模切费等固定费用。这些费用在 **ProcessingFee** 或 **ProcessingParams** 表中预设为固定项。页面上可勾选或输入相关附加费，后台直接从对应表中读取单价并乘以相应数量或套数，加到总价明细中。例如设计费固定为 1000 元/项，打样费 500 元/令，系统只需累加即可。

## 9. 打包费用计算

最后，依据包装尺寸计算打包运输费或体积费。**BoxPackaging** 表提供包装箱长宽高等字段。开发者可用公式 `体积 = 长 × 宽 × 高`（或相似公式）计算包装体积，再乘以单位费用（如元/立方米）。也可预先在表中录入不同体积区间对应的费用系数进行查表计算。示例：假设根据长宽高计算出体积0.02立方米，对应打包费单价为100元/立方米，则费用为2元。此项加入总价细项中。

## 10. 页面设计建议

* **分步填写：** 将报价流程拆分为多个步骤页面（如：盒子属性→部件→材料与工艺→配件→确认），每页仅显示当前步骤所需字段。多步骤表单可以分解复杂流程，提升完成率与用户体验。
* **进度提示与返回修改：** 在页面顶部添加步骤进度条，标明当前步骤。用户应能随时后退修改前面步骤的输入，并动态更新报价。对长表单，应提供“保存并继续”功能，让用户可中断后恢复填写。
* **数据校验与提示：** 对输入的尺寸、数量等进行格式和范围校验。若填写有误，通过悬浮提示或红色高亮提示具体错误项，保证用户能快速修正。
* **动态计算反馈：** 在配置过程中实时展示费用估算明细（如“纸费：XXX元，印刷费：YYY元…”），帮助用户决策。修改参数后自动刷新计算结果。
* **合并与批量操作：** 如果一系列部件或属性相似，提供批量操作按钮（如“一键合并所有相同部件”、“复制当前设置到多个盒型”），提高效率。

## 11. 数据表结构与字段关系

系统数据库表主要如下：

* **Box**：记录盒子基本信息（ID、名称、尺寸、数量等）。
* **BoxAttribute**：记录盒子的额外属性（外键关联 Box，字段如颜色、纸质等）。
* **BoxPart**：记录盒子各部件信息（外键关联 Box，字段包括部件名、尺寸、数量等）。
* **BoxPackaging**：记录拼版和包装相关数据（可存放展开图长宽高公式、打包尺寸等）。
* **PrintingMachine/Printing**：记录印刷机型号及参数（如最大幅面、印刷成本、速度等）。
* **Paper/SpecialPaper/GreyBoard**：各类材料表，包含材质类型、克重、单价、可用尺寸等。
* **MaterialSize**：标准开张尺寸表（如正度、大度、特殊规格等），用于开数计算。
* **SurfaceProcess/SilkScreenProcess/HotStampingProcess**：工艺费用表，记录覆膜、丝印、烫金等单价或参数。
* **Accessory/GiftBoxAccessory**：配件表，记录配件名称、单位、单价。礼盒配件表可额外保存长宽高字段用于体积计算。
* **CustomFormula**：用户自定义计算公式表，字段包括公式表达式和描述。
* **ProcessingFee/ProcessingParams**：固定费用表，记录设计费、样费、模切费等项目和对应费用。

以上表通过外键和关联字段形成业务逻辑网。例如 Box–BoxPart 一对多，Box–BoxPackaging 一对一或一对多，Accessory 表单独存取后在报价时引用，CustomFormula 公式则可对接任意计算节点。

## 12. 公式示例

* **拼版公式示例：** 设成品长 L、宽 W，各留边3mm，则排版尺寸公式为 `(L + 3) * (W + 3)`。实际实现时可将公式写入 BoxPackaging 表，由解析器计算。
* **纸张用量公式：** 根据材料规格和开数，可使用面积系数公式。例如 4 开（大度纸）面积系数约为0.531，则纸费计算公式可表示为：

  ```
  纸费 = 0.531 × 纸张克重(克/平米) × 吨价(元/吨) ÷ 500 ÷ (开数) × (印数) × (1 + 损耗率)  
  ```

  如所示。
* **加工工艺公式：** 例如覆膜费 = `覆膜单价 × 覆膜面积`；丝印费 = `丝印单价 × 版面张数`。烫金费常按烫金面积计算。
* **体积计算公式：** 包装箱体积 = 长 × 宽 × 高，用于计算打包费。礼盒配件体积亦同理。
* **示例自定义公式：** 如 CustomFormula 表中有公式 `%length% * %width% / 1000`，解析时替换%length%、%width%为具体数值再计算。

## 13. 表达式字段解析说明

系统可借助表达式引擎解析自定义公式。例如使用 Apache JEXL，将数据库中存储的公式字符串解析为可执行表达式。具体流程：从 CustomFormula 表读取公式表达式（例如 `"${length}*${width}"`），然后在运行时构建变量映射表，把已有的参数值（如 length、width）注入到表达式中，最后调用引擎执行，得到计算结果。这样，无论是材料用量、工艺费用还是自定义规则，都可以通过此机制灵活实现。

## 14. 输出格式建议（报价单明细）

最终生成的报价单应详细列出各项成本构成，便于客户与生产参考。一般包含：

* **盒型信息：** 盒子规格及数量说明。
* **纸张费：** 纸张克重、开数、令数及总费用。
* **印刷费：** 颜色数、版费与印刷数量及总费用。
* **加工费：** 覆膜、丝印、烫金、压痕等工艺费用明细。
* **配件费：** 普通配件和礼盒配件的单价与总费用。
* **附加费：** 如设计费、打样费、模切费等。
* **打包费：** 包装体积计算及费用。
* **总价：** 各项费用累加后的报价总额。

每个费用明细应明确单价计算方式（如单价、面积、数量等）和对应金额。例如：“纸张费 = 0.43kg/m² × 400g × 6000元/T ÷500 ÷ 4开 × 10000张 = XX元”。输出格式可采用表格形式，栏目清晰，可点击“导出”生成 PDF/Excel 文件供打印或发送给客户。







现在来优化盒型计算
根据不同部件中的公式进行判断
面纸长度和宽度公式都存在，则可以选择面纸
灰板纸长度和宽度公式都存在，则可以选择灰板纸

然后根据面纸和灰板纸的尺寸进行计算

面纸：
支持如下功能
1. 根据打印机尺寸和上一步计算出的展开尺寸进行最大拼版计算，并计算出拼版数量
展示为：横向拼版数量，纵向拼版数量
并计算出拼版后的尺寸，即材料尺寸，展示为：材料长，材料宽
注意 出血 拉规 咬口 等参数需要考虑进去
2. 考虑双接问题，即拼版后的尺寸是否可以被打印机打印
3. 支持部件合并，即可以合并多个部件，并计算出合并后的尺寸
4. 支持部件旋转，即可以旋转部件，并计算出旋转后的尺寸

灰板纸：
1. 支持部件合并，即可以合并多个部件，并计算出合并后的尺寸
2. 支持部件旋转，即可以旋转部件，并计算出旋转后的尺寸
计算出拼版后的尺寸，即材料尺寸，展示为：材料长，材料宽


最终输出
1. 面纸的拼版数量，拼版后的尺寸，即材料尺寸，展示为：材料长，材料宽
2. 灰板纸的拼版数量，拼版后的尺寸，即材料尺寸，展示为：材料长，材料宽

