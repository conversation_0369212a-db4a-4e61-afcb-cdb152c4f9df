import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';
import { DeleteHotStampingPlateFeeData, deleteHotStampingPlateFeeSchema } from '@/lib/validations/admin/hotStampingProcess';


const handler = withValidation(
  deleteHotStampingPlateFeeSchema,
  async (request: AuthenticatedRequest, validatedQuery: DeleteHotStampingPlateFeeData) => {
    const { id } = validatedQuery;

    // 检查烫金版费是否存在
    const existingHotStampingPlateFee = await prisma.hotStampingPlateFee.findFirst({
      where: {
        id: id,
        isDel: false,
      },
    });

    assert(!!existingHotStampingPlateFee, ErrorCode.NOT_FOUND, '烫金版费不存在');

    // 软删除烫金版费
    const deletedHotStampingPlateFee = await prisma.hotStampingPlateFee.update({
      where: { id: id },
      data: { isDel: true },
    });

    return successResponse(
      deletedHotStampingPlateFee,
      '删除烫金版费成功'
    );
  }
); 
export const POST = withInternalAuth(handler);