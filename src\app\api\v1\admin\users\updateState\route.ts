import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { updateUserStateSchema, UpdateUserStateParams } from '@/lib/validations/user';
import { withValidation } from '@/lib/middleware/errorHandler';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { UserState } from '@/types/user';

const handler = withValidation<UpdateUserStateParams>(
  updateUserStateSchema,
  async (request: AuthenticatedRequest, validatedData: UpdateUserStateParams) => {
    const { id, state } = validatedData;
    const currentUser = request.user;

    try {
      // 检查用户是否存在
      const existingUser = await prisma.user.findFirst({
        where: {
          id,
          isDel: false
        }
      });

      if (!existingUser) {
        return NextResponse.json(
          errorResponse(ErrorCode.USER_NOT_FOUND, '用户不存在'),
          { status: 404 }
        );
      }

      // 防止用户禁用自己
      if (currentUser && currentUser.userId === id && state === UserState.DISABLED) {
        return NextResponse.json(
          errorResponse(ErrorCode.FORBIDDEN, '不能禁用自己的账户'),
          { status: 403 }
        );
      }

      // 更新用户状态
      const updatedUser = await prisma.user.update({
        where: { id },
        data: {
          state,
          // 如果禁用用户，清除当前登录Token
          ...(state === UserState.DISABLED && {
            currentLoginToken: null
          })
        },
        select: {
          id: true,
          name: true,
          phone: true,
          email: true,
          role: true,
          expiresAt: true,
          state: true,
          createdAt: true,
          updatedAt: true
        }
      });

      const stateText = state === UserState.ENABLED ? '启用' : '禁用';
      
      return NextResponse.json(
        successResponse(updatedUser, `用户${stateText}成功`)
      );

    } catch (error) {
      console.error('更新用户状态失败:', error);
      return NextResponse.json(
        errorResponse(ErrorCode.INTERNAL_ERROR, '更新用户状态失败，请稍后重试'),
        { status: 500 }
      );
    }
  }
);

export const POST = withInternalAuth(handler);
