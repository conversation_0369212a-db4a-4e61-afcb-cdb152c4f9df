/**
 * 印刷模块类型定义
 */

// 印刷计价单位枚举
export const PRINTING_UNITS = [
  '元/张',
  '元/平方',
  '起步价+张'
] as const;

export type PrintingUnit = typeof PRINTING_UNITS[number];

// 印刷数据模型
export interface Printing {
  id: number;
  machineModel: string;          // 印刷机型
  basePrice: number;             // 起步价
  price1000_1999: number;        // 1000-1999价格
  price2000_2999: number;        // 2000-2999价格
  price3000_3999: number;        // 3000-3999价格
  price4000_4999: number;        // 4000-4999价格
  price5000_5999: number;        // 5000-5999价格
  price6000_6999: number;        // 6000-6999价格
  price7000_7999: number;        // 7000-7999价格
  price8000_8999: number;        // 8000-8999价格
  price9000_9999: number;        // 9000-9999价格
  price10000Plus: number;        // 10000以上价格
  unit: PrintingUnit;            // 计价单位
  ctpPlateFee: number;          // CTP板费
  spotColorFee: number;         // 专色费
  remark?: string;              // 备注
  isDel: boolean;               // 是否删除
  createdAt: string;            // 创建时间
  updatedAt: string;            // 更新时间
}

// 印刷机数据模型
export interface PrintingMachine {
  id: number;
  machineName: string;          // 印刷机名称
  maxLength: number;           // 最大印刷长度(mm)
  maxWidth: number;            // 最大印刷宽度(mm)
  remark?: string;             // 备注
  isDel: boolean;              // 是否删除
  createdAt: string;           // 创建时间
  updatedAt: string;           // 更新时间
}

// 创建印刷数据的请求类型
export interface CreatePrintingRequest {
  machineModel: string;
  basePrice: number;
  price1000_1999: number;
  price2000_2999: number;
  price3000_3999: number;
  price4000_4999: number;
  price5000_5999: number;
  price6000_6999: number;
  price7000_7999: number;
  price8000_8999: number;
  price9000_9999: number;
  price10000Plus: number;
  unit: PrintingUnit;
  ctpPlateFee: number;
  spotColorFee: number;
  remark?: string;
}

// 更新印刷数据的请求类型
export interface UpdatePrintingRequest extends Partial<CreatePrintingRequest> {
  id: number;
}

// 创建印刷机的请求类型
export interface CreatePrintingMachineRequest {
  machineName: string;
  maxLength: number;
  maxWidth: number;
  remark?: string;
}

// 更新印刷机的请求类型
export interface UpdatePrintingMachineRequest extends Partial<CreatePrintingMachineRequest> {
  id: number;
}

// 印刷数据列表查询参数
export interface PrintingListParams {
  page?: number;
  pageSize?: number;
  search?: string;
  unit?: PrintingUnit;
  sortBy?: 'createdAt' | 'updatedAt' | 'machineModel' | 'basePrice';
  sortOrder?: 'asc' | 'desc';
}

// 印刷机列表查询参数
export interface PrintingMachineListParams {
  page?: number;
  pageSize?: number;
  search?: string;
  sortBy?: 'createdAt' | 'updatedAt' | 'machineName';
  sortOrder?: 'asc' | 'desc';
}

// API响应类型
export interface PrintingResponse {
  data: Printing[];
  total: number;
  page: number;
  pageSize: number;
}

export interface PrintingMachineResponse {
  data: PrintingMachine[];
  total: number;
  page: number;
  pageSize: number;
}

export interface PrintingDetailResponse {
  data: Printing;
}

export interface PrintingMachineDetailResponse {
  data: PrintingMachine;
}

// 表单类型
export interface PrintingFormData {
  machineModel: string;
  basePrice: string;           // 表单中使用字符串，提交时转换为数字
  price1000_1999: string;
  price2000_2999: string;
  price3000_3999: string;
  price4000_4999: string;
  price5000_5999: string;
  price6000_6999: string;
  price7000_7999: string;
  price8000_8999: string;
  price9000_9999: string;
  price10000Plus: string;
  unit: PrintingUnit;
  ctpPlateFee: string;
  spotColorFee: string;
  remark?: string;
}

export interface PrintingMachineFormData {
  machineName: string;
  maxLength: string;          // 表单中使用字符串
  maxWidth: string;           // 表单中使用字符串
  remark?: string;
}

// 价格计算相关类型
export interface PriceCalculationParams {
  quantity: number;           // 数量
  printingId: number;        // 印刷配置ID
  length?: number;           // 长度(mm)
  width?: number;            // 宽度(mm)
}

export interface PriceCalculationResult {
  basePrice: number;         // 起步价
  unitPrice: number;        // 单价
  totalPrice: number;       // 总价
  ctpPlateFee: number;     // CTP板费
  spotColorFee: number;    // 专色费
  finalPrice: number;      // 最终价格
  priceRange: string;      // 价格区间描述
}

// 表格列配置类型
export interface PrintingTableColumn {
  key: string;
  title: string;
  dataIndex: string;
  width?: number;
  fixed?: 'left' | 'right';
  sorter?: boolean;
  render?: (value: any, record: Printing) => React.ReactNode;
}

export interface PrintingMachineTableColumn {
  key: string;
  title: string;
  dataIndex: string;
  width?: number;
  fixed?: 'left' | 'right';
  sorter?: boolean;
  render?: (value: any, record: PrintingMachine) => React.ReactNode;
}

// ===========================================
// 覆膜工艺模块类型定义
// ===========================================

// 覆膜工艺单位枚举
export const SURFACE_PROCESS_UNITS = ['元/平方', '元/张'] as const;
export type SurfaceProcessUnit = typeof SURFACE_PROCESS_UNITS[number];

// 覆膜工艺数据模型
export interface SurfaceProcess {
  id: number;
  name: string;                   // 名称
  price: number;                  // 价格
  unit: SurfaceProcessUnit;       // 单位
  basePrice: number;              // 起步价
  thickness: number;              // 厚度
  density: number;                // 密度
  remark?: string;                // 备注
  isDel: boolean;                 // 是否删除
  createdAt: string;              // 创建时间
  updatedAt: string;              // 更新时间
}

// 创建覆膜工艺的请求类型
export interface CreateSurfaceProcessRequest {
  name: string;
  price: number;
  unit: SurfaceProcessUnit;
  basePrice: number;
  thickness: number;
  density: number;
  remark?: string;
}

// 更新覆膜工艺的请求类型
export interface UpdateSurfaceProcessRequest extends CreateSurfaceProcessRequest {
  id: number;
}

// 覆膜工艺列表查询参数
export interface SurfaceProcessListParams {
  page?: number;
  pageSize?: number;
  search?: string;                // 搜索名称或备注
  unit?: SurfaceProcessUnit;      // 按单位筛选
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 表单类型
export interface SurfaceProcessFormData {
  name: string;
  price: string;                  // 表单中使用字符串，提交时转换为数字
  unit: SurfaceProcessUnit;
  basePrice: string;
  thickness: string;
  density: string;
  remark?: string;
}

// ===========================================
// 丝印工艺模块类型定义
// ===========================================

// 丝印工艺单位枚举
export const SILK_SCREEN_PROCESS_UNITS = ['元/平方', '元/个'] as const;
export type SilkScreenProcessUnit = typeof SILK_SCREEN_PROCESS_UNITS[number];

// 丝印工艺数据模型
export interface SilkScreenProcess {
  id: number;
  name: string;                     // 名称
  unitPrice: number;                // 单价
  unit: SilkScreenProcessUnit;      // 单位
  basePrice: number;                // 起步价
  materialFee: number;              // 材料费
  remark?: string;                  // 备注
  isDel: boolean;                   // 是否删除
  createdAt: string;                // 创建时间
  updatedAt: string;                // 更新时间
}

// 创建丝印工艺的请求类型
export interface CreateSilkScreenProcessRequest {
  name: string;
  unitPrice: number;
  unit: SilkScreenProcessUnit;
  basePrice: number;
  materialFee: number;
  remark?: string;
}

// 更新丝印工艺的请求类型
export interface UpdateSilkScreenProcessRequest extends CreateSilkScreenProcessRequest {
  id: number;
}

// 丝印工艺列表查询参数
export interface SilkScreenProcessListParams {
  page?: number;
  pageSize?: number;
  search?: string;                    // 搜索名称或备注
  unit?: SilkScreenProcessUnit;       // 按单位筛选
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 表单类型
export interface SilkScreenProcessFormData {
  name: string;
  unitPrice: string;                  // 表单中使用字符串，提交时转换为数字
  unit: SilkScreenProcessUnit;
  basePrice: string;
  materialFee: string;
  remark?: string;
}

// ===========================================
// 瓦楞工艺模块类型定义
// ===========================================

// 楞形枚举
export const FLUTE_TYPES = ['A', 'B', 'C', 'D', 'E', 'F'] as const;
export type FluteType = typeof FLUTE_TYPES[number];

// 瓦楞工艺单位枚举（固定为元/平方）
export const CORRUGATED_PROCESS_UNITS = ['元/平方'] as const;
export type CorrugatedProcessUnit = typeof CORRUGATED_PROCESS_UNITS[number];

// 瓦楞工艺数据模型
export interface CorrugatedProcess {
  id: number;
  code?: string;                    // 代号
  materialName: string;             // 材质名称
  price: number;                    // 价格
  unit: CorrugatedProcessUnit;      // 单位
  setupFee: number;                 // 上机费
  thickness: number;                // 厚度(mm)
  coreWeight1: number;              // 芯纸1
  fluteType1?: FluteType;           // 楞形1
  linerWeight1: number;             // 里纸1
  coreWeight2: number;              // 芯纸2
  fluteType2?: FluteType;           // 楞形2
  linerWeight2: number;             // 里纸2
  remark?: string;                  // 备注
  isDel: boolean;                   // 是否删除
  createdAt: string;                // 创建时间
  updatedAt: string;                // 更新时间
}

// 瓦楞率配置数据模型
export interface CorrugatedRate {
  id: number;
  fluteType: FluteType;             // 楞形
  rate: number;                     // 瓦楞率
  isDel: boolean;                   // 是否删除
  createdAt: string;                // 创建时间
  updatedAt: string;                // 更新时间
}

// 创建瓦楞工艺的请求类型
export interface CreateCorrugatedProcessRequest {
  code?: string;
  materialName: string;
  price: number;
  unit: CorrugatedProcessUnit;
  setupFee: number;
  thickness: number;
  coreWeight1: number;
  fluteType1?: FluteType;
  linerWeight1: number;
  coreWeight2: number;
  fluteType2?: FluteType;
  linerWeight2: number;
  remark?: string;
}

// 更新瓦楞工艺的请求类型
export interface UpdateCorrugatedProcessRequest extends CreateCorrugatedProcessRequest {
  id: number;
}

// 创建瓦楞率配置的请求类型
export interface CreateCorrugatedRateRequest {
  fluteType: FluteType;
  rate: number;
}

// 更新瓦楞率配置的请求类型
export interface UpdateCorrugatedRateRequest extends CreateCorrugatedRateRequest {
  id: number;
}

// 瓦楞工艺列表查询参数
export interface CorrugatedProcessListParams {
  page?: number;
  pageSize?: number;
  search?: string;                  // 搜索代号、材质名称或备注
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 瓦楞率配置列表查询参数
export interface CorrugatedRateListParams {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 表单类型
export interface CorrugatedProcessFormData {
  code?: string;
  materialName: string;
  price: string;                    // 表单中使用字符串，提交时转换为数字
  unit: CorrugatedProcessUnit;
  setupFee: string;
  thickness: string;
  coreWeight1: string;
  fluteType1?: FluteType;
  linerWeight1: string;
  coreWeight2: string;
  fluteType2?: FluteType;
  linerWeight2: string;
  remark?: string;
}

export interface CorrugatedRateFormData {
  fluteType: FluteType;
  rate: string;                     // 表单中使用字符串，提交时转换为数字
}

// ===========================================
// 对裱工艺模块类型定义
// ===========================================

// 对裱工艺单位枚举
export const LAMINATING_PROCESS_UNITS = ['元/平方', '元/张'] as const;
export type LaminatingProcessUnit = typeof LAMINATING_PROCESS_UNITS[number];

// 对裱工艺数据模型
export interface LaminatingProcess {
  id: number;
  name: string;                     // 名称
  price: number;                    // 价格
  unit: LaminatingProcessUnit;      // 单位
  basePrice: number;                // 起步价
  remark?: string;                  // 备注
  isDel: boolean;                   // 是否删除
  createdAt: string;                // 创建时间
  updatedAt: string;                // 更新时间
}

// 创建对裱工艺的请求类型
export interface CreateLaminatingProcessRequest {
  name: string;
  price: number;
  unit: LaminatingProcessUnit;
  basePrice: number;
  remark?: string;
}

// 更新对裱工艺的请求类型
export interface UpdateLaminatingProcessRequest extends CreateLaminatingProcessRequest {
  id: number;
}

// 对裱工艺列表查询参数
export interface LaminatingProcessListParams {
  page?: number;
  pageSize?: number;
  search?: string;                    // 搜索名称或备注
  unit?: LaminatingProcessUnit;       // 按单位筛选
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 表单类型
export interface LaminatingProcessFormData {
  name: string;
  price: string;                      // 表单中使用字符串，提交时转换为数字
  unit: LaminatingProcessUnit;
  basePrice: string;
  remark?: string;
}

// ===========================================
// 烫金工艺模块类型定义
// ===========================================

// 烫金工艺工资单位枚举（固定为元/张）
export const HOT_STAMPING_SALARY_UNITS = ['元/张'] as const;
export type HotStampingSalaryUnit = typeof HOT_STAMPING_SALARY_UNITS[number];

// 烫金工艺材料单位枚举（固定为元/平方）
export const HOT_STAMPING_MATERIAL_UNITS = ['元/平方'] as const;
export type HotStampingMaterialUnit = typeof HOT_STAMPING_MATERIAL_UNITS[number];

// 烫金版费单位枚举
export const HOT_STAMPING_PLATE_FEE_UNITS = ['元/个', '元/平方'] as const;
export type HotStampingPlateFeeUnit = typeof HOT_STAMPING_PLATE_FEE_UNITS[number];

// 烫金工艺数据模型
export interface HotStampingProcess {
  id: number;
  name: string;                          // 名称
  salary: number;                        // 工资，可为0
  salaryUnit: HotStampingSalaryUnit;     // 工资单位（元/张）
  materialPrice: number;                 // 材料价格
  materialUnit: HotStampingMaterialUnit; // 材料单位（元/平方）
  basePrice: number;                     // 起步价，可为0
  remark?: string;                       // 备注
  isDel: boolean;                        // 是否删除
  createdAt: string;                     // 创建时间
  updatedAt: string;                     // 更新时间
}

// 烫金版费数据模型
export interface HotStampingPlateFee {
  id: number;
  name: string;                          // 名称
  price: number;                         // 价格，可为0
  unit: HotStampingPlateFeeUnit;         // 单位
  basePrice: number;                     // 起步价，可为0
  remark?: string;                       // 备注
  isDel: boolean;                        // 是否删除
  createdAt: string;                     // 创建时间
  updatedAt: string;                     // 更新时间
}

// 创建烫金工艺的请求类型
export interface CreateHotStampingProcessRequest {
  name: string;
  salary: number;
  salaryUnit: HotStampingSalaryUnit;
  materialPrice: number;
  materialUnit: HotStampingMaterialUnit;
  basePrice: number;
  remark?: string;
}

// 更新烫金工艺的请求类型
export interface UpdateHotStampingProcessRequest extends CreateHotStampingProcessRequest {
  id: number;
}

// 烫金工艺列表查询参数
export interface HotStampingProcessListParams {
  page?: number;
  pageSize?: number;
  search?: string;           // 名称搜索
}

// 创建烫金版费的请求类型
export interface CreateHotStampingPlateFeeRequest {
  name: string;
  price: number;
  unit: HotStampingPlateFeeUnit;
  basePrice: number;
  remark?: string;
}

// 更新烫金版费的请求类型
export interface UpdateHotStampingPlateFeeRequest extends CreateHotStampingPlateFeeRequest {
  id: number;
}

// 烫金版费列表查询参数
export interface HotStampingPlateFeeListParams {
  page?: number;
  pageSize?: number;
  search?: string;           // 名称搜索
}

// 表单类型
export interface HotStampingProcessFormData {
  name: string;
  salary: string;                         // 表单中使用字符串，提交时转换为数字
  salaryUnit: HotStampingSalaryUnit;
  materialPrice: string;
  materialUnit: HotStampingMaterialUnit;
  basePrice: string;
  remark?: string;
}

export interface HotStampingPlateFeeFormData {
  name: string;
  price: string;                          // 表单中使用字符串，提交时转换为数字
  unit: HotStampingPlateFeeUnit;
  basePrice: string;
  remark?: string;
}

// ===========================================
// 凹凸工艺模块类型定义
// ===========================================

// 压纹工艺单位枚举（固定为元/张）
export const TEXTURING_PROCESS_UNITS = ['元/张'] as const;
export type TexturingProcessUnit = typeof TEXTURING_PROCESS_UNITS[number];

// 凹凸工艺和液压工艺单位枚举
export const EMBOSSING_HYDRAULIC_PROCESS_UNITS = ['元/个', '元/平方'] as const;
export type EmbossingHydraulicProcessUnit = typeof EMBOSSING_HYDRAULIC_PROCESS_UNITS[number];

// 压纹工艺数据模型
export interface TexturingProcess {
  id: number;
  name: string;                     // 名称
  textureVersion: number;           // 压纹版，可为0
  unit: TexturingProcessUnit;       // 单位（元/张）
  priceBelow1000: number;           // 数量1000以下总价
  price1000_1999: number;           // 数量1000-1999总价
  price2000_3999: number;           // 数量2000-3999总价
  price4000Plus: number;            // 数量4000以上单价
  remark?: string;                  // 备注
  isDel: boolean;                   // 是否删除
  createdAt: string;                // 创建时间
  updatedAt: string;                // 更新时间
}

// 凹凸工艺数据模型
export interface EmbossingProcess {
  id: number;
  name: string;                           // 名称
  price: number;                          // 价格，可以为0
  unit: EmbossingHydraulicProcessUnit;    // 单位
  basePrice: number;                      // 起步价
  salary: number;                         // 工资单价
  salaryBasePrice: number;                // 工资起步价，可为0
  remark?: string;                        // 备注
  isDel: boolean;                         // 是否删除
  createdAt: string;                      // 创建时间
  updatedAt: string;                      // 更新时间
}

// 液压工艺数据模型
export interface HydraulicProcess {
  id: number;
  name: string;                           // 名称
  price: number;                          // 价格，可以为0
  unit: EmbossingHydraulicProcessUnit;    // 单位
  basePrice: number;                      // 起步价
  salary: number;                         // 工资单价
  salaryBasePrice: number;                // 工资起步价，可为0
  remark?: string;                        // 备注
  isDel: boolean;                         // 是否删除
  createdAt: string;                      // 创建时间
  updatedAt: string;                      // 更新时间
}

// 创建压纹工艺的请求类型
export interface CreateTexturingProcessRequest {
  name: string;
  textureVersion: number;
  unit: TexturingProcessUnit;
  priceBelow1000: number;
  price1000_1999: number;
  price2000_3999: number;
  price4000Plus: number;
  remark?: string;
}

// 更新压纹工艺的请求类型
export interface UpdateTexturingProcessRequest extends CreateTexturingProcessRequest {
  id: number;
}

// 创建凹凸工艺的请求类型
export interface CreateEmbossingProcessRequest {
  name: string;
  price: number;
  unit: EmbossingHydraulicProcessUnit;
  basePrice: number;
  salary: number;
  salaryBasePrice: number;
  remark?: string;
}

// 更新凹凸工艺的请求类型
export interface UpdateEmbossingProcessRequest extends CreateEmbossingProcessRequest {
  id: number;
}

// 创建液压工艺的请求类型
export interface CreateHydraulicProcessRequest {
  name: string;
  price: number;
  unit: EmbossingHydraulicProcessUnit;
  basePrice: number;
  salary: number;
  salaryBasePrice: number;
  remark?: string;
}

// 更新液压工艺的请求类型
export interface UpdateHydraulicProcessRequest extends CreateHydraulicProcessRequest {
  id: number;
}

// 压纹工艺列表查询参数
export interface TexturingProcessListParams {
  page?: number;
  pageSize?: number;
  search?: string;                    // 搜索名称或备注
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 凹凸工艺列表查询参数
export interface EmbossingProcessListParams {
  page?: number;
  pageSize?: number;
  search?: string;                    // 搜索名称或备注
  unit?: EmbossingHydraulicProcessUnit; // 按单位筛选
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 液压工艺列表查询参数
export interface HydraulicProcessListParams {
  page?: number;
  pageSize?: number;
  search?: string;                    // 搜索名称或备注
  unit?: EmbossingHydraulicProcessUnit; // 按单位筛选
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 表单类型
export interface TexturingProcessFormData {
  name: string;
  textureVersion: string;             // 表单中使用字符串，提交时转换为数字
  unit: TexturingProcessUnit;
  priceBelow1000: string;
  price1000_1999: string;
  price2000_3999: string;
  price4000Plus: string;
  remark?: string;
}

export interface EmbossingProcessFormData {
  name: string;
  price: string;                      // 表单中使用字符串，提交时转换为数字
  unit: EmbossingHydraulicProcessUnit;
  basePrice: string;
  salary: string;
  salaryBasePrice: string;
  remark?: string;
}

export interface HydraulicProcessFormData {
  name: string;
  price: string;                      // 表单中使用字符串，提交时转换为数字
  unit: EmbossingHydraulicProcessUnit;
  basePrice: string;
  salary: string;
  salaryBasePrice: string;
  remark?: string;
}

// ===========================================
// 加工费模块类型定义
// ===========================================

// 加工费单位枚举
export const PROCESSING_FEE_UNITS = [
  '元/个',
  '元/平方',
] as const;

export type ProcessingFeeUnit = typeof PROCESSING_FEE_UNITS[number];

// 加工费数据模型
export interface ProcessingFee {
  id: number;
  name: string;                     // 名称
  unitPrice: number;                // 单价
  unit: ProcessingFeeUnit;          // 单位
  basePrice: number;                // 起步价
  remark?: string;                  // 备注
  isDel: boolean;                   // 是否删除
  createdAt: string;                // 创建时间
  updatedAt: string;                // 更新时间
}

// 加工费固定参数数据模型
export interface ProcessingParams {
  id: number;
  pvcFilm: number;                 // PVC贴膜 (元/吨)
  slottingSalary: number;          // 开槽工资 (元/个)
  slottingBasePrice: number;       // 开槽起步价
  blisterPlate: number;            // 吸塑版 (元/平方)
  blisterBasePrice: number;        // 吸塑起步价
  highFrequencyPlate: number;      // 高频机版 (元/平方)
  highFrequencyBasePrice: number;  // 高频机起步价
  sprayCodeFee: number;           // 喷码费用 (元/个)
  sprayCodeBasePrice: number;     // 喷码起步价
  inspectionFee: number;          // 检验费用 (元/个)
  inspectionBasePrice: number;    // 检验起步价
  createdAt: string;              // 创建时间
  updatedAt: string;              // 更新时间
}

// 创建加工费的请求类型
export interface CreateProcessingFeeRequest {
  name: string;
  unitPrice: number;
  unit: ProcessingFeeUnit;
  basePrice: number;
  remark?: string;
}

// 更新加工费的请求类型
export interface UpdateProcessingFeeRequest extends CreateProcessingFeeRequest {
  id: number;
}

// 更新加工费固定参数的请求类型
export interface UpdateProcessingParamsRequest {
  pvcFilm: number;
  slottingSalary: number;
  slottingBasePrice: number;
  blisterPlate: number;
  blisterBasePrice: number;
  highFrequencyPlate: number;
  highFrequencyBasePrice: number;
  sprayCodeFee: number;
  sprayCodeBasePrice: number;
  inspectionFee: number;
  inspectionBasePrice: number;
}

// 加工费列表查询参数
export interface ProcessingFeeListParams {
  page?: number;
  pageSize?: number;
  search?: string;                  // 搜索名称或备注
  unit?: ProcessingFeeUnit;         // 按单位筛选
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 加工费表单数据类型
export interface ProcessingFeeFormData {
  name: string;
  unitPrice: string;                // 表单中使用字符串，提交时转换为数字
  unit: ProcessingFeeUnit;
  basePrice: string;
  remark?: string;
}

// 加工费固定参数表单数据类型
export interface ProcessingParamsFormData {
  pvcFilm: string;                 // 表单中使用字符串，提交时转换为数字
  slottingSalary: string;
  slottingBasePrice: string;
  blisterPlate: string;
  blisterBasePrice: string;
  highFrequencyPlate: string;
  highFrequencyBasePrice: string;
  sprayCodeFee: string;
  sprayCodeBasePrice: string;
  inspectionFee: string;
  inspectionBasePrice: string;
}

// API响应类型
export interface ProcessingFeeResponse {
  data: ProcessingFee[];
  total: number;
  page: number;
  pageSize: number;
}

export interface ProcessingFeeDetailResponse {
  data: ProcessingFee;
}

export interface ProcessingParamsResponse {
  data: ProcessingParams;
}

// ===========================================
// 模切工艺模块类型定义
// ===========================================

// 模切工艺单位枚举
export const DIE_CUTTING_PROCESS_UNITS = [
  '元/张',
  '元/平方',
] as const;

export type DieCuttingProcessUnit = typeof DIE_CUTTING_PROCESS_UNITS[number];

// 刀版费单位枚举
export const DIE_CUTTING_PLATE_FEE_UNITS = [
  '元/平方',
  '元/个'
] as const;

export type DieCuttingPlateFeeUnit = typeof DIE_CUTTING_PLATE_FEE_UNITS[number];

// 模切工艺数据模型
export interface DieCuttingProcess {
  id: number;
  name: string;                     // 名称
  price: number;                    // 价格
  unit: DieCuttingProcessUnit;      // 单位
  basePrice: number;                // 起步价
  remark?: string;                  // 备注
  isDel: boolean;                   // 是否删除
  createdAt: string;                // 创建时间
  updatedAt: string;                // 更新时间
}

// 刀版费数据模型
export interface DieCuttingPlateFee {
  id: number;
  name: string;                     // 名称
  price: number;                    // 价格
  unit: DieCuttingPlateFeeUnit;     // 单位
  basePrice: number;                // 起步金额
  impositionQuantity: number;       // 按拼版数量
  remark?: string;                  // 备注
  isDel: boolean;                   // 是否删除
  createdAt: string;                // 创建时间
  updatedAt: string;                // 更新时间
}

// 创建模切工艺的请求类型
export interface CreateDieCuttingProcessRequest {
  name: string;
  price: number;
  unit: DieCuttingProcessUnit;
  basePrice: number;
  remark?: string;
}

// 更新模切工艺的请求类型
export interface UpdateDieCuttingProcessRequest extends CreateDieCuttingProcessRequest {
  id: number;
}

// 创建刀版费的请求类型
export interface CreateDieCuttingPlateFeeRequest {
  name: string;
  price: number;
  unit: DieCuttingPlateFeeUnit;
  basePrice: number;
  impositionQuantity: number;
  remark?: string;
}

// 更新刀版费的请求类型
export interface UpdateDieCuttingPlateFeeRequest extends CreateDieCuttingPlateFeeRequest {
  id: number;
}

// 模切工艺列表查询参数
export interface DieCuttingProcessListParams {
  page?: number;
  pageSize?: number;
  search?: string;                    // 搜索名称或备注
  unit?: DieCuttingProcessUnit;       // 按单位筛选
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 刀版费列表查询参数
export interface DieCuttingPlateFeeListParams {
  page?: number;
  pageSize?: number;
  search?: string;                    // 搜索名称或备注
  unit?: DieCuttingPlateFeeUnit;      // 按单位筛选
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 模切工艺表单数据类型
export interface DieCuttingProcessFormData {
  name: string;
  price: string;                      // 表单中使用字符串，提交时转换为数字
  unit: DieCuttingProcessUnit;
  basePrice: string;
  remark?: string;
}

// 刀版费表单数据类型
export interface DieCuttingPlateFeeFormData {
  name: string;
  price: string;                      // 表单中使用字符串，提交时转换为数字
  unit: DieCuttingPlateFeeUnit;
  basePrice: string;
  impositionQuantity: string;
  remark?: string;
}