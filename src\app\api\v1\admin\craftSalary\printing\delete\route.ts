import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const deletePrintingSchema = z.object({
  id: z.coerce.number().positive('ID必须是正整数')
});

const handler = withValidation(
  deletePrintingSchema,
  async (request: AuthenticatedRequest, validatedQuery: any) => {
    const data = validatedQuery;

    // 检查印刷数据是否存在
    const existingPrinting = await prisma.printing.findFirst({
      where: {
        id: data.id,
        isDel: false,
      },
    });

    assertExists(existingPrinting, ErrorCode.RESOURCE_NOT_FOUND, '印刷数据不存在');

    // 软删除印刷数据
    const result = await prisma.printing.update({
      where: { id: data.id },
      data: {
        isDel: true,
      },
    });

    return successResponse(
      { id: result.id },
      '删除印刷配置成功'
    );
  }
); 
export const POST = withInternalAuth(handler);