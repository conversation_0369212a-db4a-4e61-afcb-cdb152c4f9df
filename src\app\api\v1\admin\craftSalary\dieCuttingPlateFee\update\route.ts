import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { UpdateDieCuttingPlateFeeData, updateDieCuttingPlateFeeSchema } from '@/lib/validations/admin/dieCuttingPlateFee';
import { withValidation, assert, assertExists } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation(
  updateDieCuttingPlateFeeSchema,
  async (request: NextRequest, validatedData: UpdateDieCuttingPlateFeeData) => {
    const data = validatedData;

    // 检查刀版费是否存在
    const existingPlateFee = await prisma.dieCuttingPlateFee.findFirst({
      where: {
        id: data.id,
        isDel: false,
      },
    });

    assertExists(existingPlateFee, ErrorCode.RESOURCE_NOT_FOUND, '刀版费不存在');

    // 检查名称是否与其他记录重复
    const duplicatePlateFee = await prisma.dieCuttingPlateFee.findFirst({
      where: {
        name: data.name,
        id: { not: data.id },
        isDel: false,
      },
    });

    assert(!duplicatePlateFee, ErrorCode.DUPLICATE_ENTRY, '刀版费名称已存在');

    // 更新刀版费数据
    const updatedPlateFee = await prisma.dieCuttingPlateFee.update({
      where: { id: data.id },
      data: {
        name: data.name,
        price: data.price,
        unit: data.unit,
        basePrice: data.basePrice,
        impositionQuantity: data.impositionQuantity,
        remark: data.remark,
      },
    });

    return successResponse(updatedPlateFee, '更新刀版费成功');
  }
);
