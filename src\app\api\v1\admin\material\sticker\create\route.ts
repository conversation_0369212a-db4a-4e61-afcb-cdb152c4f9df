import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createStickerSchema, CreateStickerParams } from '@/lib/validations/admin/sticker';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation<CreateStickerParams>(
  createStickerSchema,
  async (request: NextRequest, validatedData: CreateStickerParams) => {
    // 检查不干胶名称是否已存在
    const existingSticker = await prisma.sticker.findFirst({
      where: {
        name: validatedData.name,
        isDel: false,
      },
    });

    assert(!existingSticker, ErrorCode.MATERIAL_NAME_EXISTS, '不干胶名称已存在');

    // 创建不干胶
    const sticker = await prisma.sticker.create({
      data: {
        name: validatedData.name,
        price: validatedData.price,
        unit: validatedData.unit,
        weight: validatedData.weight,
        category: validatedData.category,
        remark: validatedData.remark || null,
        isDel: false,
      },
    });

    return successResponse(sticker, '创建不干胶成功');
  }
); 