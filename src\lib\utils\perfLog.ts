/**
 * 性能日志工具
 * 统一管理应用中的日志输出，支持环境变量控制
 */

// 性能优化配置
const PERFORMANCE_CONFIG = {
  enableDebugLogs: process.env.NODE_ENV === 'development', // 只在开发环境启用详细日志
};

/**
 * 性能日志工具
 * 提供统一的日志接口，支持环境变量控制
 */
export const perfLog = {
  /**
   * 调试日志 - 只在开发环境输出
   */
  debug: (...args: any[]) => {
    if (PERFORMANCE_CONFIG.enableDebugLogs) {
      console.log(...args);
    }
  },
  
  /**
   * 信息日志 - 所有环境都输出
   */
  info: (...args: any[]) => {
    console.log(...args);
  },
  
  /**
   * 警告日志 - 所有环境都输出
   */
  warn: (...args: any[]) => {
    console.warn(...args);
  },
  
  /**
   * 错误日志 - 所有环境都输出
   */
  error: (...args: any[]) => {
    console.error(...args);
  }
};

/**
 * API调用防重复工具
 * 用于防止同一个API在短时间内被重复调用
 */
class ApiCallTracker {
  private callHistory: Map<string, number> = new Map();
  private readonly CALL_INTERVAL = 1000; // 1秒内不允许重复调用

  /**
   * 检查是否可以调用API
   * @param apiKey API的唯一标识
   * @returns 是否可以调用
   */
  canCall(apiKey: string): boolean {
    const now = Date.now();
    const lastCall = this.callHistory.get(apiKey);

    if (!lastCall || (now - lastCall) > this.CALL_INTERVAL) {
      this.callHistory.set(apiKey, now);
      return true;
    }

    perfLog.debug(`API调用被阻止 - ${apiKey}，距离上次调用仅 ${now - lastCall}ms`);
    return false;
  }

  /**
   * 清除API调用记录
   * @param apiKey API的唯一标识
   */
  clearCall(apiKey: string): void {
    this.callHistory.delete(apiKey);
  }

  /**
   * 清除所有API调用记录
   */
  clearAll(): void {
    this.callHistory.clear();
  }
}

export const apiCallTracker = new ApiCallTracker();
