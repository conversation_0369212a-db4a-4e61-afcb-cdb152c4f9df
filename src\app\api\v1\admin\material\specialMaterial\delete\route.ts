import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { deleteSpecialMaterialSchema, DeleteSpecialMaterialParams } from '@/lib/validations/admin/specialMaterial';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation<DeleteSpecialMaterialParams>(
  deleteSpecialMaterialSchema,
  async (request: AuthenticatedRequest, validatedQuery: DeleteSpecialMaterialParams) => {
    const { id } = validatedQuery;

    // 检查特殊材料是否存在
    const existingMaterial = await prisma.specialMaterial.findUnique({
      where: {
        id,
        isDel: false,
      },
    });

    assertExists(existingMaterial, ErrorCode.MATERIAL_NOT_FOUND, '特殊材料不存在');

    // 软删除特殊材料
    const specialMaterial = await prisma.specialMaterial.update({
      where: {
        id,
      },
      data: {
        isDel: true,
        updatedAt: new Date(),
      },
    });

    return successResponse(specialMaterial, '删除特殊材料成功');
  }
); 
export const POST = withInternalAuth(handler);