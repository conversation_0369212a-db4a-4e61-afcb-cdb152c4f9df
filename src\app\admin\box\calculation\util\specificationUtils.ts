/**
 * 规格和单位转换工具函数
 * 提供统一的规格显示名称和单位转换功能
 */

/**
 * 规格代码到中文显示名称的映射
 */
const SPEC_DISPLAY_NAMES: Record<string, string> = {
  'regular': '正度',
  'large': '大度',
  'special': '特规',
  'stock': '现货尺寸',
  'custom': '自定义'
};

/**
 * 单位代码到中文显示名称的映射
 */
const CHINESE_UNIT_NAMES: Record<string, string> = {
  'm²': '平方米',
  '吨': '吨',
  '张': '张',
  '张(缺少克重)': '张(缺少克重)',
  '元/张': '元/张',
  '元/平方': '元/平方',
  '元/平方米': '元/平方米',
  '元/m²': '元/平方米',
  '元/吨': '元/吨',
  '元/个': '元/个',
  '元/米': '元/米',
  '元/条': '元/条',
  '元/对': '元/对',
  '元/立方': '元/立方',
  'mm': '毫米',
  'cm': '厘米',
  'kg': '千克',
  'g': '克'
};

/**
 * 材料类型到中文显示名称的映射
 */
const MATERIAL_TYPE_NAMES: Record<string, string> = {
  'paper': '纸张',
  'specialPaper': '特种纸',
  'greyBoard': '灰板',
  'corrugated': '瓦楞',
  'face': '面纸',
  'grey': '灰板纸'
};

/**
 * 获取规格的中文显示名称
 * @param spec 规格代码
 * @returns 中文显示名称
 */
export const getSpecDisplayName = (spec: string): string => {
  return SPEC_DISPLAY_NAMES[spec] || spec;
};

/**
 * 获取单位的中文显示名称
 * @param unit 单位代码
 * @returns 中文显示名称
 */
export const getChineseUnit = (unit: string): string => {
  return CHINESE_UNIT_NAMES[unit] || unit;
};

/**
 * 获取材料类型的中文显示名称
 * @param materialType 材料类型代码
 * @returns 中文显示名称
 */
export const getMaterialTypeDisplayName = (materialType: string): string => {
  return MATERIAL_TYPE_NAMES[materialType] || materialType;
};

/**
 * 检查规格代码是否有效
 * @param spec 规格代码
 * @returns 是否有效
 */
export const isValidSpec = (spec: string): boolean => {
  return spec in SPEC_DISPLAY_NAMES;
};

/**
 * 检查单位代码是否有效
 * @param unit 单位代码
 * @returns 是否有效
 */
export const isValidUnit = (unit: string): boolean => {
  return unit in CHINESE_UNIT_NAMES;
};

/**
 * 检查材料类型代码是否有效
 * @param materialType 材料类型代码
 * @returns 是否有效
 */
export const isValidMaterialType = (materialType: string): boolean => {
  return materialType in MATERIAL_TYPE_NAMES;
};

/**
 * 获取所有支持的规格代码
 * @returns 规格代码数组
 */
export const getSupportedSpecs = (): string[] => {
  return Object.keys(SPEC_DISPLAY_NAMES);
};

/**
 * 获取所有支持的单位代码
 * @returns 单位代码数组
 */
export const getSupportedUnits = (): string[] => {
  return Object.keys(CHINESE_UNIT_NAMES);
};

/**
 * 获取所有支持的材料类型代码
 * @returns 材料类型代码数组
 */
export const getSupportedMaterialTypes = (): string[] => {
  return Object.keys(MATERIAL_TYPE_NAMES);
};

/**
 * 批量转换规格显示名称
 * @param specs 规格代码数组
 * @returns 中文显示名称数组
 */
export const batchGetSpecDisplayNames = (specs: string[]): string[] => {
  return specs.map(spec => getSpecDisplayName(spec));
};

/**
 * 批量转换单位显示名称
 * @param units 单位代码数组
 * @returns 中文显示名称数组
 */
export const batchGetChineseUnits = (units: string[]): string[] => {
  return units.map(unit => getChineseUnit(unit));
};

/**
 * 批量转换材料类型显示名称
 * @param materialTypes 材料类型代码数组
 * @returns 中文显示名称数组
 */
export const batchGetMaterialTypeDisplayNames = (materialTypes: string[]): string[] => {
  return materialTypes.map(type => getMaterialTypeDisplayName(type));
};
