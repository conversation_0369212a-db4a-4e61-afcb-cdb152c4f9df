/**
 * 价格计算工具函数
 * 提供统一的价格计算逻辑和格式化功能
 */

/**
 * 基础价格计算结果接口
 */
export interface BasePriceResult {
  unitPrice: number;
  totalPrice: number;
  usedBasePrice: boolean;
}

/**
 * 阶梯价格配置接口
 */
export interface TieredPriceConfig {
  min: number;
  max: number;
  field: string;
}

/**
 * 阶梯价格计算结果接口
 */
export interface TieredPriceResult {
  unitPrice: number;
  priceSource: string;
}

/**
 * 复合价格计算结果接口
 */
export interface CompositePriceResult {
  materialCost: number;
  salaryCost: number;
  totalPrice: number;
}

/**
 * 面积计算参数接口
 */
export interface AreaDimensions {
  length: number;
  width: number;
}

/**
 * 通用基础价格计算（应用起步价逻辑）
 * @param unitPrice 单价
 * @param quantity 数量
 * @param basePrice 起步价（可选）
 * @returns 价格计算结果
 */
export const calculateBasePriceWithMinimum = (
  unitPrice: number,
  quantity: number,
  basePrice: number = 0
): BasePriceResult => {
  const calculatedTotalPrice = unitPrice * quantity;
  const finalTotalPrice = basePrice > 0 && calculatedTotalPrice < basePrice ? basePrice : calculatedTotalPrice;
  const finalUnitPrice = finalTotalPrice === basePrice && quantity > 0 ? basePrice / quantity : unitPrice;

  return {
    unitPrice: finalUnitPrice,
    totalPrice: finalTotalPrice,
    usedBasePrice: finalTotalPrice === basePrice
  };
};

/**
 * 阶梯价格计算（用于印刷和压纹工艺）
 * @param data 价格数据对象
 * @param quantity 数量
 * @param priceFields 价格字段配置
 * @returns 阶梯价格计算结果
 */
export const calculateTieredPrice = (
  data: Record<string, any>,
  quantity: number,
  priceFields: Record<string, TieredPriceConfig>
): TieredPriceResult => {
  let unitPrice = 0;
  let priceSource = 'basePrice';

  for (const [key, config] of Object.entries(priceFields)) {
    if (quantity >= config.min && quantity <= config.max && data[config.field] > 0) {
      unitPrice = Number(data[config.field]) || 0;
      priceSource = config.field;
      break;
    }
  }

  // 如果没有匹配的阶梯价格，使用最高阶梯或默认价格
  if (unitPrice === 0) {
    const highestTierField = Object.values(priceFields).pop()?.field;
    if (highestTierField && data[highestTierField]) {
      unitPrice = Number(data[highestTierField]) || 0;
      priceSource = highestTierField;
    }
  }

  return { unitPrice, priceSource };
};

/**
 * 面积基础价格计算
 * @param price 单价
 * @param dimensions 尺寸参数
 * @returns 计算结果（平方米）
 */
export const calculateAreaBasedPrice = (
  price: number,
  dimensions: AreaDimensions
): number => {
  return (price * dimensions.length * dimensions.width) / 1000000; // 转换为平方米
};

/**
 * 复合价格计算（材料费+工资费，用于凹凸和液压工艺）
 * @param processData 工艺数据
 * @param materialQuantity 材料数量
 * @param salaryQuantity 工资数量
 * @returns 复合价格计算结果
 */
export const calculateCompositePrice = (
  processData: Record<string, any>,
  materialQuantity: number,
  salaryQuantity: number
): CompositePriceResult => {
  // 材料费计算
  const materialPrice = Number(processData.price) || 0;
  const materialBasePrice = Number(processData.basePrice) || 0;
  const materialCost = materialPrice * materialQuantity;
  const finalMaterialCost = materialBasePrice > 0 && materialCost < materialBasePrice ? materialBasePrice : materialCost;

  // 工资费计算
  const salaryPrice = Number(processData.salary) || 0;
  const salaryBasePrice = Number(processData.salaryBasePrice) || 0;
  const salaryCost = salaryPrice * salaryQuantity;
  const finalSalaryCost = salaryBasePrice > 0 && salaryCost < salaryBasePrice ? salaryBasePrice : salaryCost;

  return {
    materialCost: finalMaterialCost,
    salaryCost: finalSalaryCost,
    totalPrice: finalMaterialCost + finalSalaryCost
  };
};

/**
 * 格式化货币显示
 * @param amount 金额
 * @param currency 货币符号（默认为¥）
 * @param decimals 小数位数（默认为2）
 * @returns 格式化后的货币字符串
 */
export const formatCurrency = (
  amount: number,
  currency: string = '¥',
  decimals: number = 2
): string => {
  return `${currency}${amount.toLocaleString('zh-CN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  })}`;
};

/**
 * 计算材料重量（用于按吨计价）
 * @param areaM2 面积（平方米）
 * @param weight 克重（g/m²）
 * @returns 重量（吨）
 */
export const calculateMaterialWeight = (areaM2: number, weight: number): number => {
  const totalWeightGrams = areaM2 * weight; // 总重量(克)
  return totalWeightGrams / 1000000; // 转换克到吨
};

/**
 * 根据单位类型计算实际数量
 * @param unit 单位
 * @param baseQuantity 基础数量
 * @param dimensions 尺寸（用于面积计算）
 * @param weight 克重（用于重量计算）
 * @returns 实际计算数量
 */
export const calculateQuantityByUnit = (
  unit: string,
  baseQuantity: number,
  dimensions?: AreaDimensions,
  weight?: number
): number => {
  switch (unit) {
    case '元/张':
      return baseQuantity;
    
    case '元/平方':
    case '元/平方米':
    case '元/m²':
      if (!dimensions) return baseQuantity;
      return calculateAreaBasedPrice(1, dimensions);
    
    case '元/吨':
      if (!dimensions || !weight) return baseQuantity;
      const areaM2 = calculateAreaBasedPrice(1, dimensions);
      return calculateMaterialWeight(areaM2, weight);
    
    default:
      return baseQuantity;
  }
};

/**
 * 验证价格计算参数
 * @param unitPrice 单价
 * @param quantity 数量
 * @returns 是否有效
 */
export const validatePriceParams = (unitPrice: number, quantity: number): boolean => {
  return !isNaN(unitPrice) && !isNaN(quantity) && unitPrice >= 0 && quantity >= 0;
};

/**
 * 安全的数字转换
 * @param value 要转换的值
 * @param defaultValue 默认值
 * @returns 转换后的数字
 */
export const safeNumber = (value: any, defaultValue: number = 0): number => {
  const num = Number(value);
  return isNaN(num) ? defaultValue : num;
};
