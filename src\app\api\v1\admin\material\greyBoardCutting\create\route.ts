import { NextRequest } from 'next/server';
import { createGreyBoardCuttingSchema, CreateGreyBoardCuttingParams } from '@/lib/validations/admin/greyBoardCutting';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';
import { prisma } from '@/lib/prisma';

export const POST = withValidation<CreateGreyBoardCuttingParams>(
  createGreyBoardCuttingSchema,
  async (request: NextRequest, validatedData: CreateGreyBoardCuttingParams) => {
    // 检查灰板分切尺寸名称是否重复
    const existingGreyBoardCutting = await prisma.greyBoardCutting.findFirst({
      where: {
        name: validatedData.name,
        isDel: false,
      },
    });

    assert(!existingGreyBoardCutting, ErrorCode.MATERIAL_NAME_EXISTS, '灰板分切尺寸名称已存在');

    // 创建灰板分切尺寸
    const greyBoardCutting = await prisma.greyBoardCutting.create({
      data: {
        name: validatedData.name,
        initialCutPrice: validatedData.initialCutPrice,
        sizes: validatedData.sizes,
        isDel: false,
      },
      select: {
        id: true,
        name: true,
        initialCutPrice: true,
        sizes: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return successResponse(greyBoardCutting, '创建灰板分切尺寸成功');
  }
); 