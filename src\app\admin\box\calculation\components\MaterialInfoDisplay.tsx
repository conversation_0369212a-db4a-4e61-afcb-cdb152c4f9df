'use client';

import React from 'react';
import { Space, Typography } from 'antd';
import { PartMaterialConfig } from '../types/calculation';
import { getSpecDisplayName } from '../util';

const { Text } = Typography;

interface MaterialInfoDisplayProps {
  partGroupId: string;
  materialType: 'face' | 'grey';
  partMaterialConfigs?: Record<string, PartMaterialConfig>;
  sheetsNeeded?: number; // 材料张数，来源于拼版结果
}

const MaterialInfoDisplay: React.FC<MaterialInfoDisplayProps> = ({
  partGroupId,
  materialType,
  partMaterialConfigs,
  sheetsNeeded
}) => {
  // 获取该部件分组的材料配置
  const materialConfig = partMaterialConfigs?.[partGroupId];

  if (!materialConfig) {
    return (
      <Text type="secondary" style={{ fontSize: '12px' }}>
        未配置材料
      </Text>
    );
  }

  // 构建材料信息
  const materialInfo = {
    name: materialConfig.materialName || '未选择',
    spec: getSpecDisplayName(materialConfig.materialSpec || '未指定'),
    size: materialConfig.materialSize
      ? `${materialConfig.materialSize.width}×${materialConfig.materialSize.height}mm`
      : '未指定',
    printingMachine: materialConfig.printingMachineName || '未选择'
  };

  return (
    <div style={{ marginBottom: 12, padding: 8, backgroundColor: '#fafafa', borderRadius: 4 }}>
      <Space direction="vertical" size={2} style={{ width: '100%' }}>
        <Space>
          <Text strong>{materialInfo.name}</Text>
        </Space>
        <Space>
          <Text type="secondary">规格: {materialInfo.spec}</Text>
          <Text type="secondary">尺寸: {materialInfo.size}</Text>
          {sheetsNeeded !== undefined && (
            <Text type="secondary">数量: {sheetsNeeded}张</Text>
          )}
          {materialType === 'face' && materialConfig.printingMachineName && (
            <Text type="secondary"> 印刷机: {materialInfo.printingMachine} </Text>
          )}
        </Space>

      </Space>
    </div>
  );
};

export default MaterialInfoDisplay;
