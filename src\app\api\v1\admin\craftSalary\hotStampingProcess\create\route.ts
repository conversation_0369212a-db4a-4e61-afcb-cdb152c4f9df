import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createHotStampingProcessSchema } from '@/lib/validations/admin/hotStampingProcess';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation(
  createHotStampingProcessSchema,
  async (request: NextRequest, validatedData: any) => {
    const data = validatedData;

    // 检查名称是否重复
    const existingHotStampingProcess = await prisma.hotStampingProcess.findFirst({
      where: {
        name: data.name,
        isDel: false,
      },
    });

    assert(!existingHotStampingProcess, ErrorCode.DUPLICATE_ENTRY, '烫金工艺名称已存在');

    // 创建烫金工艺
    const hotStampingProcess = await prisma.hotStampingProcess.create({
      data: {
        name: data.name,
        salary: data.salary,
        salaryUnit: data.salaryUnit,
        materialPrice: data.materialPrice,
        materialUnit: data.materialUnit,
        basePrice: data.basePrice,
        remark: data.remark || null,
        isDel: false,
      },
    });

    return successResponse(
      hotStampingProcess,
      '创建烫金工艺成功'
    );
  }
); 