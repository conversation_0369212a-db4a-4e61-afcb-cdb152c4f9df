version: '3.8'

services:
  mysql:
    image: mysql:5.7
    container_name: ycbz_mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root123456
      MYSQL_DATABASE: ycbz
      MYSQL_USER: ycbz
      MYSQL_PASSWORD: ycbz123456
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - ./mysql/data:/var/lib/mysql
      - ./mysql/conf:/etc/mysql/conf.d
      - ./mysql/logs:/var/log/mysql
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    networks:
      - ycbz_network

networks:
  ycbz_network:
    driver: bridge
