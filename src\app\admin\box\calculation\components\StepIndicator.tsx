'use client';

import React from 'react';
import { Steps } from 'antd';
import { CheckOutlined } from '@ant-design/icons';
import { CalculationStep } from '../types/calculation';
import { STEP_CONFIG } from '../hooks/useStepNavigation';

interface StepIndicatorProps {
  steps: CalculationStep[];
  currentStep: CalculationStep;
  onStepClick: (step: CalculationStep) => void;
  getStepValidation: (step: CalculationStep) => boolean;
}

/**
 * 步骤指示器组件
 */
const StepIndicator: React.FC<StepIndicatorProps> = ({
  steps,
  currentStep,
  onStepClick,
  getStepValidation
}) => {
  const currentStepIndex = steps.indexOf(currentStep);

  const stepItems = steps.map((step, index) => {
    const config = STEP_CONFIG[step];
    const isCompleted = index < currentStepIndex && getStepValidation(step);
    const isCurrent = step === currentStep;

    return {
      title: config.title,
      description: config.description,
      status: (isCompleted ? 'finish' : (isCurrent ? 'process' : 'wait')) as 'finish' | 'process' | 'wait' | 'error',
      icon: isCompleted ? <CheckOutlined /> : undefined
    };
  });

  return (
    <Steps
      current={currentStepIndex}
      items={stepItems}
      size="small"
      className="cursor-pointer"
      onChange={(current) => {
        if (current <= currentStepIndex) {
          onStepClick(steps[current]);
        }
      }}
    />
  );
};

export default StepIndicator; 