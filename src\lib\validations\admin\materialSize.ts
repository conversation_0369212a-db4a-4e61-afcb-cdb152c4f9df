import { z } from 'zod';

// 材料尺寸配置验证规则
export const materialSizeConfigSchema = z.object({
  regularLength: z.number()
    .min(100, '正度长度不能小于100mm')
    .max(2000, '正度长度不能大于2000mm'),
  regularWidth: z.number()
    .min(100, '正度宽度不能小于100mm')
    .max(2000, '正度宽度不能大于2000mm'),
  largeLength: z.number()
    .min(100, '大度长度不能小于100mm')
    .max(2000, '大度长度不能大于2000mm'),
  largeWidth: z.number()
    .min(100, '大度宽度不能小于100mm')
    .max(2000, '大度宽度不能大于2000mm'),
  specialLength: z.number()
    .min(100, '特规长度不能小于100mm')
    .max(2000, '特规长度不能大于2000mm'),
  specialWidth: z.number()
    .min(100, '特规宽度不能小于100mm')
    .max(2000, '特规宽度不能大于2000mm'),
});

// 材料尺寸更新参数验证
export const materialSizeUpdateSchema = z.object({
  config: materialSizeConfigSchema,
});

// 导出类型
export type MaterialSizeConfigParams = z.infer<typeof materialSizeConfigSchema>;
export type MaterialSizeUpdateParams = z.infer<typeof materialSizeUpdateSchema>; 