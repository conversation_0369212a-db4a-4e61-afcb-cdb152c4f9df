import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createTexturingProcessSchema } from '@/lib/validations/admin/embossingProcess';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation(
  createTexturingProcessSchema,
  async (request: AuthenticatedRequest, validatedData: any) => {
    const data = validatedData;

    // 检查名称是否重复
    const existingTexturingProcess = await prisma.texturingProcess.findFirst({
      where: {
        name: data.name,
        isDel: false,
      },
    });

    assert(!existingTexturingProcess, ErrorCode.DUPLICATE_ENTRY, '压纹工艺名称已存在');

    // 创建压纹工艺
    const texturingProcess = await prisma.texturingProcess.create({
      data: {
        name: data.name,
        textureVersion: data.textureVersion,
        unit: data.unit,
        priceBelow1000: data.priceBelow1000,
        price1000_1999: data.price1000_1999,
        price2000_3999: data.price2000_3999,
        price4000Plus: data.price4000Plus,
        remark: data.remark || null,
        isDel: false,
      },
    });

    return successResponse(
      texturingProcess,
      '创建压纹工艺成功'
    );
  }
); 
export const POST = withInternalAuth(handler);