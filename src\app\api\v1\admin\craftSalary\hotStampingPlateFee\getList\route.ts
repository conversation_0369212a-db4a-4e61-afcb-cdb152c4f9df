import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { hotStampingPlateFeeListParamsSchema } from '@/lib/validations/admin/hotStampingProcess';
import { withValidation } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { paginatedResponse } from '@/lib/utils/apiResponse';
import { Prisma } from '@prisma/client';

const handler = withValidation(
  hotStampingPlateFeeListParamsSchema,
  async (request: AuthenticatedRequest, validatedQuery: any) => {
    const data = validatedQuery;
    const page = data.page ?? 1;
    const pageSize = data.pageSize ?? 10;
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const where: Prisma.HotStampingPlateFeeWhereInput = {
      isDel: false,
    };

    if (data.search) {
      where.OR = [
        { name: { contains: data.search } },
        { remark: { contains: data.search } }
      ];
    }

    // 查询数据和总数
    const [hotStampingPlateFees, total] = await Promise.all([
      prisma.hotStampingPlateFee.findMany({
        where,
        skip,
        take: pageSize,
        orderBy: { createdAt: 'desc' },
      }),
      prisma.hotStampingPlateFee.count({ where }),
    ]);

    return paginatedResponse(
      hotStampingPlateFees,
      {
        total,
        page,
        pageSize,
      },
      '查询烫金版费列表成功'
    );
  }
); 
export const POST = withInternalAuth(handler);