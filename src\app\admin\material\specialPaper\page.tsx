'use client';

import React, { useState, useEffect } from 'react';
import {
  Table, Button, Space, Modal, Form, Input,
  Select, InputNumber, Popconfirm, Card, Typography,
  message, Row, Col, Switch, Tag, Tabs
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined,
  SearchOutlined, ReloadOutlined
} from '@ant-design/icons';
import { specialPaperApi } from '@/services/adminApi';
import { SpecialPaper } from '@/types/material';
import { createSpecialPaperSchema, updateSpecialPaperSchema, validateSpecialSize } from '@/lib/validations/admin/specialPaper';
import { useAsyncError } from '@/lib/hooks/useErrorHandler';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;

// 特种纸数据库管理页面
export default function SpecialPaperManagementPage() {
  // 错误处理Hook
  const { execute: executeSpecialPaper, loading: specialPaperLoading } = useAsyncError<any>();

  // 特种纸数据相关状态
  const [paperList, setPaperList] = useState<SpecialPaper[]>([]);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [keyword, setKeyword] = useState('');
  const [category, setCategory] = useState('');
  const [categoryList, setCategoryList] = useState<string[]>([]);

  // 模态框相关状态
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [form] = Form.useForm();

  // 初始加载数据
  useEffect(() => {
    fetchList();
    fetchCategoryList();
  }, []);

  // 获取特种纸列表
  const fetchList = async (
    page = current,
    ps = pageSize,
    kw = keyword,
    cat = category
  ) => {
    const result = await executeSpecialPaper(async () => {
      return await specialPaperApi.getList({
        page,
        pageSize: ps,
        keyword: kw,
        category: cat
      });
    }, '获取特种纸列表');

    if (result) {
      setPaperList(result.list);
      setTotal(result.pagination.total);
    } else {
      setPaperList([]);
      setTotal(0);
    }
  };

  // 获取材料品类列表
  const fetchCategoryList = async () => {
    const result = await executeSpecialPaper(async () => {
      return await specialPaperApi.getCategoryList();
    }, '获取材料品类列表');

    if (result) {
      setCategoryList(result);
    } else {
      setCategoryList([]);
    }
  };

  // 处理分页变化
  const handleTableChange = (pagination: any) => {
    setCurrent(pagination.current);
    setPageSize(pagination.pageSize);
    fetchList(pagination.current, pagination.pageSize);
  };

  // 打开添加特种纸模态框
  const showAddModal = () => {
    setModalTitle('添加特种纸');
    setEditingRecord(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 打开编辑特种纸模态框
  const showEditModal = (record: SpecialPaper) => {
    setModalTitle('编辑特种纸');
    setEditingRecord(record);
    form.setFieldsValue({
      name: record.name,
      price: record.price,
      unit: record.unit,
      weight: record.weight,
      thickness: record.thickness,
      isRegular: record.isRegular,
      isLarge: record.isLarge,
      isSpecial: record.isSpecial,
      size1: record.size1,
      size2: record.size2,
      category: record.category,
      remark: record.remark
    });
    setModalVisible(true);
  };

  // 处理表单提交
  const handleFormSubmit = async () => {
    try {
      const values = await form.validateFields();

      // 处理品类数据类型问题 - mode="tags"会返回数组，需要转换为字符串
      if (Array.isArray(values.category) && values.category.length > 0) {
        values.category = values.category[0];
      }

      // 使用通用校验组件进行验证
      const schema = editingRecord ? updateSpecialPaperSchema : createSpecialPaperSchema;
      const validationResult = schema.safeParse({
        ...values,
        ...(editingRecord ? { id: editingRecord.id } : {})
      });

      if (!validationResult.success) {
        const errors = validationResult.error.format();
        const firstError = Object.values(errors)[0];
        if (firstError && typeof firstError === 'object' && '_errors' in firstError) {
          message.error(firstError._errors[0] || '表单验证失败');
        } else {
          message.error('表单验证失败');
        }
        return;
      }

      // 特规尺寸条件验证
      const specialSizeValidation = validateSpecialSize(values);
      if (!specialSizeValidation.success) {
        message.error(specialSizeValidation.error);
        return;
      }

      let result;
      if (editingRecord) {
        // 更新特种纸
        result = await executeSpecialPaper(async () => {
          return await specialPaperApi.update({
            id: editingRecord.id,
            ...values
          });
        }, '更新特种纸');
      } else {
        // 创建特种纸
        result = await executeSpecialPaper(async () => {
          return await specialPaperApi.create(values);
        }, '创建特种纸');
      }

      if (result) {
        message.success(editingRecord ? '特种纸更新成功' : '特种纸添加成功');
        setModalVisible(false);
        fetchList();
        fetchCategoryList(); // 重新获取品类列表，确保新添加的品类能被列出
      }
    } catch (error) {
      console.error('表单提交失败:', error);
      message.error('表单验证失败');
    }
  };

  // 删除特种纸
  const handleDelete = async (id: number) => {
    const result = await executeSpecialPaper(async () => {
      return await specialPaperApi.delete(id);
    }, '删除特种纸');

    if (result) {
      message.success('特种纸删除成功');
      fetchList();
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center' as const,
      width: 150,
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      align: 'center' as const,
      width: 100,
      render: (text: number) => `${text} 元`,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      align: 'center' as const,
      width: 100,
    },
    {
      title: '克重',
      dataIndex: 'weight',
      key: 'weight',
      align: 'center' as const,
      width: 80,
    },
    {
      title: '厚度',
      dataIndex: 'thickness',
      key: 'thickness',
      align: 'center' as const,
      width: 80,
    },
    {
      title: '正度',
      dataIndex: 'isRegular',
      key: 'isRegular',
      align: 'center' as const,
      width: 80,
      render: (isRegular: boolean) => (
        isRegular ?
          <Tag color="blue">是</Tag> :
          <Tag color="default">否</Tag>
      ),
    },
    {
      title: '大度',
      dataIndex: 'isLarge',
      key: 'isLarge',
      align: 'center' as const,
      width: 80,
      render: (isLarge: boolean) => (
        isLarge ?
          <Tag color="green">是</Tag> :
          <Tag color="default">否</Tag>
      ),
    },
    {
      title: '特规',
      dataIndex: 'isSpecial',
      key: 'isSpecial',
      align: 'center' as const,
      width: 80,
      render: (isSpecial: boolean) => (
        isSpecial ?
          <Tag color="purple">是</Tag> :
          <Tag color="default">否</Tag>
      ),
    },
    {
      title: '特规尺寸',
      key: 'specialSize',
      align: 'center' as const,
      width: 120,
      render: (_: any, record: SpecialPaper) => {
        if (record.isSpecial && record.size1 && record.size2) {
          return <Tag color="blue">{record.size1} × {record.size2}</Tag>;
        }
        return '-';
      }
    },
    {
      title: '材料品类',
      dataIndex: 'category',
      key: 'category',
      align: 'center' as const,
      width: 120,
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      align: 'center' as const,
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'action',
      align: 'center' as const,
      fixed: 'right' as const,
      width: 150,
      render: (_: any, record: SpecialPaper) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => showEditModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此特种纸吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="是"
            cancelText="否"
          >
            <Button
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>特种纸数据库</Title>

      <Tabs
        defaultActiveKey="1"
        items={[
          {
            key: '1',
            label: '特种纸数据库',
            children: (
              <Card>
                <div style={{ marginBottom: 16 }}>
                  <Row gutter={16} align="middle">
                    <Col span={4}>
                      <Input
                        placeholder="搜索名称"
                        prefix={<SearchOutlined />}
                        allowClear
                        value={keyword}
                        onChange={(e) => setKeyword(e.target.value)}
                        onPressEnter={() => fetchList(1, pageSize, keyword, category)}
                      />
                    </Col>
                    <Col span={4}>
                      <Select
                        placeholder="搜索材料品类"
                        allowClear
                        showSearch
                        loading={specialPaperLoading}
                        style={{ width: '100%' }}
                        value={category || undefined}
                        onChange={(value) => setCategory(value || '')}
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          (option?.children as unknown as string).toLowerCase().includes(input.toLowerCase())
                        }
                      >
                        {categoryList.map((cat) => (
                          <Option key={cat} value={cat}>
                            {cat}
                          </Option>
                        ))}
                      </Select>
                    </Col>
                    <Col>
                      <Button
                        type="primary"
                        icon={<SearchOutlined />}
                        onClick={() => fetchList(1, pageSize, keyword, category)}
                      >
                        搜索
                      </Button>
                    </Col>
                    <Col>
                      <Button
                        icon={<ReloadOutlined />}
                        onClick={() => {
                          setKeyword('');
                          setCategory('');
                          fetchList(1, pageSize, '', '');
                        }}
                      >
                        重置
                      </Button>
                    </Col>
                    <Col flex="auto" style={{ textAlign: 'right' }}>
                      <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={showAddModal}
                      >
                        添加特种纸
                      </Button>
                    </Col>
                  </Row>
                </div>

                <Table
                  columns={columns}
                  dataSource={paperList}
                  rowKey="id"
                  pagination={{
                    current,
                    pageSize,
                    total,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                  }}
                  loading={specialPaperLoading}
                  onChange={handleTableChange}
                  bordered
                  scroll={{ x: 1200 }}
                  locale={{ emptyText: '暂无数据' }}
                  size="middle"
                />
              </Card>
            )
          }
        ]}
      />

      {/* 表单模态框 */}
      <Modal
        title={modalTitle}
        open={modalVisible}
        onOk={handleFormSubmit}
        onCancel={() => setModalVisible(false)}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="name"
                label="特种纸名称"
                rules={[{ required: true, message: '请输入特种纸名称' }]}
              >
                <Input placeholder="请输入特种纸名称" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="category"
                label="材料品类"
                rules={[{ required: true, message: '请选择或输入材料品类' }]}
              >
                <Select
                  placeholder="请选择或输入材料品类"
                  showSearch
                  allowClear
                  loading={specialPaperLoading}
                  mode="tags"
                  onChange={(value) => {
                    // 如果是数组并且有多个值，只保留最后一个
                    if (Array.isArray(value) && value.length > 1) {
                      const lastValue = value[value.length - 1];
                      form.setFieldValue('category', [lastValue]);
                    }
                  }}
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                  }
                  dropdownRender={(menu) => (
                    <>
                      {menu}
                      {categoryList.length === 0 && !specialPaperLoading && (
                        <div style={{ padding: '8px', textAlign: 'center' }}>
                          暂无数据，请输入新品类
                        </div>
                      )}
                    </>
                  )}
                >
                  {categoryList.map((cat) => (
                    <Option key={cat} value={cat}>
                      {cat}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="price"
                label="价格"
                rules={[{ required: true, message: '请输入价格' }]}
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="请输入价格"
                  addonAfter="元"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="unit"
                label="单位"
                rules={[{ required: true, message: '请选择单位' }]}
              >
                <Select placeholder="请选择单位">
                  <Option value="元/吨">元/吨</Option>
                  <Option value="元/张">元/张</Option>
                  <Option value="元/平方">元/平方</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="weight"
                label="克重"
                rules={[{ required: true, message: '请输入克重' }]}
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                  placeholder="请输入克重"
                  addonAfter="g/m²"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="thickness"
                label="厚度"
                rules={[{ required: true, message: '请输入厚度' }]}
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                  placeholder="请输入厚度"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={3}>
              <Form.Item
                name="isRegular"
                label="是否正度"
                valuePropName="checked"
                initialValue={false}
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item
                name="isLarge"
                label="是否大度"
                valuePropName="checked"
                initialValue={false}
              >
                <Switch />
              </Form.Item>
            </Col>

            <Col span={3}>
              <Form.Item
                name="isSpecial"
                label="是否特规"
                valuePropName="checked"
                initialValue={false}
              >
                <Switch />
              </Form.Item>
            </Col>

            <Col span={15}>
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) => prevValues.isSpecial !== currentValues.isSpecial}
              >
                {({ getFieldValue }) =>
                  getFieldValue('isSpecial') ? (
                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="size1"
                          label="特规尺寸1"
                          rules={[{ required: true, message: '请输入特规尺寸1' }]}
                        >
                          <InputNumber
                            min={0}
                            style={{ width: '100%' }}
                            placeholder="请输入特规尺寸1"
                          />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="size2"
                          label="特规尺寸2"
                          rules={[{ required: true, message: '请输入特规尺寸2' }]}
                        >
                          <InputNumber
                            min={0}
                            style={{ width: '100%' }}
                            placeholder="请输入特规尺寸2"
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  ) : null
                }
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="remark"
            label="备注"
          >
            <Input.TextArea rows={3} placeholder="请输入备注信息（可选）" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
} 