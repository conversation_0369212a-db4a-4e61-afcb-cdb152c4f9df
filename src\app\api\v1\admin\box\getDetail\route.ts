import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getBoxDetailSchema, GetBoxDetailParams } from '@/lib/validations/admin/box';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation<GetBoxDetailParams>(
  getBoxDetailSchema,
  async (request: AuthenticatedRequest, validatedQuery: GetBoxDetailParams) => {
    const data = validatedQuery;

    // 查询盒型详细信息
    const box = await prisma.box.findFirst({
      where: {
        id: data.id,
        isDel: false,
      },
      include: {
        attributes: {
          select: {
            id: true,
            name: true,
            code: true,
            value: true,
            sortOrder: true,
          },
          orderBy: {
            sortOrder: 'asc',
          },
        },
        parts: {
          select: {
            id: true,
            name: true,
            formulas: {
              select: {
                id: true,
                name: true,
                expression: true,
              },
            },
          },
        },
        packaging: {
          select: {
            id: true,
            lengthFormula: true,
            widthFormula: true,
            heightFormula: true,
          },
        },
        images: {
          select: {
            id: true,
            name: true,
            mimeType: true,
            sortOrder: true,
          },
          orderBy: {
            sortOrder: 'asc',
          },
        },
      },
    });

    assertExists(box, ErrorCode.BOX_NOT_FOUND, '盒型不存在');

    return successResponse(box, '获取盒型详情成功');
  }
);

export const POST = withInternalAuth(handler);