export default function FrontLayout({ children, }: { children: React.ReactNode; }) {
  // 结构化数据
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "艺创包装",
    "description": "专业纸箱包装定制服务提供商",
    "url": "https://www.ycbz.com",
    "logo": "https://www.ycbz.com/images/logo.png",
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+86-18638728164",
      "contactType": "customer service",
      "availableLanguage": "Chinese"
    },
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "郑州市",
      "addressRegion": "河南省",
      "addressCountry": "CN"
    },
    "sameAs": [
      "https://www.ycbz.com"
    ]
  };

  return (
    <>
      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />

      {/* DNS预解析 */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//www.google-analytics.com" />

      {/* 预加载关键资源 */}
      <link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />

      <div className="front-layout">
        {children}
      </div>
    </>
  );
}
