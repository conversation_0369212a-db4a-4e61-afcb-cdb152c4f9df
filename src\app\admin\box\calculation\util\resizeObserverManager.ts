/**
 * ResizeObserver 管理器
 * 解决多个组件同时使用 ResizeObserver 导致的警告问题
 */

interface ObserverCallback {
  id: string;
  element: Element;
  callback: (entry: ResizeObserverEntry) => void;
}

class ResizeObserverManager {
  private observer: ResizeObserver | null = null;
  private callbacks: Map<Element, ObserverCallback[]> = new Map();
  private isInitialized = false;

  /**
   * 初始化 ResizeObserver
   */
  private initialize() {
    if (this.isInitialized || typeof window === 'undefined') {
      return;
    }

    this.observer = new ResizeObserver((entries) => {
      // 使用 requestAnimationFrame 来避免频繁触发
      requestAnimationFrame(() => {
        entries.forEach((entry) => {
          const callbacks = this.callbacks.get(entry.target);
          if (callbacks) {
            callbacks.forEach((callbackInfo) => {
              try {
                callbackInfo.callback(entry);
              } catch (error) {
                console.warn('ResizeObserver callback error:', error);
              }
            });
          }
        });
      });
    });

    this.isInitialized = true;
  }

  /**
   * 观察元素
   * @param element 要观察的元素
   * @param callback 回调函数
   * @param id 唯一标识符
   * @returns 取消观察的函数
   */
  observe(
    element: Element,
    callback: (entry: ResizeObserverEntry) => void,
    id: string = Math.random().toString(36).substr(2, 9)
  ): () => void {
    this.initialize();

    if (!this.observer) {
      return () => {};
    }

    // 获取或创建该元素的回调数组
    let elementCallbacks = this.callbacks.get(element);
    if (!elementCallbacks) {
      elementCallbacks = [];
      this.callbacks.set(element, elementCallbacks);
      // 开始观察该元素
      this.observer.observe(element);
    }

    // 检查是否已存在相同 id 的回调
    const existingIndex = elementCallbacks.findIndex(cb => cb.id === id);
    if (existingIndex !== -1) {
      // 更新现有回调
      elementCallbacks[existingIndex].callback = callback;
    } else {
      // 添加新回调
      elementCallbacks.push({ id, element, callback });
    }

    // 返回取消观察的函数
    return () => this.unobserve(element, id);
  }

  /**
   * 取消观察元素
   * @param element 要取消观察的元素
   * @param id 回调的唯一标识符
   */
  unobserve(element: Element, id?: string) {
    const elementCallbacks = this.callbacks.get(element);
    if (!elementCallbacks) {
      return;
    }

    if (id) {
      // 移除特定的回调
      const index = elementCallbacks.findIndex(cb => cb.id === id);
      if (index !== -1) {
        elementCallbacks.splice(index, 1);
      }
    } else {
      // 移除所有回调
      elementCallbacks.length = 0;
    }

    // 如果该元素没有回调了，停止观察
    if (elementCallbacks.length === 0) {
      this.callbacks.delete(element);
      if (this.observer) {
        this.observer.unobserve(element);
      }
    }
  }

  /**
   * 断开所有观察
   */
  disconnect() {
    if (this.observer) {
      this.observer.disconnect();
      this.callbacks.clear();
    }
  }

  /**
   * 获取当前观察的元素数量
   */
  getObservedElementsCount(): number {
    return this.callbacks.size;
  }

  /**
   * 获取当前回调总数
   */
  getTotalCallbacksCount(): number {
    let total = 0;
    this.callbacks.forEach(callbacks => {
      total += callbacks.length;
    });
    return total;
  }
}

// 创建全局单例
const resizeObserverManager = new ResizeObserverManager();

export default resizeObserverManager;

/**
 * React Hook 用于使用 ResizeObserver
 * @param callback 回调函数
 * @param deps 依赖数组
 * @returns ref 对象
 */
export function useResizeObserver<T extends Element>(
  callback: (entry: ResizeObserverEntry) => void,
  deps: React.DependencyList = []
): React.RefObject<T> {
  const elementRef = React.useRef<T>(null);
  const callbackRef = React.useRef(callback);
  const idRef = React.useRef<string>();

  // 更新回调引用
  React.useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  React.useEffect(() => {
    const element = elementRef.current;
    if (!element) {
      return;
    }

    // 生成唯一 ID
    if (!idRef.current) {
      idRef.current = `resize-observer-${Math.random().toString(36).substr(2, 9)}`;
    }

    // 开始观察
    const unobserve = resizeObserverManager.observe(
      element,
      (entry) => callbackRef.current(entry),
      idRef.current
    );

    // 清理函数
    return unobserve;
  }, [elementRef.current, ...deps]);

  return elementRef;
}

// 导入 React（如果在 Hook 中使用）
import React from 'react';
