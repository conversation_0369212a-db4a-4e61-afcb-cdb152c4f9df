import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withValidation } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';

const handler = withValidation(
  null,
  async (request: AuthenticatedRequest) => {
    // 查询所有非删除的特种纸记录的category字段
    const categories = await prisma.specialPaper.findMany({
      where: {
        isDel: false,
      },
      select: {
        category: true,
      },
      distinct: ['category'],
    });

    // 过滤掉空值和空字符串
    const filteredCategories = categories
      .map(item => item.category)
      .filter(category => category && category.trim() !== '')
      .sort(); // 按字母顺序排序

    return successResponse(filteredCategories, '获取特种纸品类列表成功');

  }
); 
export const POST = withInternalAuth(handler);