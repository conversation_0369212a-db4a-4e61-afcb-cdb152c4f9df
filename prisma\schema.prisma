// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Box {
  id                  Int            @id @default(autoincrement())
  name                String         @default("") @db.VarChar(255) /// 盒型名称
  status              Int            @default(0) @db.UnsignedTinyInt /// 状态 0: 草稿 1: 发布
  description         String?        @db.VarChar(255) /// 描述
  processingFee       Float          @default(0) @map("processing_fee") @db.Float /// 加工费
  processingBasePrice Float          @default(0) @map("processing_base_price") @db.Float /// 加工费起步价
  isDel               Boolean        @default(false) @map("is_del") /// 是否删除 0: 否 1: 是
  createdAt           DateTime       @default(now()) @map("created_at")
  updatedAt           DateTime       @default(now()) @updatedAt @map("updated_at")
  attributes          BoxAttribute[]
  parts               BoxPart[]
  formulas            BoxFormula[]
  images              BoxImage[]
  packaging           BoxPackaging?

  @@map("box")
}

model BoxAttribute {
  id        Int      @id @default(autoincrement())
  boxId     Int      @map("box_id") /// 盒型ID
  name      String   @db.VarChar(255) /// 属性名称
  code      String   @db.VarChar(512) /// 属性英文名称
  value     Float? /// 属性值
  sortOrder Int      @default(0) @map("sort_order") /// 排序顺序
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
  box       Box      @relation(fields: [boxId], references: [id])

  @@unique([boxId, code])
  @@map("box_attributes")
}

model BoxPart {
  id        Int          @id @default(autoincrement())
  boxId     Int          @map("box_id") /// 盒型ID
  name      String       @db.VarChar(255) /// 部件名称
  createdAt DateTime     @default(now()) @map("created_at")
  updatedAt DateTime     @default(now()) @updatedAt @map("updated_at")
  box       Box          @relation(fields: [boxId], references: [id])
  formulas  BoxFormula[]

  @@map("box_parts")
}

model BoxFormula {
  id         Int      @id @default(autoincrement())
  boxId      Int      @map("box_id") /// 盒型ID
  partId     Int      @map("part_id") /// 部件ID
  name       String   @db.VarChar(255) /// 公式名称
  expression String?  @db.VarChar(1024) /// 公式内容
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @default(now()) @updatedAt @map("updated_at")
  box        Box      @relation(fields: [boxId], references: [id])
  part       BoxPart  @relation(fields: [partId], references: [id])

  @@index([boxId], map: "box_formulas_box_id_fkey")
  @@index([partId], map: "box_formulas_part_id_fkey")
  @@map("box_formulas")
}

model BoxImage {
  id        Int      @id @default(autoincrement())
  boxId     Int      @map("box_id") /// 盒型ID
  name      String   @db.VarChar(255) /// 图片名称
  imageData Bytes    @map("image_data") /// 图片数据
  mimeType  String   @map("mime_type") @db.VarChar(50) /// MIME类型
  sortOrder Int      @map("sort_order") /// 排序顺序
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
  box       Box      @relation(fields: [boxId], references: [id])

  @@map("box_images")
}

// 纸盒打包信息
model BoxPackaging {
  id            Int      @id @default(autoincrement())
  boxId         Int      @unique @map("box_id") /// 盒型ID
  lengthFormula String   @map("length_formula") @db.VarChar(1024) /// 长度计算公式
  widthFormula  String   @map("width_formula") @db.VarChar(1024) /// 宽度计算公式
  heightFormula String?  @map("height_formula") @db.VarChar(1024) /// 高度计算公式
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @default(now()) @updatedAt @map("updated_at")
  box           Box      @relation(fields: [boxId], references: [id])

  @@map("box_packaging")
}

// 用户模型
model User {
  id                  Int       @id @default(autoincrement())
  name                String    @db.VarChar(100) /// 用户姓名
  phone               String    @db.VarChar(20) /// 手机号
  email               String?   @db.VarChar(100) /// 邮箱地址 可选
  password            String    @db.VarChar(255) /// 密码哈希值
  role                String    @db.VarChar(20) /// 用户角色
  expiresAt           DateTime? @map("expires_at") /// 到期时间，仅超级用户需要
  currentLoginIp      String?   @map("current_login_ip") @db.VarChar(45) /// 本次登录IP地址
  currentLoginAt      DateTime? @map("current_login_at") /// 本次登录时间
  currentLoginToken   String?   @map("current_login_token") @db.Text /// 本次登录JWT token
  lastLoginIp         String?   @map("last_login_ip") @db.VarChar(45) /// 上次登录IP地址
  lastLoginAt         DateTime? @map("last_login_at") /// 上次登录时间
  state               Int       @default(1) @db.UnsignedTinyInt /// 用户状态，1=启用，0=禁用
  isDel               Boolean   @default(false) @map("is_del") /// 软删除标记
  createdAt           DateTime  @default(now()) @map("created_at")
  updatedAt           DateTime  @default(now()) @updatedAt @map("updated_at")
  @@unique([phone, isDel])
  @@map("users")
}

// 自定义计算公式模型
model CustomFormula {
  id            Int                      @id @default(autoincrement())
  name          String                   @db.VarChar(255) /// 公式名称
  initialAmount Float                    @default(0) @map("initial_amount") /// 起步金额
  expression    String?                  @db.VarChar(1024) /// 计算公式
  status        Int                      @default(0) @db.UnsignedTinyInt /// 状态 0: 草稿 1: 发布
  isDel         Boolean                  @default(false) @map("is_del") /// 是否删除 0: 否 1: 是
  createdAt     DateTime                 @default(now()) @map("created_at")
  updatedAt     DateTime                 @default(now()) @updatedAt @map("updated_at")
  attributes    CustomFormulaAttribute[]

  @@map("custom_formula")
}

// 自定义计算公式属性
model CustomFormulaAttribute {
  id        Int           @id @default(autoincrement())
  formulaId Int           @map("formula_id") /// 公式ID
  name      String        @db.VarChar(255) /// 属性名称
  value     Float? /// 属性值
  createdAt DateTime      @default(now()) @map("created_at")
  updatedAt DateTime      @default(now()) @updatedAt @map("updated_at")
  formula   CustomFormula @relation(fields: [formulaId], references: [id])

  @@map("custom_formula_attributes")
}

// 材料尺寸配置
model MaterialSize {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(50) /// 尺寸名称，如"正度长度"
  type        String   @db.VarChar(30) /// 尺寸类型标识，如"regularLength"
  size        Float /// 尺寸值，单位为mm
  description String?  @db.VarChar(100) /// 中文描述说明
  sortOrder   Int      @default(0) @map("sort_order") /// 显示排序
  isDefault   Boolean  @default(false) @map("is_default") /// 是否为默认值
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")

  @@unique([type])
  @@map("material_size")
}

// 纸类材料
model Paper {
  id           Int      @id @default(autoincrement())
  name         String   @db.VarChar(100) /// 纸张名称
  price        Float /// 价格
  unit         String   @db.VarChar(20) /// 单位（元/吨、元/张、元/平方）
  weight       Float /// 克重
  thickness    Float /// 厚度
  regularPrice Float?   @map("regular_price") /// 正度价格
  largePrice   Float?   @map("large_price") /// 大度价格
  category     String   @db.VarChar(50) /// 材料品类（书写纸、双胶纸等）
  remark       String?  @db.Text /// 备注
  isDel        Boolean  @default(false) @map("is_del") /// 是否删除
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("paper")
}

// 卷筒材料分切尺寸
model PaperCutting {
  id              Int      @id @default(autoincrement())
  name            String   @db.VarChar(100) /// 名称
  initialCutPrice Float    @map("initial_cut_price") /// 分切起步金额
  sizes           Json /// 分切尺寸数组，存储为JSON
  isDel           Boolean  @default(false) @map("is_del") /// 是否删除
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("paper_cutting")
}

// 特种纸
model SpecialPaper {
  id        Int      @id @default(autoincrement())
  name      String   @db.VarChar(100) /// 名称
  price     Float /// 价格
  unit      String   @db.VarChar(20) /// 单位（元/吨、元/张、元/平方）
  weight    Float /// 克重
  thickness Float /// 厚度
  isRegular Boolean  @default(false) @map("is_regular") /// 是否正度
  isLarge   Boolean  @default(false) @map("is_large") /// 是否大度
  isSpecial Boolean  @default(false) @map("is_special") /// 是否特规
  size1     Float? /// 特规尺寸1
  size2     Float? /// 特规尺寸2
  category  String   @db.VarChar(50) /// 材料品类
  remark    String?  @db.Text /// 备注
  isDel     Boolean  @default(false) @map("is_del") /// 是否删除
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("special_paper")
}

// 灰板纸密度板
model GreyBoard {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(100) /// 名称
  price       Float /// 价格
  unit        String   @db.VarChar(20) /// 单位（元/张、元/吨）
  weight      Float /// 克重
  thickness   Float /// 厚度
  isRegular   Boolean  @default(false) @map("is_regular") /// 是否正度
  isLarge     Boolean  @default(false) @map("is_large") /// 是否大度
  isStockSize Boolean  @default(false) @map("is_stock_size") /// 是否按现货尺寸
  stockLength Float?   @map("stock_length") /// 现货长度
  stockWidth  Float?   @map("stock_width") /// 现货宽度
  category    String   @db.VarChar(50) /// 品类
  remark      String?  @db.Text /// 备注
  isDel       Boolean  @default(false) @map("is_del") /// 是否删除
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("grey_board")
}

// 灰板材料分切尺寸
model GreyBoardCutting {
  id              Int      @id @default(autoincrement())
  name            String   @db.VarChar(100) /// 名称
  initialCutPrice Float    @map("initial_cut_price") /// 分切起步金额
  sizes           Json /// 分切尺寸数组，存储为JSON
  isDel           Boolean  @default(false) @map("is_del") /// 是否删除
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("grey_board_cutting")
}

// 不干胶
model Sticker {
  id        Int      @id @default(autoincrement())
  name      String   @db.VarChar(100) /// 名称
  price     Float /// 价格
  unit      String   @db.VarChar(20) /// 单位（元/平方、元/张）
  weight    Float /// 克重
  category  String   @db.VarChar(50) /// 材料品类
  remark    String?  @db.Text /// 备注
  isDel     Boolean  @default(false) @map("is_del") /// 是否删除
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("sticker")
}

// 特殊材料
model SpecialMaterial {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(100) /// 名称
  price       Float /// 价格
  unit        String   @db.VarChar(20) /// 单位（元/平方、元/吨、元/张）
  thickness   Float /// 厚度
  density     Float /// 密度
  isStockSize Boolean  @default(false) @map("is_stock_size") /// 是否按现货尺寸
  stockLength Float?   @map("stock_length") /// 现货长度
  stockWidth  Float?   @map("stock_width") /// 现货宽度
  category    String   @db.VarChar(50) /// 材料品类
  remark      String?  @db.Text /// 备注
  isDel       Boolean  @default(false) @map("is_del") /// 是否删除
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("special_material")
}

// 配件
model Accessory {
  id           Int      @id @default(autoincrement())
  name         String   @db.VarChar(100) /// 名称
  price        Float /// 价格
  initialPrice Float    @map("initial_price") /// 起步价
  weight       Float /// 重量
  unit         String   @db.VarChar(20) /// 单位（元/对、元/米、元/条、元/平方）
  remark       String?  @db.Text /// 备注
  isDel        Boolean  @default(false) @map("is_del") /// 是否删除
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("accessory")
}

// 礼盒配件
model GiftBoxAccessory {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(100) /// 名称
  price       Float /// 价格
  unit        String   @db.VarChar(20) /// 单位（元/立方、元/平方）
  isStockSize Boolean  @default(false) @map("is_stock_size") /// 是否按现货尺寸
  stockLength Float?   @map("stock_length") /// 现货长度
  stockWidth  Float?   @map("stock_width") /// 现货宽度
  remark      String?  @db.Text /// 备注
  isDel       Boolean  @default(false) @map("is_del") /// 是否删除
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("gift_box_accessory")
}

// 纸箱材料
model BoxMaterial {
  id            Int      @id @default(autoincrement())
  code          String   @unique @db.VarChar(50) // 编号
  facePaper     String   @map("face_paper") @db.VarChar(100) // 面纸
  linerPaper    String   @map("liner_paper") @db.VarChar(100) // 里纸
  threeLayerBE  Float?   @map("three_layer_be") // 三层B/E
  threeLayerAC  Float?   @map("three_layer_ac") // 三层A/C
  fiveLayerABBC Float?   @map("five_layer_abbc") // 五层AB/BC
  fiveLayerEB   Float?   @map("five_layer_eb") // 五层EB
  sevenLayerEBA Float?   @map("seven_layer_eba") // 七层EBA
  remark        String?  @db.Text // 备注
  isDel         Boolean  @default(false) @map("is_del")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  @@map("box_materials")
}

// 印刷数据库
model Printing {
  id                 Int      @id @default(autoincrement())
  machineModel       String   @map("machine_model") @db.VarChar(100) /// 印刷机型
  basePrice          Float    @default(0) @map("base_price") /// 起步价，可以为0
  price1000_1999     Float    @default(0) @map("price_1000_1999") /// 数量1000-1999价格，可以为0
  price2000_2999     Float    @default(0) @map("price_2000_2999") /// 数量2000-2999价格，可以为0
  price3000_3999     Float    @default(0) @map("price_3000_3999") /// 数量3000-3999价格，可以为0
  price4000_4999     Float    @default(0) @map("price_4000_4999") /// 数量4000-4999价格，可以为0
  price5000_5999     Float    @default(0) @map("price_5000_5999") /// 数量5000-5999价格，可以为0
  price6000_6999     Float    @default(0) @map("price_6000_6999") /// 数量6000-6999价格，可以为0
  price7000_7999     Float    @default(0) @map("price_7000_7999") /// 数量7000-7999价格，可以为0
  price8000_8999     Float    @default(0) @map("price_8000_8999") /// 数量8000-8999价格，可以为0
  price9000_9999     Float    @default(0) @map("price_9000_9999") /// 数量9000-9999价格，可以为0
  price10000Plus     Float    @map("price_10000_plus") /// 10000以上价格，单价
  unit               String   @db.VarChar(20) /// 单位（元/张、元/平方、起步价+张）
  ctpPlateFee        Float    @default(0) @map("ctp_plate_fee") /// CTP板费，可以为0
  spotColorFee       Float    @default(0) @map("spot_color_fee") /// 专色费，可以为0
  remark             String?  @db.Text /// 备注
  isDel              Boolean  @default(false) @map("is_del") /// 是否删除
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("printing")
}

// 印刷机最大尺寸
model PrintingMachine {
  id          Int      @id @default(autoincrement())
  machineName String   @map("machine_name") @db.VarChar(100) /// 印刷机名称
  maxLength   Float    @map("max_length") /// 印刷长度（单位：mm）
  maxWidth    Float    @map("max_width") /// 印刷宽度（单位：mm）
  remark      String?  @db.Text /// 备注
  isDel       Boolean  @default(false) @map("is_del") /// 是否删除
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("printing_machine")
}

// 覆膜工艺
model SurfaceProcess {
  id        Int      @id @default(autoincrement())
  name      String   @db.VarChar(100) /// 名称
  price     Float /// 价格
  unit      String   @db.VarChar(20) /// 单位（元/平方、元/张）
  basePrice Float    @default(0) @map("base_price") /// 起步价，可以为0
  thickness Float    @default(0) /// 厚度，可以为0
  density   Float    @default(0) /// 密度，可以为0
  remark    String?  @db.Text /// 备注
  isDel     Boolean  @default(false) @map("is_del") /// 是否删除
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("surface_process")
}

// 丝印工艺
model SilkScreenProcess {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(100) /// 名称
  unitPrice   Float    @default(0) @map("unit_price") /// 单价，可以为0
  unit        String   @db.VarChar(20) /// 单位（元/平方、元/个）
  basePrice   Float    @default(0) @map("base_price") /// 起步价，可以为0
  materialFee Float    @default(0) @map("material_fee") /// 材料费，可以为0
  remark      String?  @db.Text /// 备注
  isDel       Boolean  @default(false) @map("is_del") /// 是否删除
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("silk_screen_process")
}

// 瓦楞工艺
model CorrugatedProcess {
  id            Int      @id @default(autoincrement())
  code          String?  @db.VarChar(50) /// 代号，可以为空
  materialName  String   @map("material_name") @db.VarChar(100) /// 材质名称
  price         Float /// 价格
  unit          String   @db.VarChar(20) /// 单位（元/平方）
  setupFee      Float    @default(0) @map("setup_fee") /// 上机费，可以为0
  thickness     Float /// 厚度，单位mm
  coreWeight1   Float    @default(0) @map("core_weight_1") /// 芯纸1，可以为0
  fluteType1    String?  @map("flute_type_1") @db.VarChar(1) /// 楞形1，A,B,C,D,E,F
  linerWeight1  Float    @default(0) @map("liner_weight_1") /// 里纸1，可以为0
  coreWeight2   Float    @default(0) @map("core_weight_2") /// 芯纸2，可以为0
  fluteType2    String?  @map("flute_type_2") @db.VarChar(1) /// 楞形2，A,B,C,D,E,F
  linerWeight2  Float    @default(0) @map("liner_weight_2") /// 里纸2，可以为0
  remark        String?  @db.Text /// 备注
  isDel         Boolean  @default(false) @map("is_del") /// 是否删除
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("corrugated_process")
}

// 瓦楞率配置
model CorrugatedRate {
  id        Int      @id @default(autoincrement())
  fluteType String   @unique @map("flute_type") @db.VarChar(1) /// 楞形 A,B,C,D,E,F
  rate      Float /// 瓦楞率
  isDel     Boolean  @default(false) @map("is_del") /// 是否删除
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("corrugated_rate")
}

// 对裱工艺
model LaminatingProcess {
  id        Int      @id @default(autoincrement())
  name      String   @db.VarChar(100) /// 名称
  price     Float /// 价格
  unit      String   @db.VarChar(20) /// 单位（元/平方、元/张）
  basePrice Float    @default(0) @map("base_price") /// 起步价，可以为0
  remark    String?  @db.Text /// 备注
  isDel     Boolean  @default(false) @map("is_del") /// 是否删除
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("laminating_process")
}

// 烫金工艺
model HotStampingProcess {
  id            Int      @id @default(autoincrement())
  name          String   @db.VarChar(100) /// 名称
  salary        Float    @default(0) /// 工资，可为0
  salaryUnit    String   @map("salary_unit") @db.VarChar(20) /// 工资单位（元/张）
  materialPrice Float    @map("material_price") /// 材料价格
  materialUnit  String   @map("material_unit") @db.VarChar(20) /// 材料单位（元/平方）
  basePrice     Float    @default(0) @map("base_price") /// 起步价，可为0
  remark        String?  @db.Text /// 备注
  isDel         Boolean  @default(false) @map("is_del") /// 是否删除
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("hot_stamping_process")
}

// 烫金版费
model HotStampingPlateFee {
  id        Int      @id @default(autoincrement())
  name      String   @db.VarChar(100) /// 名称
  price     Float    @default(0) /// 价格，可为0
  unit      String   @db.VarChar(20) /// 单位（元/个、元/平方）
  basePrice Float    @default(0) @map("base_price") /// 起步价，可为0
  remark    String?  @db.Text /// 备注
  isDel     Boolean  @default(false) @map("is_del") /// 是否删除
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("hot_stamping_plate_fee")
}

// 压纹工艺
model TexturingProcess {
  id               Int      @id @default(autoincrement())
  name             String   @db.VarChar(100) /// 名称
  textureVersion   Float    @default(0) @map("texture_version") /// 压纹版，可为0
  unit             String   @db.VarChar(20) /// 单位（元/张）
  priceBelow1000   Float    @map("price_below_1000") /// 数量1000以下总价
  price1000_1999   Float    @map("price_1000_1999") /// 数量1000-1999总价
  price2000_3999   Float    @map("price_2000_3999") /// 数量2000-3999总价
  price4000Plus    Float    @map("price_4000_plus") /// 数量4000以上单价
  remark           String?  @db.Text /// 备注
  isDel            Boolean  @default(false) @map("is_del") /// 是否删除
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("texturing_process")
}

// 凹凸工艺
model EmbossingProcess {
  id                Int      @id @default(autoincrement())
  name              String   @db.VarChar(100) /// 名称
  price             Float    @default(0) /// 价格，可以为0
  unit              String   @db.VarChar(20) /// 单位（元/个、元/平方英寸）
  basePrice         Float    @map("base_price") /// 起步价
  salary            Float /// 工资单价
  salaryBasePrice   Float    @default(0) @map("salary_base_price") /// 工资起步价，可为0
  remark            String?  @db.Text /// 备注
  isDel             Boolean  @default(false) @map("is_del") /// 是否删除
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("embossing_process")
}

// 液压工艺
model HydraulicProcess {
  id                Int      @id @default(autoincrement())
  name              String   @db.VarChar(100) /// 名称
  price             Float    @default(0) /// 价格，可以为0
  unit              String   @db.VarChar(20) /// 单位（元/个、元/平方英寸）
  basePrice         Float    @map("base_price") /// 起步价
  salary            Float /// 工资单价
  salaryBasePrice   Float    @default(0) @map("salary_base_price") /// 工资起步价，可为0
  remark            String?  @db.Text /// 备注
  isDel             Boolean  @default(false) @map("is_del") /// 是否删除
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("hydraulic_process")
}

// 加工费配置
model ProcessingFee {
  id        Int      @id @default(autoincrement())
  name      String   @db.VarChar(100) /// 名称
  unitPrice Float    @map("unit_price") /// 单价
  unit      String   @db.VarChar(20) /// 单位（元/个、元/平方、元/张）
  basePrice Float    @default(0) @map("base_price") /// 起步价，可以为0
  remark    String?  @db.Text /// 备注
  isDel     Boolean  @default(false) @map("is_del") /// 是否删除
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("processing_fee")
}

// 加工费固定参数配置（单例）
model ProcessingParams {
  id                      Int      @id @default(autoincrement())
  pvcFilm                 Float    @default(0) @map("pvc_film") /// PVC贴膜 (元/吨)
  slottingSalary          Float    @default(0) @map("slotting_salary") /// 开槽工资 (元/个)
  slottingBasePrice       Float    @default(0) @map("slotting_base_price") /// 开槽起步价
  blisterPlate            Float    @default(0) @map("blister_plate") /// 吸塑版 (元/平方)
  blisterBasePrice        Float    @default(0) @map("blister_base_price") /// 吸塑起步价
  highFrequencyPlate      Float    @default(0) @map("high_frequency_plate") /// 高频机版 (元/平方)
  highFrequencyBasePrice  Float    @default(0) @map("high_frequency_base_price") /// 高频机起步价
  sprayCodeFee            Float    @default(0) @map("spray_code_fee") /// 喷码费用 (元/个)
  sprayCodeBasePrice      Float    @default(0) @map("spray_code_base_price") /// 喷码起步价
  inspectionFee           Float    @default(0) @map("inspection_fee") /// 检验费用 (元/个)
  inspectionBasePrice     Float    @default(0) @map("inspection_base_price") /// 检验起步价
  createdAt               DateTime @default(now()) @map("created_at")
  updatedAt               DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("processing_params")
}

// 模切工艺
model DieCuttingProcess {
  id        Int      @id @default(autoincrement())
  name      String   @db.VarChar(100) /// 名称
  price     Float /// 价格
  unit      String   @db.VarChar(20) /// 单位（元/张、元/平方）
  basePrice Float    @default(0) @map("base_price") /// 起步价，可以为0
  remark    String?  @db.Text /// 备注
  isDel     Boolean  @default(false) @map("is_del") /// 是否删除
  createdAt DateTime @default(now()) @map("created_at") /// 创建时间
  updatedAt DateTime @updatedAt @map("updated_at") /// 更新时间

  @@map("die_cutting_process")
}

// 刀版费
model DieCuttingPlateFee {
  id                Int      @id @default(autoincrement())
  name              String   @db.VarChar(100) /// 名称
  price             Float /// 价格
  unit              String   @db.VarChar(20) /// 单位（元/平方、元/个）
  basePrice         Float    @default(0) @map("base_price") /// 起步金额，可以为0
  impositionQuantity Float   @default(0) @map("imposition_quantity") /// 按拼版数量，可以为0
  remark            String?  @db.Text /// 备注
  isDel             Boolean  @default(false) @map("is_del") /// 是否删除
  createdAt         DateTime @default(now()) @map("created_at") /// 创建时间
  updatedAt         DateTime @updatedAt @map("updated_at") /// 更新时间

  @@map("die_cutting_plate_fee")
}
