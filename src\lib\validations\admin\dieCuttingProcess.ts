import { z } from 'zod';
import { DIE_CUTTING_PROCESS_UNITS } from '@/types/craftSalary';

// 模切工艺表单验证模式
export const dieCuttingProcessFormSchema = z.object({
  name: z.string()
    .min(1, '请输入模切工艺名称')
    .max(100, '名称长度不能超过100字符'),
  
  price: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '价格必须是大于等于0的数字'),
  
  unit: z.enum(DIE_CUTTING_PROCESS_UNITS, {
    required_error: '请选择计价单位',
    invalid_type_error: '无效的计价单位',
  }),
  
  basePrice: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '起步价必须是大于等于0的数字'),
  
  remark: z.string()
    .max(500, '备注长度不能超过500字符')
    .optional()
    .or(z.literal(''))
});

// 模切工艺创建请求验证模式
export const createDieCuttingProcessSchema = z.object({
  name: z.string().min(1).max(100),
  price: z.number().min(0),
  unit: z.enum(DIE_CUTTING_PROCESS_UNITS),
  basePrice: z.number().min(0),
  remark: z.string().max(500).optional()
});

// 模切工艺更新请求验证模式
export const updateDieCuttingProcessSchema = createDieCuttingProcessSchema.extend({
  id: z.number().int().positive()
});

// 模切工艺列表查询参数验证模式
export const dieCuttingProcessListParamsSchema = z.object({
  page: z.number().int().positive().optional(),
  pageSize: z.number().int().positive().max(100).optional(),
  search: z.string().optional(),
  unit: z.enum(DIE_CUTTING_PROCESS_UNITS).optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
});

// 表单数据转换函数
export function transformDieCuttingProcessFormData(formData: z.infer<typeof dieCuttingProcessFormSchema>) {
  return {
    name: formData.name,
    price: Number(formData.price),
    unit: formData.unit,
    basePrice: Number(formData.basePrice),
    remark: formData.remark || undefined
  };
}

export type DieCuttingProcessFormData = z.infer<typeof dieCuttingProcessFormSchema>;
export type CreateDieCuttingProcessData = z.infer<typeof createDieCuttingProcessSchema>;
export type UpdateDieCuttingProcessData = z.infer<typeof updateDieCuttingProcessSchema>;
