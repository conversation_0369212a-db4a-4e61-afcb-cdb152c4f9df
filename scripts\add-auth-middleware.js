const fs = require('fs');
const path = require('path');

// 需要添加认证中间件的API接口目录
const adminApiDir = 'src/app/api/v1/admin';

// 递归查找所有route.ts文件
function findRouteFiles(dir) {
  const files = [];
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      files.push(...findRouteFiles(fullPath));
    } else if (item === 'route.ts') {
      files.push(fullPath);
    }
  }
  
  return files;
}

// 检查文件是否已经有认证中间件
function hasAuthMiddleware(content) {
  return content.includes('withInternalAuth') || 
         content.includes('withAdminAuth') || 
         content.includes('withAuth');
}

// 添加认证中间件到文件
function addAuthMiddleware(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  
  // 如果已经有认证中间件，跳过
  if (hasAuthMiddleware(content)) {
    console.log(`跳过 ${filePath} - 已有认证中间件`);
    return;
  }
  
  let newContent = content;
  
  // 添加导入语句
  if (!content.includes('withInternalAuth')) {
    // 查找现有的import语句
    const importMatch = content.match(/import.*from '@\/lib\/middleware\/errorHandler';/);
    if (importMatch) {
      newContent = newContent.replace(
        importMatch[0],
        `${importMatch[0]}\nimport { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';`
      );
    }
  }
  
  // 替换NextRequest为AuthenticatedRequest
  newContent = newContent.replace(/async \(request: NextRequest/g, 'async (request: AuthenticatedRequest');
  
  // 查找export const POST/GET/PUT/DELETE语句并修改
  const exportMatches = newContent.match(/export const (POST|GET|PUT|DELETE) = withValidation/g);
  if (exportMatches) {
    // 将直接导出改为先定义handler再导出
    newContent = newContent.replace(
      /export const (POST|GET|PUT|DELETE) = withValidation/g,
      'const handler = withValidation'
    );
    
    // 在文件末尾添加导出语句
    const method = exportMatches[0].match(/(POST|GET|PUT|DELETE)/)[1];
    newContent += `\nexport const ${method} = withInternalAuth(handler);`;
  }
  
  // 处理withQueryValidation的情况
  const queryValidationMatches = newContent.match(/export const (GET) = withQueryValidation/g);
  if (queryValidationMatches) {
    newContent = newContent.replace(
      /export const (GET) = withQueryValidation/g,
      'const handler = withQueryValidation'
    );
    
    const method = queryValidationMatches[0].match(/(GET)/)[1];
    newContent += `\nexport const ${method} = withInternalAuth(handler);`;
  }
  
  // 写入修改后的内容
  fs.writeFileSync(filePath, newContent);
  console.log(`已修改 ${filePath}`);
}

// 主函数
function main() {
  try {
    const routeFiles = findRouteFiles(adminApiDir);
    console.log(`找到 ${routeFiles.length} 个route.ts文件`);
    
    for (const file of routeFiles) {
      addAuthMiddleware(file);
    }
    
    console.log('批量添加认证中间件完成');
  } catch (error) {
    console.error('执行失败:', error);
  }
}

main();
