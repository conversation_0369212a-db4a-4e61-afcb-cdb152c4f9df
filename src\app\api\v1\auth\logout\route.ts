import { prisma } from '@/lib/prisma';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';
import { withAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { clearTokenCookie } from '@/lib/auth/jwt';

export const POST = withAuth(
  async (request: AuthenticatedRequest) => {
    try {
      const user = request.user;
      
      if (!user) {
        return errorResponse(ErrorCode.UNAUTHORIZED, '未授权访问', null, 401)
      }

      // 清除用户的当前登录Token
      await prisma.user.update({
        where: { id: user.userId },
        data: {
          currentLoginToken: null
        }
      });

      // 创建响应
      const response = successResponse(null, '登出成功');

      // 清除Cookie
      response.headers.set('Set-Cookie', clearTokenCookie());

      return response;

    } catch (error) {
      console.error('登出失败:', error);
      return errorResponse(ErrorCode.INTERNAL_ERROR, '登出失败，请稍后重试')
    }
  }
);
