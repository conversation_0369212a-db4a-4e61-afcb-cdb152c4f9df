import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { UpdateLaminatingProcessData, updateLaminatingProcessSchema } from '@/lib/validations/admin/laminatingProcess';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation(
  updateLaminatingProcessSchema,
  async (request: NextRequest, validatedData: UpdateLaminatingProcessData) => {
    const { id, ...data } = validatedData;

    // 检查对裱工艺是否存在
    const existingLaminatingProcess = await prisma.laminatingProcess.findFirst({
      where: {
        id: id,
        isDel: false,
      },
    });

    assert(!!existingLaminatingProcess, ErrorCode.NOT_FOUND, '对裱工艺不存在');

    // 检查名称是否与其他记录重复
    const duplicateLaminatingProcess = await prisma.laminatingProcess.findFirst({
      where: {
        name: data.name,
        id: { not: id },
        isDel: false,
      },
    });

    assert(!duplicateLaminatingProcess, ErrorCode.DUPLICATE_ENTRY, '对裱工艺名称已存在');

    // 更新对裱工艺
    const laminatingProcess = await prisma.laminatingProcess.update({
      where: { id },
      data: {
        name: data.name,
        price: data.price,
        unit: data.unit,
        basePrice: data.basePrice,
        remark: data.remark,
      },
    });

    return successResponse(laminatingProcess, '更新对裱工艺成功');
  }
); 