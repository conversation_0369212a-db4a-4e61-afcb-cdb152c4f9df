// 拼版计算相关类型定义

import { BoxPart, BoxFormula } from '@/types/box';

// 纸张规格类型
export type PaperSizeType = 'regular' | 'large' | 'custom';

// 纸张尺寸接口
export interface PaperSize {
  width: number;    // 宽度(mm)
  height: number;   // 高度(mm)
  name: string;     // 名称
}

// 材料类型判断结果
export interface MaterialTypeAnalysis {
  canSelectFacePaper: boolean;     // 可以选择面纸
  canSelectGreyBoard: boolean;     // 可以选择灰板纸
  facePartFormulas: BoxFormula[];  // 面纸相关公式
  greyPartFormulas: BoxFormula[];  // 灰板纸相关公式
}

// 部件尺寸信息
export interface PartDimension {
  part: BoxPart;           // 部件信息
  length: number;          // 长度(mm)
  width: number;           // 宽度(mm)
  area: number;            // 面积(mm²)
  canRotate: boolean;      // 是否可旋转
  isRotated: boolean;      // 是否已旋转
  materialType: 'face' | 'grey'; // 材料类型 - 修复：添加材料类型标识，避免重复显示
}

// 部件合并组
export interface PartGroup {
  id: string;              // 组ID
  name: string;            // 组名称
  parts: PartDimension[];  // 包含的部件
  totalLength: number;     // 总长度
  totalWidth: number;      // 总宽度
  totalArea: number;       // 总面积
  materialType: 'face' | 'grey'; // 材料类型
}

// 拼版计算参数
export interface ImpositionParams {
  marginLength: number;    // 长度留边(mm)
  marginWidth: number;     // 宽度留边(mm)
  bleed: number;           // 出血(mm)
  gripper: number;         // 拉规(mm)
  bite: number;            // 咬口(mm)

  // 打印机限制
  printingMachineMaxLength?: number; // 打印机最大长度(mm)
  printingMachineMaxWidth?: number;  // 打印机最大宽度(mm)

  // 材料尺寸限制
  materialMaxLength?: number;        // 材料最大长度(mm)
  materialMaxWidth?: number;         // 材料最大宽度(mm)

  enableMaxImposition?: boolean;     // 启用最大拼版
  arrangementMode?: 'normal' | 'cross'; // 排列方式

  // 生产数量参数
  quantity: number;                 // 盒子数量，用于计算实际需要的材料张数

  // 材料约束获取函数
  getMaterialConstraints?: (partGroupId: string) => {
    materialSize?: { width: number; height: number };
    printingMachineMaxLength?: number;
    printingMachineMaxWidth?: number;
    materialName?: string;
    materialSpec?: string;
  } | null;
}

// 拼版结果
export interface ImpositionResult {
  materialType: 'face' | 'grey';  // 材料类型
  partGroup: PartGroup;           // 部件组
  impositionX: number;            // 横向拼版数量
  impositionY: number;            // 纵向拼版数量
  totalImposition: number;        // 总拼版数量
  materialLength: number;         // 材料长度(mm)
  materialWidth: number;          // 材料宽度(mm)
  materialArea: number;           // 材料面积(mm²)
  efficiency: number;             // 材料利用率(%)
  sheetsNeeded: number;           // 所需张数
  wasteArea: number;              // 浪费面积(mm²)
  warnings: string[];             // 警告信息
  
  // 新增字段
  productWidth?: number;          // 实际产品宽度(mm)
  productHeight?: number;         // 实际产品高度(mm)
  isRotated?: boolean;            // 是否旋转
  printingMachineUsage?: {        // 打印机使用情况
    maxLength: number;            // 打印机最大长度
    maxWidth: number;             // 打印机最大宽度
    usedLength: number;           // 使用长度
    usedWidth: number;            // 使用宽度
    lengthUtilization: number;    // 长度利用率(%)
    widthUtilization: number;     // 宽度利用率(%)
  };
  constraintUsage?: {             // 约束使用情况（打印机+材料）
    constraintSource: string;     // 约束来源
    maxLength: number;            // 最大长度
    maxWidth: number;             // 最大宽度
    usedLength: number;           // 使用长度
    usedWidth: number;            // 使用宽度
    lengthUtilization: number;    // 长度利用率(%)
    widthUtilization: number;     // 宽度利用率(%)
    printingMachine?: {           // 打印机约束详情
      maxLength: number;
      maxWidth: number;
    };
    material?: {                  // 材料约束详情
      maxLength: number;
      maxWidth: number;
    };
  };
  
  // 排列方式详情
  arrangementDetails?: {
    mode: 'normal' | 'cross';     // 实际使用的排列方式
    pattern: string;              // 排列模式描述
    items: Array<{               // 每个产品的位置信息
      x: number;                 // X坐标
      y: number;                 // Y坐标  
      width: number;             // 宽度
      height: number;            // 高度
      rotation: number;          // 旋转角度（0或180）
      index: number;             // 产品序号
    }>;
    efficiencyGain?: number;     // 相比普通排列的效率提升
    
    // 新增：决策过程信息
    userSelectedMode?: 'normal' | 'cross';  // 用户选择的排列方式
    actualMode: 'normal' | 'cross';         // 实际使用的排列方式
    decisionReason?: string;                // 决策原因说明
    comparison?: {                          // 排列方式比较结果
      normal: {
        totalImposition: number;            // 普通排列的拼版数量
        pattern: string;                    // 普通排列模式描述
      };
      cross?: {
        totalImposition: number;            // 交叉排列的拼版数量  
        pattern: string;                    // 交叉排列模式描述
      };
    };
  };
  
  // 利用率分析详情
  utilizationAnalysis?: {
    productUtilization: number;     // 产品利用率（部件面积/含出血产品面积）
    impositionUtilization: number;  // 拼版利用率（含出血产品面积/材料面积）
    originalCalculation: number;    // 原始计算结果（用于对比）
    breakdown: {
      partArea: number;             // 部件面积（不含出血）
      productAreaWithBleed: number; // 产品面积（含出血）
      totalProductArea: number;     // 总产品面积
      materialArea: number;         // 材料总面积
      wasteArea: number;           // 浪费面积
    };
  };
}

// 拼版优化选项
export interface ImpositionOptimization {
  enableRotation: boolean;        // 启用旋转优化
  enableGroupMerge: boolean;      // 启用部件合并
  maxGroupSize: number;           // 最大组大小
  optimizeFor: 'efficiency' | 'cost' | 'time'; // 优化目标
  
  // 新增优化选项
  enableMixedRotation: boolean;   // 启用混合旋转（同一组内部件可不同旋转状态）
  enableDynamicGrouping: boolean; // 启用动态分组（智能重新组合部件）
  maxIterations: number;          // 最大优化迭代次数
  minEfficiencyThreshold: number; // 最小利用率阈值（低于此值会尝试优化）
  allowOversizeGrouping: boolean; // 允许超尺寸分组（通过旋转等方式适配）
  prioritizeMaterialSaving: boolean; // 优先节约材料
  
  // 合并策略配置
  mergeStrategy: {
    preferSimilarSizes: boolean;     // 优先合并相似尺寸的部件
    allowSizeVariance: number;       // 允许的尺寸差异百分比（0-100）
    maxPartsPerGroup: number;        // 每组最大部件数量
    minGroupEfficiency: number;      // 最小组合效率要求
  };
  
  // 评分权重配置
  scoreWeights: {
    efficiency: number;              // 材料利用率权重（0-1）
    totalImposition: number;         // 拼版数量权重（0-1）
    materialArea: number;           // 材料面积权重（0-1）
    groupCount: number;             // 分组数量权重（0-1，组数越少越好）
    rotationPenalty: number;        // 旋转惩罚权重（0-1，旋转增加工艺复杂度）
  };
}

// 拼版计算引擎接口
export interface PackagingCalculationEngine {
  // 分析材料类型
  analyzeMaterialTypes(parts: BoxPart[]): MaterialTypeAnalysis;
  
  // 计算部件尺寸
  calculatePartDimensions(parts: BoxPart[], attributes: Record<string, number>): PartDimension[];
  
  // 创建部件组
  createPartGroups(parts: PartDimension[], materialType: 'face' | 'grey'): PartGroup[];
  
  // 计算拼版
  calculateImposition(partGroup: PartGroup, params: ImpositionParams): ImpositionResult;
  
  // 优化拼版
  optimizeImposition(partGroups: PartGroup[], params: ImpositionParams, options: ImpositionOptimization): ImpositionResult[];
  
  // 新增优化方法
  intelligentOptimizeImposition(partGroups: PartGroup[], params: ImpositionParams, options: ImpositionOptimization): OptimizationScheme;
    
  // 生成合并候选项
  generateMergeCandidates(groups: PartGroup[], params: ImpositionParams, options: ImpositionOptimization): MergeCandidate[];
  
  // 评估优化方案
  evaluateOptimizationScheme(scheme: OptimizationScheme, options: ImpositionOptimization): number;
}

// 优化方案评估结果
export interface OptimizationScheme {
  id: string;                      // 方案ID
  name: string;                    // 方案名称  
  partGroups: PartGroup[];         // 优化后的部件组
  impositionResults: ImpositionResult[]; // 拼版结果
  totalScore: number;              // 总评分
  metrics: {
    totalEfficiency: number;       // 总体利用率
    totalMaterialArea: number;     // 总材料面积
    totalImposition: number;       // 总拼版数量
    groupCount: number;            // 分组数量
    rotationCount: number;         // 旋转部件数量
    estimatedCost: number;         // 预估成本
    estimatedTime: number;         // 预估用时
  };
  details: {
    mergedParts: Array<{           // 合并的部件信息
      originalGroups: string[];    // 原始组ID
      newGroup: PartGroup;         // 新组
      improvementRatio: number;    // 改进比例
    }>;
  };
}

// 部件合并候选项
export interface MergeCandidate {
  groups: PartGroup[];             // 待合并的组
  combinedGroup: PartGroup;        // 合并后的组
  efficiencyImprovement: number;   // 效率改进
  materialSaving: number;          // 材料节约
  complexity: number;              // 复杂度（越低越好）
  feasible: boolean;               // 是否可行
}
