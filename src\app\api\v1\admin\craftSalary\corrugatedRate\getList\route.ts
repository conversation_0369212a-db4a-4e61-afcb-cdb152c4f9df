import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { corrugatedRateListParamsSchema } from '@/lib/validations/admin/corrugatedProcess';
import { withValidation } from '@/lib/middleware/errorHandler';
import { paginatedResponse } from '@/lib/utils/apiResponse';
import { Prisma } from '@prisma/client';

export const POST = withValidation(
  corrugatedRateListParamsSchema,
  async (request: NextRequest, validatedQuery: any) => {
    const data = validatedQuery;
    const page = data.page ?? 1;
    const pageSize = data.pageSize ?? 10;
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const where: Prisma.CorrugatedRateWhereInput = {
      isDel: false,
    };

    // 构建排序条件
    const sortBy = data.sortBy ?? 'fluteType';
    const sortOrder = data.sortOrder ?? 'asc';
    const orderBy: Prisma.CorrugatedRateOrderByWithRelationInput = {
      [sortBy]: sortOrder
    } as Prisma.CorrugatedRateOrderByWithRelationInput;

    // 查询总数和列表数据
    const [total, list] = await Promise.all([
      prisma.corrugatedRate.count({ where }),
      prisma.corrugatedRate.findMany({
        where,
        orderBy,
        skip,
        take: pageSize,
      })
    ]);

    return paginatedResponse(
      list,
      {
        page,
        pageSize,
        total,
      },
      '获取瓦楞率配置列表成功'
    );
  }
); 