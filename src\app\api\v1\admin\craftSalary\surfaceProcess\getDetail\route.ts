import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const getSurfaceProcessDetailSchema = z.object({
  id: z.number().int().positive(),
});

export const POST = withValidation(
  getSurfaceProcessDetailSchema,
  async (request: NextRequest, validatedData: z.infer<typeof getSurfaceProcessDetailSchema>) => {
    const { id } = validatedData;

    // 查询覆膜工艺详情
    const surfaceProcess = await prisma.surfaceProcess.findFirst({
      where: {
        id: id,
        isDel: false,
      },
    });

    assert(!!surfaceProcess, ErrorCode.NOT_FOUND, '覆膜工艺不存在');

    return successResponse(surfaceProcess, '获取覆膜工艺详情成功');
  }
); 