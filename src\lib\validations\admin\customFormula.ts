import { z } from 'zod';
import { FormulaStatus } from '@/types/customFormula';
import { validateFormulaAttributes, translateChineseToPinyin } from '@/lib/utils/formula';

// 自定义公式属性基础验证规则
export const customFormulaAttributeSchema = z.object({
  id: z.number().int().positive('ID必须是正整数').optional(),
  name: z.string()
    .min(1, '属性名称不能为空')
    .max(255, '属性名称长度不能超过255'),
  value: z.number().optional().nullable(),
});

// 自定义公式基础验证规则
export const customFormulaBaseSchema = z.object({
  name: z.string()
    .min(1, '公式名称不能为空')
    .max(255, '公式名称长度不能超过255'),
  initialAmount: z.number()
    .min(0, '起步金额不能小于0')
    .max(1000000, '起步金额不能超过1,000,000'),
  expression: z.string()
    .min(1, '计算公式不能为空')
    .max(1024, '公式内容长度不能超过1024'),
  status: z.number()
    .int()
    .min(0)
    .max(1)
    .default(FormulaStatus.ENABLED),
  attributes: z.array(customFormulaAttributeSchema)
    .min(1, '请至少添加一个计算参数'),
}).superRefine((data, ctx) => {
  // 验证公式中引用的属性是否都存在
  if (data.expression && data.attributes && data.attributes.length > 0) {
    // 将属性转换为校验格式
    const attributesForValidation = data.attributes.map(attr => ({
      name: attr.name,
      code: translateChineseToPinyin(attr.name)
    }));
    
    const validation = validateFormulaAttributes(data.expression, attributesForValidation);
    if (!validation.isValid) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: `计算公式中包含未定义的属性: ${validation.missingAttributes.join(', ')}`,
        path: ['expression'],
      });
    }
  }
});

// 创建自定义公式的验证schema
export const createCustomFormulaSchema = customFormulaBaseSchema;

// 更新自定义公式的验证schema
export const updateCustomFormulaSchema = z.object({
  id: z.number().int().positive('ID必须是正整数'),
  name: z.string()
    .min(1, '公式名称不能为空')
    .max(255, '公式名称长度不能超过255'),
  initialAmount: z.number()
    .min(0, '起步金额不能小于0')
    .max(1000000, '起步金额不能超过1,000,000'),
  expression: z.string()
    .min(1, '计算公式不能为空')
    .max(1024, '公式内容长度不能超过1024'),
  status: z.number()
    .int()
    .min(0)
    .max(1)
    .default(FormulaStatus.ENABLED),
  attributes: z.array(customFormulaAttributeSchema)
    .min(1, '请至少添加一个计算参数'),
  deleteAttributes: z.array(z.number().int().positive('ID必须是正整数')).optional(),
}).superRefine((data, ctx) => {
  // 验证公式中引用的属性是否都存在
  if (data.expression && data.attributes && data.attributes.length > 0) {
    // 将属性转换为校验格式
    const attributesForValidation = data.attributes.map(attr => ({
      name: attr.name,
      code: translateChineseToPinyin(attr.name)
    }));
    
    const validation = validateFormulaAttributes(data.expression, attributesForValidation);
    if (!validation.isValid) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: `计算公式中包含未定义的属性: ${validation.missingAttributes.join(', ')}`,
        path: ['expression'],
      });
    }
  }
});

// 获取自定义公式详情的验证schema
export const getCustomFormulaDetailSchema = z.object({
  id: z.number().int().positive('ID必须是正整数'),
});

// 获取自定义公式列表的验证schema
export const getCustomFormulaListSchema = z.object({
  page: z.number().int().min(1).default(1),
  pageSize: z.number().int().min(1).max(100).default(10),
  name: z.string().optional(),
  status: z.number().int().min(0).max(1).optional(),
  startTime: z.string().datetime().optional(),
  endTime: z.string().datetime().optional(),
});

// 删除自定义公式的验证schema
export const deleteCustomFormulaSchema = z.object({
  id: z.number().int().positive('ID必须是正整数'),
});

// 导出类型
export type CustomFormulaAttribute = z.infer<typeof customFormulaAttributeSchema>;
export type CustomFormulaBase = z.infer<typeof customFormulaBaseSchema>;
export type CreateCustomFormulaParams = z.infer<typeof createCustomFormulaSchema>;
export type UpdateCustomFormulaParams = z.infer<typeof updateCustomFormulaSchema>;
export type GetCustomFormulaDetailParams = z.infer<typeof getCustomFormulaDetailSchema>;
export type GetCustomFormulaListParams = z.infer<typeof getCustomFormulaListSchema>;
export type DeleteCustomFormulaParams = z.infer<typeof deleteCustomFormulaSchema>; 
