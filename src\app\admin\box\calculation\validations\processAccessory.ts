import { z } from 'zod';

// 工艺项目校验规则
export const processItemSchema = z.object({
  id: z.number(),
  name: z.string(),
  quantity: z.number().min(0, '数量不能为负数'),
  unit: z.string().min(1, '单位不能为空'),
  unitPrice: z.number().min(0, '单价不能为负数'),
  totalPrice: z.number().min(0, '总价不能为负数'),
  parameters: z.record(z.any()).optional(),
  hotStampingDimensions: z.object({
    length: z.number().min(0, '长度不能为负数'),
    width: z.number().min(0, '宽度不能为负数'),
  }).optional(),
  embossingDimensions: z.object({
    length: z.number().min(0, '长度不能为负数'),
    width: z.number().min(0, '宽度不能为负数'),
  }).optional(),
  hydraulicDimensions: z.object({
    length: z.number().min(0, '长度不能为负数'),
    width: z.number().min(0, '宽度不能为负数'),
  }).optional(),
});

// 工艺配置校验规则 - 支持多选
export const processConfigSchema = z.object({
  printing: z.array(processItemSchema).optional(),
  surfaceProcess: z.array(processItemSchema).optional(),
  silkScreen: z.array(processItemSchema).optional(),
  hotStamping: z.array(processItemSchema).optional(),
  laminating: z.array(processItemSchema).optional(),
  embossing: z.array(processItemSchema).optional(),
  corrugated: z.array(processItemSchema).optional(),
  processCost: z.number().min(0).optional(),
});

// 选择的材料校验规则（用于配件）
export const selectedMaterialSchema = z.object({
  material: z.object({
    id: z.number(),
    name: z.string(),
    price: z.number(),
    unit: z.string(),
  }),
  quantity: z.number().min(0, '数量不能为负数'),
  unit: z.string().min(1, '单位不能为空'),
  unitPrice: z.number().min(0, '单价不能为负数'),
  totalPrice: z.number().min(0, '总价不能为负数'),
});

// 配件配置校验规则
export const accessoryConfigSchema = z.object({
  accessories: z.array(selectedMaterialSchema).optional(),
  giftBoxAccessories: z.array(selectedMaterialSchema).optional(),
  accessoryCost: z.number().min(0).optional(),
});

// 工艺与配件组合配置校验规则
export const processAccessoryConfigSchema = z.object({
  processConfig: processConfigSchema,
  accessoryConfig: accessoryConfigSchema,
});

// 导出类型
export type ProcessItemForm = z.infer<typeof processItemSchema>;
export type ProcessConfigForm = z.infer<typeof processConfigSchema>;
export type SelectedMaterialForm = z.infer<typeof selectedMaterialSchema>;
export type AccessoryConfigForm = z.infer<typeof accessoryConfigSchema>;
export type ProcessAccessoryConfigForm = z.infer<typeof processAccessoryConfigSchema>;
