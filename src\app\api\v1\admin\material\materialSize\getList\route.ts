import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { successResponse } from '@/lib/utils/apiResponse';
import { MaterialSizeConfig } from '@/types/material';

export async function POST(request: NextRequest) {
  try {
    // 获取所有材料尺寸配置，按排序字段排序
    const materialSizes = await prisma.materialSize.findMany({
      orderBy: { sortOrder: 'asc' },
      select: {
        id: true,
        name: true,
        type: true,
        size: true,
        description: true,
        sortOrder: true,
        isDefault: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // 将数据转换为配置对象格式
    const config: MaterialSizeConfig = {
      regularLength: 1092,
      regularWidth: 787,
      largeLength: 1194,
      largeWidth: 889,
      specialLength: 787,
      specialWidth: 1092,
    };

    // 根据数据库中的实际值更新配置
    materialSizes.forEach(item => {
      if (item.type && item.type in config) {
        (config as any)[item.type] = item.size;
      }
    });

    return successResponse({
      list: materialSizes,
      config: config,
    }, '获取材料尺寸配置成功');

  } catch (error) {
    console.error('获取材料尺寸配置失败:', error);
    return Response.json(
      {
        code: 500,
        message: '获取材料尺寸配置失败',
        data: null,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
} 