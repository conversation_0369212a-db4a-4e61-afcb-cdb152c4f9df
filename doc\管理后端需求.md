## 一、技术栈

- 框架: Next.js + TypeScript
- 前端组件库: Ant Design
- 数据库: MySQL
- ORM: Prisma
- 状态管理: React Context API 或 Zustand
- 公式计算引擎: math.js
- 文件存储: 本地文件系统
- 身份验证: Next-Auth
- 样式: Tailwind CSS + Ant Design

## 管理后端

这是一个印刷品的报价系统后端，你熟知当前印刷品的计算方法，并且了解如何实现
下方是管理后台的功能

### 盒型公式

一共 5 个模块

价格统一使用 元 进行计算
尺寸参数统一使毫米进行计算

#### 盒型

具有如下属性
1. 盒型名称
2. 图片多张
3. 可供用户自定义的多个用于计算的参数，例如: 长、宽、高、糊口、头部、底部
    * 需要进行唯一校验
4. 加工费起步价
5. 加工费
6. 具有计算打包尺寸的计算公式，可引用自定义的参数
    * 面纸长度
    * 面纸宽度
    * 灰板纸长度
    * 灰板纸宽度
7. 可供用户添加最多 4 个部位
8. 每个部位具有如下用户可使用参数手动填写计算的公式
    * 面纸长度
    * 面纸宽度
    * 灰板纸长度
    * 灰板纸宽度
 
#### 书刊画册

具有如下属性
1. 盒型名称
2. 图片多张
3. 可供用户自定义的多个用于计算的参数，例如: 长、宽、高、糊口、头部、底部
    * 需要进行唯一校验
4. 可有3种不同的工艺价格计算参数
    * 名称
    * 起步价
    * 加工费
5. 具有如下用户可使用参数手动填写计算的公式
    * 封面尺寸1
    * 封面尺寸2
    * 封面内芯1
    * 封面内芯2
    * 扉页尺寸1
    * 扉页尺寸2
    * 内页尺寸1
    * 内页尺寸2

#### 自定义计算公式
具有如下属性
1. 名称
2. 可供用户自定义的多个用于计算的参数，例如: 长、宽、高、糊口、头部、底部
    * 需要进行唯一校验
3. 起步金额
4. 计算公式，可使用自定义参数填写计算的公式，用户输入参数自动计算出结果


#### 利润设置

有两种计算方式
1. 按数量加利润点
2. 按金额加利润点
    * 可根据不同的计算金额设置不同的利润点


### 材料数据库


#### 损耗调整

1. 工艺损耗调整
损耗具有如下参数
* 印刷损耗
    * 5000 以下 200
    * 10000 以下 250
    * 10000 以上 0.025
* 覆膜损耗
    * 10000 以下 10
    * 10000 以上 0.001
* 丝印损耗
    * 10000 以下 30
    * 10000 以上 0.003
* 烫金损耗
    * 10000 以下 20
    * 10000 以上 0.002
* 模切损耗
    * 10000 以下 10
    * 10000 以上 0.001
* 糊盒损耗
    * 10000 以下 20
    * 10000 以上 0.002
* 印刷好减数量
    * 10000 以下 100
    * 10000 以上 0.01
每个不同的数量都可以单独进行配置

2. 固定配置
* 覆膜比面纸小 xx mm
* 瓦楞比面纸小 xx mm

#### 纸类

1. 纸张数据库
具有如下属性
    * 名称
    * 价格
    * 单位
        * 元/吨
        * 元/张
        * 元/平方
    * 克重
    * 厚度
    * 正度价格
    * 大度价格
    * 备注
    * 材料品类
        * 书写纸
        * 双胶纸

2. 卷筒材料分切尺寸

固定参数：分切费

具有如下属性
    * 名称
    * 分切起步金额
    * 可自由增加的不同分切尺寸

#### 特种纸

具有如下属性
* 名称
* 价格
* 单位
    * 元/吨
    * 元/张
    * 元/平方
* 克重
* 厚度
* 正度 可选是否
* 大度 可选是否
* 特规 可选是否
    * 为是则可以定义 尺寸1，尺寸2
* 材料品类
* 备注

#### 灰板纸密度板
1. 灰板纸密度板数据库
具有如下属性
* 名称
* 价格
* 单位
    * 元/张
    * 元/吨
* 克重
* 厚度
* 正度 可选是否
* 大度 可选是否
* 按现货尺寸 可选是否
    * 为是则可以定义 现货长度、现货宽度
* 品类
* 备注

2. 卷筒材料分切尺寸

固定参数：分切费

具有如下属性
    * 名称
    * 分切起步金额
    * 可自由增加的不同分切尺寸


#### 不干胶

具有如下属性
* 名称
* 价格
* 单位
    * 元/平方
    * 元/张
* 克重
* 备注
* 材料品类

#### 特殊材料

具有如下属性
* 名称
* 价格
* 单位
    * 元/平方
    * 元/吨
    * 元/张
* 厚度
* 密度
* 按现货尺寸 可选是否
    * 为是则可以定义 现货长度、现货宽度
* 备注
* 材料品类

#### 配件

1. 配件数据库
具有如下属性
* 名称 string
* 价格 number
* 单位
    * 元/对
    * 元/米
    * 元/条
    * 元/平方
* 备注 string

2. 礼盒配件数据库
具有如下属性
* 名称 string
* 价格 number
* 单位
    * 元/立方
    * 元/平方
* 按现货尺寸 可选是否 bool
    * 为是则可以定义
    * 现货长度 number
    * 现货宽度 number
* 备注 string

#### 纸箱材料

具有如下属性
* 编号 string
* 面纸 string
* 里纸 string
* 三层B/E float
* 三层A/C float
* 五层AB/BC float
* 五层EB float
* 七层EBA float
* 备注 string

其中的关联关系
面纸和里纸可以两两互相组合，组合不能重复

#### 材料尺寸

有以下 6 种参数，以下为默认值，可以进行修改
* 正度长: 1092
* 正度宽：787
* 大度长: 1194
* 大度宽: 889
* 特规长: 787
* 特规宽: 1092


### 工艺工资

#### 印刷

1. 印刷数据库
具备字段
    * 印刷机型 string
    * 起步价 float 可以为0
    * 数量1000-1999价格 float 可以为0，为0代表使用10000以上价格计算
    * 数量2000-2999价格 float 可以为0，为0代表使用10000以上价格计算
    * 数量3000-3999价格 float 可以为0，为0代表使用10000以上价格计算
    * 数量4000-4999价格 float 可以为0，为0代表使用10000以上价格计算
    * 数量5000-5999价格 float 可以为0，为0代表使用10000以上价格计算
    * 数量6000-6999价格 float 可以为0，为0代表使用10000以上价格计算
    * 数量7000-7999价格 float 可以为0，为0代表使用10000以上价格计算
    * 数量8000-8999价格 float 可以为0，为0代表使用10000以上价格计算
    * 数量9000-9999价格 float 可以为0，为0代表使用10000以上价格计算
    * 10000以上价格 float 单价
    * 单位
        * 元/张
        * 元/平方
        * 起步价+张
    * CTP板费 float 可以为0
    * 专色费 float 可以为0
    * 备注

2. 印刷机最大尺寸
具有如下字段
    * 印刷机名称
    * 印刷长度（单位：mm
    * 印刷宽度（单位：mm

#### 覆膜工艺
具有如下字段
    * 名称 string
    * 价格 float
    * 单位
        * 元/平方
        * 元/张
    * 起步价 float 可以为 0
    * 厚度 float 可以为 0
    * 密度 float 可以为 0
    * 备注

#### 丝印工艺
具有如下字段
    * 名称 string
    * 单价 float 可以为 0
    * 单位
        * 元/平方
        * 元/个
    * 起步价 float 可以为0
    * 材料费 float 可以为0
    * 备注

#### 瓦楞工艺
具有如下字段
    * 代号 string 可以为空
    * 材质名称 string
    * 价格 float
    * 单位
        * 元/平方
    * 上机费 float 可以为0
    * 厚度 float 单位mm
    * 材料克重
        * 芯纸1 float 可以为0
        * 楞形1 字母枚举 可以不填
            * A,B,C,D,E,F
        * 里纸1 flaot 可以为0
        * 芯纸2 float 可以为0
        * 楞形2 字母枚举 可以不填
            * A,B,C,D,E,F
        * 里纸2 flaot 可以为0
    * 备注

瓦楞率配置
楞形 A,B,C,D,E,F
瓦楞率 float

#### 对裱工艺
具有如下字段
    * 名称 string
    * 价格 float
    * 单位
        * 元/平方
        * 元/张
    * 起步价 float 可以为0
    * 备注

#### 模切工艺
1. 模切
具有如下字段
    * 名称 string
    * 价格 float
    * 单位
        * 元/张
        * 元/平方
    * 起步价 float 可以为0
    * 备注

2. 刀版费
具有如下字段
    * 名称 string
    * 价格 float
    * 单位
        * 元/平方
        * 元/个
    * 起步金额 float 可为0
    * 按拼版数量 float 可为0
    * 备注

#### 烫金工艺
1. 烫金
具有如下字段
    * 名称
    * 工资 float 可为0
    * 单位
        * 元/张
    * 材料价格 float
    * 单位
        * 元/平方
    * 起步价 float 可为0
    * 备注
2. 烫金版费
具有如下字段
    * 名称
    * 价格 float 可为0
    * 单位
        * 元/个
        * 元/平方
    * 起步价 float 可为0
    * 备注

#### 凹凸工艺
1. 压纹
具有如下字段
    * 名称
    * 压纹版 float 可为0
    * 单位
        * 元/张
    * 数量1000以下 float 总价
    * 数量1000-1999 float 总价
    * 数量2000-3999 float 总价
    * 数量4000以上 float 单价
    * 备注

2. 凹凸
具有如下字段
    * 名称
    * 价格 float 可以为0
    * 单位
        * 元/个
        * 元/平方英寸
    * 起步价 float
    * 工资 float 单价
    * 工资起步价 float 可为0
    * 备注

3. 液压
具有如下字段
    * 名称
    * 价格 float 可以为0
    * 单位
        * 元/个
        * 元/平方英寸
    * 起步价 float
    * 工资 float 单价
    * 工资起步价 float 可为0
    * 备注

#### 加工费
具有如下字段
    * 名称
    * 单价 float
    * 单位
        * 元/个
        * 元/平方
        * 元/张
    * 起步价 float 可以为0
    * 备注

其他固定参数
    * PVC贴膜 float 元/吨
    * 开槽工资 float 元/个   起步价 float 可以为0
    * 吸塑版   float 元/平方 起步价 float 可以为0
    * 高频机版 float 元/平方 起步价 float 可以为0
    * 喷码费用 float 元/个   起步价 float 可以为0
    * 检验费用 float 元/个   起步价 float 可以为0

#### 包装运费（暂定，不实现）
1. 包装
具有如下字段
    * 名称
    * 价格
    * 单位
        * 元/平方
    * 起步价 float 可为0
    * 长出血 float 可为0
    * 宽出血 float 可为0
    * 备注

2. 运费
具有如下字段
    * 地址
    * 元/kg float 可为0
    * 元/立方米 float 可为0
    * 起步价 float 可为0
    * 备注


