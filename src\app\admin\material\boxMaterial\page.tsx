'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Modal,
  Input,
  message,
  Typography,
  Row,
  Col,
  Table,
  Popconfirm,
  Form,
  InputNumber,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  EditOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { boxMaterialApi } from '@/services/adminApi';
import { BoxMaterial, BoxMaterialCreateParams, BoxMaterialUpdateParams } from '@/types/material';
import dayjs from 'dayjs';

const { Title } = Typography;

export default function BoxMaterialPage() {
  // 状态管理
  const [boxMaterialList, setBoxMaterialList] = useState<BoxMaterial[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [keyword, setKeyword] = useState('');
  const [code, setCode] = useState('');

  // 模态框相关状态
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [editingRecord, setEditingRecord] = useState<BoxMaterial | null>(null);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [form] = Form.useForm();

  // 初始加载数据
  useEffect(() => {
    fetchBoxMaterialList();
  }, []);

  // 获取纸箱材料列表
  const fetchBoxMaterialList = async (
    page = current,
    size = pageSize,
    searchKeyword = keyword,
    searchCode = code
  ) => {
    try {
      setLoading(true);
      const response = await boxMaterialApi.getList({
        page,
        pageSize: size,
        keyword: searchKeyword,
        code: searchCode,
      });

      if (response.data) {
        setBoxMaterialList(response.data.list);
        setTotal(response.data.pagination.total);
      }
    } catch (error: any) {
      message.error(error.message || '获取纸箱材料列表失败');
      setBoxMaterialList([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // 处理表格分页变化
  const handleTableChange = (pagination: any) => {
    setCurrent(pagination.current);
    setPageSize(pagination.pageSize);
    fetchBoxMaterialList(pagination.current, pagination.pageSize);
  };

  // 打开添加模态框
  const showAddModal = () => {
    setModalTitle('添加纸箱材料');
    setEditingRecord(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 打开编辑模态框
  const showEditModal = (record: BoxMaterial) => {
    setModalTitle('编辑纸箱材料');
    setEditingRecord(record);
    form.setFieldsValue(record);
    setModalVisible(true);
  };

  // 处理表单提交
  const handleFormSubmit = async (values: any) => {
    try {
      setSubmitLoading(true);
      if (editingRecord) {
        // 更新
        await boxMaterialApi.update({
          id: editingRecord.id,
          ...values,
        });
        message.success('更新成功');
      } else {
        // 创建
        await boxMaterialApi.create(values);
        message.success('创建成功');
      }
      setModalVisible(false);
      fetchBoxMaterialList();
    } catch (error: any) {
      message.error(error.message || '操作失败');
    } finally {
      setSubmitLoading(false);
    }
  };

  // 处理删除
  const handleDelete = async (id: number) => {
    try {
      await boxMaterialApi.delete(id);
      message.success('删除成功');
      fetchBoxMaterialList();
    } catch (error: any) {
      message.error(error.message || '删除失败');
    }
  };

  // 处理搜索
  const handleSearch = () => {
    setCurrent(1);
    fetchBoxMaterialList(1, pageSize);
  };

  // 处理重置
  const handleReset = () => {
    setKeyword('');
    setCode('');
    setCurrent(1);
    fetchBoxMaterialList(1, pageSize);
  };

  // 表格列定义
  const columns = [
    {
      title: '编号',
      dataIndex: 'code',
      key: 'code',
      width: 80,
      align: 'center' as const,
    },
    {
      title: '面纸',
      dataIndex: 'facePaper',
      key: 'facePaper',
      width: 100,
      align: 'center' as const,
    },
    {
      title: '里纸',
      dataIndex: 'linerPaper',
      key: 'linerPaper',
      width: 100,
      align: 'center' as const,
    },
    {
      title: '三层B/E',
      dataIndex: 'threeLayerBE',
      key: 'threeLayerBE',
      width: 80,
      align: 'center' as const,
      render: (value: number | null) => value || '-',
    },
    {
      title: '三层A/C',
      dataIndex: 'threeLayerAC',
      key: 'threeLayerAC',
      width: 80,
      align: 'center' as const,
      render: (value: number | null) => value || '-',
    },
    {
      title: '五层AB/BC',
      dataIndex: 'fiveLayerABBC',
      key: 'fiveLayerABBC',
      width: 80,
      align: 'center' as const,
      render: (value: number | null) => value || '-',
    },
    {
      title: '五层EB',
      dataIndex: 'fiveLayerEB',
      key: 'fiveLayerEB',
      width: 80,
      align: 'center' as const,
      render: (value: number | null) => value || '-',
    },
    {
      title: '七层EBA',
      dataIndex: 'sevenLayerEBA',
      key: 'sevenLayerEBA',
      width: 80,
      align: 'center' as const,
      render: (value: number | null) => value || '-',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      ellipsis: true,
      align: 'center' as const,
      width: 150,
      render: (value: string) => value || '-',
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      align: 'center' as const,
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      align: 'center' as const,
      render: (record: BoxMaterial) => (
        <Space size="small">
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => showEditModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除"
            description="确定要删除这个纸箱材料吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              danger
              size="small"
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>纸箱材料管理</Title>
      <Card>
        {/* 搜索区域 */}
        <div style={{ marginBottom: 16 }}>
          <Row gutter={16} align="middle">
            <Col span={6}>
              <Input
                placeholder="搜索编号"
                prefix={<SearchOutlined />}
                allowClear
                value={code}
                onChange={(e) => setCode(e.target.value)}
                onPressEnter={handleSearch}
              />
            </Col>
            <Col span={6}>
              <Input
                placeholder="搜索关键词"
                prefix={<SearchOutlined />}
                allowClear
                value={keyword}
                onChange={(e) => setKeyword(e.target.value)}
                onPressEnter={handleSearch}
              />
            </Col>
            <Col>
              <Space>
                <Button
                  type="primary"
                  icon={<SearchOutlined />}
                  onClick={handleSearch}
                >
                  搜索
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={handleReset}
                >
                  重置
                </Button>
              </Space>
            </Col>
            <Col flex="auto" style={{ textAlign: 'right' }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={showAddModal}
              >
                添加纸箱材料
              </Button>
            </Col>
          </Row>
        </div>

        {/* 表格区域 */}
        <Table
          columns={columns}
          dataSource={boxMaterialList}
          rowKey="id"
          loading={loading}
          pagination={{
            current,
            pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          onChange={handleTableChange}
          bordered
          size="middle"
          scroll={{ x: 1200 }}
          locale={{ emptyText: '暂无数据' }}
        />
      </Card>

      {/* 模态框 */}
      <Modal
        title={modalTitle}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFormSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="编号"
                name="code"
                rules={[
                  { required: true, message: '请输入编号' },
                  { max: 50, message: '编号长度不能超过50个字符' },
                ]}
              >
                <Input placeholder="请输入编号" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="面纸"
                name="facePaper"
                rules={[
                  { required: true, message: '请输入面纸' },
                  { max: 100, message: '面纸名称长度不能超过100个字符' },
                ]}
              >
                <Input placeholder="请输入面纸" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="里纸"
                name="linerPaper"
                rules={[
                  { required: true, message: '请输入里纸' },
                  { max: 100, message: '里纸名称长度不能超过100个字符' },
                ]}
              >
                <Input placeholder="请输入里纸" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="三层B/E"
                name="threeLayerBE"
              >
                <InputNumber
                  placeholder="请输入三层B/E"
                  min={0}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="三层A/C"
                name="threeLayerAC"
              >
                <InputNumber
                  placeholder="请输入三层A/C"
                  min={0}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="五层AB/BC"
                name="fiveLayerABBC"
              >
                <InputNumber
                  placeholder="请输入五层AB/BC"
                  min={0}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="五层EB"
                name="fiveLayerEB"
              >
                <InputNumber
                  placeholder="请输入五层EB"
                  min={0}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="七层EBA"
                name="sevenLayerEBA"
              >
                <InputNumber
                  placeholder="请输入七层EBA"
                  min={0}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="备注"
            name="remark"
            rules={[
              { max: 1000, message: '备注不能超过1000个字符' },
            ]}
          >
            <Input.TextArea
              placeholder="请输入备注"
              rows={3}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={submitLoading}
              >
                {editingRecord ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
} 