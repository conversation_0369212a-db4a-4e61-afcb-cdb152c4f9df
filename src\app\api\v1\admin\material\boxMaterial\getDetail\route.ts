import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { ErrorCode } from '@/lib/constants/errorCodes';
import { successResponse } from '@/lib/utils/apiResponse';

const schema = z.object({
  id: z.string().min(1),
});

export const POST = withValidation(
  schema,
  async (request: NextRequest, validatedQuery: z.infer<typeof schema>) => {
  const { id } = validatedQuery;

  const boxMaterial = await prisma.boxMaterial.findFirst({
    where: {
      id: Number(id),
      isDel: false,
    },
  });

  assert(!!boxMaterial, ErrorCode.RESOURCE_NOT_FOUND, '纸箱材料不存在');

  return successResponse(boxMaterial, '获取成功');
});
