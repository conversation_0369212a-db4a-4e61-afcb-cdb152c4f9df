import { z } from 'zod';
import { DIE_CUTTING_PLATE_FEE_UNITS } from '@/types/craftSalary';

// 刀版费表单验证模式
export const dieCuttingPlateFeeFormSchema = z.object({
  name: z.string()
    .min(1, '请输入刀版费名称')
    .max(100, '名称长度不能超过100字符'),
  
  price: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '价格必须是大于等于0的数字'),
  
  unit: z.enum(DIE_CUTTING_PLATE_FEE_UNITS, {
    required_error: '请选择计价单位',
    invalid_type_error: '无效的计价单位',
  }),
  
  basePrice: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '起步金额必须是大于等于0的数字'),
  
  impositionQuantity: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '按拼版数量必须是大于等于0的数字'),
  
  remark: z.string()
    .max(500, '备注长度不能超过500字符')
    .optional()
    .or(z.literal(''))
});

// 刀版费创建请求验证模式
export const createDieCuttingPlateFeeSchema = z.object({
  name: z.string().min(1).max(100),
  price: z.number().min(0),
  unit: z.enum(DIE_CUTTING_PLATE_FEE_UNITS),
  basePrice: z.number().min(0),
  impositionQuantity: z.number().min(0),
  remark: z.string().max(500).optional()
});

// 刀版费更新请求验证模式
export const updateDieCuttingPlateFeeSchema = createDieCuttingPlateFeeSchema.extend({
  id: z.number().int().positive()
});

// 刀版费列表查询参数验证模式
export const dieCuttingPlateFeeListParamsSchema = z.object({
  page: z.number().int().positive().optional(),
  pageSize: z.number().int().positive().max(100).optional(),
  search: z.string().optional(),
  unit: z.enum(DIE_CUTTING_PLATE_FEE_UNITS).optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
});

// 表单数据转换函数
export function transformDieCuttingPlateFeeFormData(formData: z.infer<typeof dieCuttingPlateFeeFormSchema>) {
  return {
    name: formData.name,
    price: Number(formData.price),
    unit: formData.unit,
    basePrice: Number(formData.basePrice),
    impositionQuantity: Number(formData.impositionQuantity),
    remark: formData.remark || undefined
  };
}

export type DieCuttingPlateFeeFormData = z.infer<typeof dieCuttingPlateFeeFormSchema>;
export type CreateDieCuttingPlateFeeData = z.infer<typeof createDieCuttingPlateFeeSchema>;
export type UpdateDieCuttingPlateFeeData = z.infer<typeof updateDieCuttingPlateFeeSchema>;
