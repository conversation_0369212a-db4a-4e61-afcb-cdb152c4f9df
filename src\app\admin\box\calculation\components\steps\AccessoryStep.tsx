'use client';

import React, { useState, useCallback, useEffect } from 'react';
import {
  Card, Row, Col, Alert, Button, Table, Modal, Typography, message,
  Space, InputNumber, Form} from 'antd';
import { PlusOutlined, DeleteOutlined, GiftOutlined } from '@ant-design/icons';
import { perfLog } from '@/lib/utils/perfLog';
import { CalculationState, AccessoryConfig } from '../../types/calculation';
import { Accessory, GiftBoxAccessory } from '@/types/material';
import { Box } from '@/types/box';
import { useAsyncError } from '@/lib/hooks/useErrorHandler';
import { accessoryApi, giftBoxaccessoryApi } from '@/services/adminApi';

const { Text, Title } = Typography;

interface AccessoryStepProps {
  state: CalculationState;
  onUpdate: {
    accessoryConfig: (accessoryConfig: Partial<AccessoryConfig>) => void;
  };
  onRecalculate: () => Promise<void>;
  sourceBox?: Box | null;
}

/**
 * 配件选择步骤组件
 */
const AccessoryStep: React.FC<AccessoryStepProps> = ({
  state,
  onUpdate,
  sourceBox
}) => {
  const { execute } = useAsyncError();
  const [loading, setLoading] = useState(false);
  const [accessoryModalVisible, setAccessoryModalVisible] = useState(false);
  const [currentAccessoryType, setCurrentAccessoryType] = useState<'normal' | 'giftBox'>('normal');

  // 配件数据状态
  const [accessoryList, setAccessoryList] = useState<Accessory[]>([]);
  const [giftBoxAccessoryList, setGiftBoxAccessoryList] = useState<GiftBoxAccessory[]>([]);

  // 配件参数输入状态
  const [accessoryParametersModalVisible, setAccessoryParametersModalVisible] = useState(false);
  const [selectedAccessoryData, setSelectedAccessoryData] = useState<Accessory | GiftBoxAccessory | null>(null);
  const [selectedAccessoryType, setSelectedAccessoryType] = useState<'normal' | 'giftBox'>('normal');

  // 普通配件参数
  const [accessoryQuantity, setAccessoryQuantity] = useState<number>(1);
  const [accessoryLength, setAccessoryLength] = useState<number>(0);
  const [accessoryWidth, setAccessoryWidth] = useState<number>(0);
  const [accessoryMeters, setAccessoryMeters] = useState<number>(0);

  // 礼盒配件参数
  const [giftBoxLength, setGiftBoxLength] = useState<number>(0);
  const [giftBoxWidth, setGiftBoxWidth] = useState<number>(0);
  const [giftBoxHeight, setGiftBoxHeight] = useState<number>(0);
  const [materialSpacing, setMaterialSpacing] = useState<number>(1); // 材料间距，默认1mm

  // 初始化配件总费用计算
  useEffect(() => {
    const accessories = state.accessoryConfig?.accessories || [];
    const giftBoxAccessories = state.accessoryConfig?.giftBoxAccessories || [];

    // 只有当配件存在但总费用未设置时才计算
    if ((accessories.length > 0 || giftBoxAccessories.length > 0) &&
        state.accessoryConfig?.accessoryCost === undefined) {
      const totalCost = calculateTotalAccessoryCost(accessories, giftBoxAccessories);
      onUpdate.accessoryConfig({
        accessoryCost: totalCost
      });
      perfLog.debug('初始化配件总费用:', totalCost);
    }
  }, [state.accessoryConfig?.accessories, state.accessoryConfig?.giftBoxAccessories, state.accessoryConfig?.accessoryCost, onUpdate]);

  // 获取配件数据
  const fetchAccessoryData = async (accessoryType: 'normal' | 'giftBox') => {
    const result = await execute(async () => {
      const params = { page: 1, pageSize: 100 };

      if (accessoryType === 'normal') {
        return await accessoryApi.getList(params);
      } else {
        return await giftBoxaccessoryApi.getList(params);
      }
    }, `获取${accessoryType === 'normal' ? '普通配件' : '礼盒配件'}数据`);

    if (result) {
      if (accessoryType === 'normal') {
        setAccessoryList(result.list || []);
      } else {
        setGiftBoxAccessoryList(result.list || []);
      }
    }
  };

  // 计算盒子尺寸（使用默认值或从属性计算）
  const calculateBoxDimensions = (): { length: number; width: number; height: number } => {
    try {
      // 获取盒子属性
      const attributes = state.basicInfo.attributes || [];

      // 尝试从属性中获取长宽高
      const lengthAttr = attributes.find(attr => attr.code === 'length' || attr.name === '长');
      const widthAttr = attributes.find(attr => attr.code === 'width' || attr.name === '宽');
      const heightAttr = attributes.find(attr => attr.code === 'height' || attr.name === '高');

      const length = lengthAttr?.value || 100;
      const width = widthAttr?.value || 100;
      const height = heightAttr?.value || 50;

      perfLog.debug('计算盒子尺寸:', { length, width, height });

      return { length, width, height };
    } catch (error) {
      perfLog.error('计算盒子尺寸失败:', error);
      return { length: 100, width: 100, height: 50 };
    }
  };

  // ==================== 验证函数 ====================

  /**
   * 检查当前礼盒配件参数是否有效
   */
  const isGiftBoxParametersValid = (): boolean => {
    if (!selectedAccessoryData || selectedAccessoryType !== 'giftBox') {
      return false;
    }

    // 检查基本参数
    if (giftBoxLength <= 0 || giftBoxWidth <= 0 || giftBoxHeight <= 0) {
      return false;
    }

    const giftBoxData = selectedAccessoryData as GiftBoxAccessory;

    // 对于现货尺寸配件，检查是否可以摆放
    if (giftBoxData.isStockSize && giftBoxData.stockLength && giftBoxData.stockWidth) {
      // 修正逻辑：只有当可以摆放多个盒子时才考虑间距
      const stockLength = giftBoxData.stockLength;
      const stockWidth = giftBoxData.stockWidth;

      let horizontalCount: number;
      let verticalCount: number;

      // 先检查不考虑间距时能摆放多少个
      const horizontalCountNoSpacing = Math.floor(stockLength / giftBoxLength);
      const verticalCountNoSpacing = Math.floor(stockWidth / giftBoxWidth);

      if (horizontalCountNoSpacing <= 1) {
        horizontalCount = horizontalCountNoSpacing;
      } else {
        horizontalCount = Math.floor((stockLength + materialSpacing) / (giftBoxLength + materialSpacing));
      }

      if (verticalCountNoSpacing <= 1) {
        verticalCount = verticalCountNoSpacing;
      } else {
        verticalCount = Math.floor((stockWidth + materialSpacing) / (giftBoxWidth + materialSpacing));
      }

      const boxesPerSheet = horizontalCount * verticalCount;
      return boxesPerSheet > 0;
    }

    return true;
  };

  // ==================== 配件价格计算函数 ====================

  /**
   * 计算普通配件价格
   */
  const calculateAccessoryPrice = (
    accessoryData: Accessory,
    parameters: {
      quantity?: number;
      length?: number;
      width?: number;
      meters?: number;
    }
  ): { unitPrice: number; totalPrice: number; calculationQuantity: number } => {
    perfLog.debug('计算普通配件价格:', { accessoryData, parameters });

    const unit = accessoryData.unit;
    const basePrice = Number(accessoryData.price) || 0;
    const initialPrice = Number(accessoryData.initialPrice) || 0;
    const boxQuantity = state.basicInfo.quantity || 1;

    let calculationQuantity: number;
    let totalPrice: number;

    switch (unit) {
      case '元/米':
        // 用户输入每个盒子使用的米数，乘以盒子数量得到总长度
        calculationQuantity = (parameters.meters || 0) * boxQuantity;
        totalPrice = basePrice * calculationQuantity;
        break;

      case '元/平方':
        // 用户输入长宽尺寸，计算面积
        const areaPerBox = ((parameters.length || 0) * (parameters.width || 0)) / 1000000; // 转换为平方米
        calculationQuantity = areaPerBox * boxQuantity; // 总面积 = 单个面积 × 盒子数量
        totalPrice = basePrice * calculationQuantity;

        perfLog.debug('元/平方计算详情:', {
          inputLength: parameters.length,
          inputWidth: parameters.width,
          areaPerBox,
          boxQuantity,
          calculationQuantity,
          basePrice,
          totalPrice
        });
        break;

      case '元/条':
      case '元/对':
        // 直接乘以盒子数量
        calculationQuantity = boxQuantity;
        totalPrice = basePrice * calculationQuantity;
        break;

      default:
        calculationQuantity = parameters.quantity || 1;
        totalPrice = basePrice * calculationQuantity;
    }

    // 应用起步价逻辑
    const finalTotalPrice = initialPrice > 0 && totalPrice < initialPrice ? initialPrice : totalPrice;
    const finalUnitPrice = calculationQuantity > 0 ? finalTotalPrice / calculationQuantity : basePrice;

    perfLog.debug('普通配件价格计算结果:', {
      unit,
      basePrice,
      initialPrice,
      boxQuantity,
      parameters,
      calculationQuantity,
      totalPrice,
      finalTotalPrice,
      finalUnitPrice,
      usedInitialPrice: finalTotalPrice === initialPrice
    });

    return {
      unitPrice: finalUnitPrice,
      totalPrice: finalTotalPrice,
      calculationQuantity
    };
  };

  /**
   * 计算礼盒配件价格
   */
  const calculateGiftBoxAccessoryPrice = (
    accessoryData: GiftBoxAccessory,
    dimensions: { length: number; width: number; height: number },
    spacing: number = 1
  ): { unitPrice: number; totalPrice: number; calculationQuantity: number } => {
    perfLog.debug('计算礼盒配件价格:', { accessoryData, dimensions });

    const unit = accessoryData.unit;
    const basePrice = Number(accessoryData.price) || 0;
    const boxQuantity = state.basicInfo.quantity || 1;

    let calculationQuantity: number;
    let totalPrice: number;

    if (unit === '元/平方') {
      // 计算盒子表面积：2 * (长×宽 + 长×高 + 宽×高)
      const singleBoxSurfaceArea = 2 * (
        (dimensions.length * dimensions.width) +
        (dimensions.length * dimensions.height) +
        (dimensions.width * dimensions.height)
      ) / 1000000; // 转换为平方米

      perfLog.debug('盒子表面积计算:', {
        dimensions,
        singleBoxSurfaceArea,
        boxQuantity
      });

      calculationQuantity = singleBoxSurfaceArea * boxQuantity;
      totalPrice = basePrice * calculationQuantity;
    } else if (unit === '元/立方') {
      // 判断是否使用现货尺寸（根据后台配置自动决定）
      const useStockDimensions = accessoryData.isStockSize || false;

      if (useStockDimensions && accessoryData.stockLength && accessoryData.stockWidth) {
        // 使用现货尺寸计算材料体积
        const stockLength = accessoryData.stockLength;
        const stockWidth = accessoryData.stockWidth;

        // 计算在现货材料上可以摆放的盒子数量
        // 修正逻辑：只有当可以摆放多个盒子时才考虑间距
        let horizontalCount: number;
        let verticalCount: number;

        // 先检查不考虑间距时能摆放多少个
        const horizontalCountNoSpacing = Math.floor(stockLength / dimensions.length);
        const verticalCountNoSpacing = Math.floor(stockWidth / dimensions.width);

        if (horizontalCountNoSpacing <= 1) {
          horizontalCount = horizontalCountNoSpacing;
        } else {
          horizontalCount = Math.floor((stockLength + spacing) / (dimensions.length + spacing));
        }

        if (verticalCountNoSpacing <= 1) {
          verticalCount = verticalCountNoSpacing;
        } else {
          verticalCount = Math.floor((stockWidth + spacing) / (dimensions.width + spacing));
        }

        const boxesPerSheet = horizontalCount * verticalCount;

        if (boxesPerSheet <= 0) {
          perfLog.warn('礼盒配件现货尺寸无法摆放盒子:', {
            stockLength,
            stockWidth,
            boxLength: dimensions.length,
            boxWidth: dimensions.width,
            spacing
          });
          // 无法摆放时，使用默认计算方式
          const singleBoxVolume = (dimensions.length * dimensions.width * dimensions.height) / 1000000000; // 转换为立方米
          calculationQuantity = singleBoxVolume * boxQuantity;
          totalPrice = basePrice * calculationQuantity;
        } else {
          // 计算需要的材料张数
          const sheetsNeeded = Math.ceil(boxQuantity / boxesPerSheet);

          // 计算材料体积（现货尺寸 × 厚度）
          const materialThickness = dimensions.height; // 使用盒子高度作为厚度
          const materialVolumePerSheet = (stockLength * stockWidth * materialThickness) / 1000000000; // 转换为立方米
          calculationQuantity = materialVolumePerSheet * sheetsNeeded;
          totalPrice = basePrice * calculationQuantity;

          perfLog.debug('礼盒配件现货尺寸计算:', {
            stockLength,
            stockWidth,
            materialThickness,
            boxesPerSheet,
            sheetsNeeded,
            materialVolumePerSheet,
            calculationQuantity,
            totalPrice
          });
        }
      } else {
        // 不使用现货尺寸，按盒子体积计算
        const singleBoxVolume = (dimensions.length * dimensions.width * dimensions.height) / 1000000000; // 转换为立方米
        calculationQuantity = singleBoxVolume * boxQuantity;
        totalPrice = basePrice * calculationQuantity;

        perfLog.debug('礼盒配件体积计算（非现货）:', {
          dimensions,
          singleBoxVolume,
          boxQuantity,
          calculationQuantity,
          totalPrice
        });
      }
    } else {
      // 其他单位类型
      calculationQuantity = boxQuantity;
      totalPrice = basePrice * calculationQuantity;
    }

    const unitPrice = calculationQuantity > 0 ? totalPrice / calculationQuantity : basePrice;

    perfLog.debug('礼盒配件价格计算结果:', {
      unit,
      calculationQuantity,
      totalPrice,
      unitPrice,
      useStockDimensions: accessoryData.isStockSize || false
    });

    return {
      unitPrice,
      totalPrice,
      calculationQuantity
    };
  };

  // ==================== 配件处理函数 ====================

  /**
   * 计算配件总费用
   */
  const calculateTotalAccessoryCost = (accessories: any[] = [], giftBoxAccessories: any[] = []): number => {
    let totalCost = 0;

    // 计算普通配件费用
    accessories.forEach(accessory => {
      totalCost += accessory.totalPrice || 0;
    });

    // 计算礼盒配件费用
    giftBoxAccessories.forEach(giftBoxAccessory => {
      totalCost += giftBoxAccessory.totalPrice || 0;
    });

    perfLog.debug('配件总费用计算:', {
      accessories: accessories.length,
      giftBoxAccessories: giftBoxAccessories.length,
      totalCost
    });

    return totalCost;
  };

  // 添加配件项目 - 打开参数输入模态框
  const handleAddAccessory = (accessoryData: Accessory | GiftBoxAccessory, type: 'normal' | 'giftBox') => {
    setSelectedAccessoryData(accessoryData);
    setSelectedAccessoryType(type);

    // 重置参数
    setAccessoryQuantity(1);
    setAccessoryLength(0);
    setAccessoryWidth(0);
    setAccessoryMeters(0);

    // 设置默认值
    if (type === 'giftBox') {
      // 使用打包公式计算默认长宽高
      const defaultDimensions = calculateBoxDimensions();
      setGiftBoxLength(defaultDimensions.length);
      setGiftBoxWidth(defaultDimensions.width);
      setGiftBoxHeight(defaultDimensions.height);
      setMaterialSpacing(1); // 设置默认间距
    }

    setAccessoryModalVisible(false);
    setAccessoryParametersModalVisible(true);
  };

  // 确认配件参数并添加到配置中
  const handleAccessoryParametersConfirm = () => {
    if (!selectedAccessoryData) return;

    try {
      let accessoryItem: any;

      if (selectedAccessoryType === 'normal') {
        // 普通配件
        const accessoryData = selectedAccessoryData as Accessory;
        const parameters = {
          quantity: accessoryQuantity,
          length: accessoryLength,
          width: accessoryWidth,
          meters: accessoryMeters
        };

        const priceResult = calculateAccessoryPrice(accessoryData, parameters);

        accessoryItem = {
          material: accessoryData,
          quantity: priceResult.calculationQuantity,
          unit: accessoryData.unit,
          unitPrice: priceResult.unitPrice,
          totalPrice: priceResult.totalPrice,
          parameters
        };

        // 添加到普通配件列表
        const currentAccessories = state.accessoryConfig?.accessories || [];
        const newAccessories = [...currentAccessories, accessoryItem];
        const currentGiftBoxAccessories = state.accessoryConfig?.giftBoxAccessories || [];

        // 计算更新后的总费用
        const newAccessoryCost = calculateTotalAccessoryCost(newAccessories, currentGiftBoxAccessories);

        onUpdate.accessoryConfig({
          accessories: newAccessories,
          accessoryCost: newAccessoryCost
        });

        perfLog.debug('添加普通配件:', accessoryItem);
      } else {
        // 礼盒配件
        const giftBoxData = selectedAccessoryData as GiftBoxAccessory;
        const dimensions = {
          length: giftBoxLength,
          width: giftBoxWidth,
          height: giftBoxHeight
        };

        const priceResult = calculateGiftBoxAccessoryPrice(giftBoxData, dimensions, materialSpacing);

        accessoryItem = {
          material: giftBoxData,
          quantity: priceResult.calculationQuantity,
          unit: giftBoxData.unit,
          unitPrice: priceResult.unitPrice,
          totalPrice: priceResult.totalPrice,
          parameters: {
            ...dimensions,
            spacing: materialSpacing
          }
        };

        // 添加到礼盒配件列表
        const currentAccessories = state.accessoryConfig?.accessories || [];
        const currentGiftBoxAccessories = state.accessoryConfig?.giftBoxAccessories || [];
        const newGiftBoxAccessories = [...currentGiftBoxAccessories, accessoryItem];

        // 计算更新后的总费用
        const newAccessoryCost = calculateTotalAccessoryCost(currentAccessories, newGiftBoxAccessories);

        onUpdate.accessoryConfig({
          giftBoxAccessories: newGiftBoxAccessories,
          accessoryCost: newAccessoryCost
        });

        perfLog.debug('添加礼盒配件:', accessoryItem);
      }

      // 关闭模态框并重置状态
      setAccessoryParametersModalVisible(false);
      setSelectedAccessoryData(null);
      setAccessoryQuantity(1);
      setAccessoryLength(0);
      setAccessoryWidth(0);
      setAccessoryMeters(0);
      setGiftBoxLength(0);
      setGiftBoxWidth(0);
      setGiftBoxHeight(0);
      setMaterialSpacing(1);

      message.success('配件添加成功');
    } catch (error) {
      perfLog.error('添加配件失败:', error);
      message.error('添加配件失败');
    }
  };

  // 打开配件选择模态框
  const openAccessoryModal = useCallback((type: 'normal' | 'giftBox') => {
    setCurrentAccessoryType(type);
    setAccessoryModalVisible(true);
    fetchAccessoryData(type);
  }, []);

  // 移除配件
  const handleRemoveAccessory = useCallback((index: number, type: 'normal' | 'giftBox') => {
    const currentConfig = state.accessoryConfig || {};

    if (type === 'normal') {
      const accessories = [...(currentConfig.accessories || [])];
      accessories.splice(index, 1);
      const giftBoxAccessories = currentConfig.giftBoxAccessories || [];

      // 计算更新后的总费用
      const newAccessoryCost = calculateTotalAccessoryCost(accessories, giftBoxAccessories);

      onUpdate.accessoryConfig({
        accessories,
        accessoryCost: newAccessoryCost
      });
    } else {
      const giftBoxAccessories = [...(currentConfig.giftBoxAccessories || [])];
      giftBoxAccessories.splice(index, 1);
      const accessories = currentConfig.accessories || [];

      // 计算更新后的总费用
      const newAccessoryCost = calculateTotalAccessoryCost(accessories, giftBoxAccessories);

      onUpdate.accessoryConfig({
        giftBoxAccessories,
        accessoryCost: newAccessoryCost
      });
    }
  }, [state.accessoryConfig, onUpdate, calculateTotalAccessoryCost]);

  // ==================== 配件选择列定义 ====================

  // 获取配件选择列
  const getAccessoryColumns = (accessoryType: 'normal' | 'giftBox') => {
    return [
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '单位',
        dataIndex: 'unit',
        key: 'unit',
      },
      {
        title: '价格',
        dataIndex: 'price',
        key: 'price',
        render: (price: number) => `¥${(price || 0).toFixed(2)}`,
      },
      {
        title: '起步价',
        dataIndex: 'initialPrice',
        key: 'initialPrice',
        render: (price: number) => price > 0 ? `¥${price.toFixed(2)}` : '-',
      },
      {
        title: '操作',
        key: 'action',
        render: (_: any, record: Accessory | GiftBoxAccessory) => (
          <Button
            type="primary"
            size="small"
            onClick={() => handleAddAccessory(record, accessoryType)}
          >
            选择
          </Button>
        ),
      },
    ];
  };

  // 配件列表列定义
  const accessoryColumns = [
    {
      title: '名称',
      dataIndex: ['material', 'name'],
      key: 'name',
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (quantity: number) => {
        const value = quantity || 0;
        if (value > 0 && value < 0.01) {
          return value.toFixed(4);
        } else if (value >= 0.01 && value < 1) {
          return value.toFixed(3);
        }
        return value.toFixed(2);
      },
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      render: (price: number) => `¥${(price || 0).toFixed(2)}`,
    },
    {
      title: '总价',
      dataIndex: 'totalPrice',
      key: 'totalPrice',
      render: (price: number) => (
        <Text type="success">¥{(price || 0).toFixed(2)}</Text>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any, index: number) => (
        <Button
          type="text"
          danger
          size="small"
          icon={<DeleteOutlined />}
          onClick={() => handleRemoveAccessory(index, record.type)}
        />
      ),
    },
  ];

  return (
    <div>
      {/* 垂直布局：普通配件在上，礼盒配件在下 */}
      <Row gutter={[0, 16]}>
        <Col span={24}>
          <Card
            title={
              <Space>
                <GiftOutlined />
                <span>普通配件</span>
              </Space>
            }
            extra={
              <Button
                type="primary"
                size="small"
                icon={<PlusOutlined />}
                onClick={() => openAccessoryModal('normal')}
              >
                添加配件
              </Button>
            }
          >
            {state.accessoryConfig?.accessories && state.accessoryConfig.accessories.length > 0 ? (
              <Table
                dataSource={state.accessoryConfig.accessories.map((item, index) => ({ ...item, type: 'normal', key: index }))}
                columns={accessoryColumns}
                pagination={false}
                size="small"
              />
            ) : (
              <Text type="secondary">暂无选择的普通配件</Text>
            )}
          </Card>
        </Col>

        <Col span={24}>
          <Card
            title={
              <Space>
                <GiftOutlined />
                <span>礼盒配件</span>
              </Space>
            }
            extra={
              <Button
                type="primary"
                size="small"
                icon={<PlusOutlined />}
                onClick={() => openAccessoryModal('giftBox')}
              >
                添加配件
              </Button>
            }
          >
            {state.accessoryConfig?.giftBoxAccessories && state.accessoryConfig.giftBoxAccessories.length > 0 ? (
              <Table
                dataSource={state.accessoryConfig.giftBoxAccessories.map((item, index) => ({ ...item, type: 'giftBox', key: index }))}
                columns={accessoryColumns}
                pagination={false}
                size="small"
              />
            ) : (
              <Text type="secondary">暂无选择的礼盒配件</Text>
            )}
          </Card>
        </Col>
      </Row>

      {/* 配件总费用显示 */}
      {((state.accessoryConfig?.accessories?.length || 0) > 0 || (state.accessoryConfig?.giftBoxAccessories?.length || 0) > 0) && (
        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <Title level={4}>
            配件总费用: <Text type="success">¥{(state.accessoryConfig?.accessoryCost || 0).toFixed(2)}</Text>
          </Title>
        </div>
      )}

      {/* 配件选择模态框 */}
      <Modal
        title={`选择${currentAccessoryType === 'normal' ? '普通配件' : '礼盒配件'}`}
        open={accessoryModalVisible}
        onCancel={() => setAccessoryModalVisible(false)}
        footer={null}
        width={800}
      >
        <Table
          dataSource={currentAccessoryType === 'normal' ? accessoryList : giftBoxAccessoryList as any}
          columns={getAccessoryColumns(currentAccessoryType)}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 10 }}
        />
      </Modal>

      {/* 配件参数输入模态框 */}
      <Modal
        title={`配件参数设置 - ${selectedAccessoryData?.name || ''}`}
        open={accessoryParametersModalVisible}
        onOk={handleAccessoryParametersConfirm}
        onCancel={() => {
          setAccessoryParametersModalVisible(false);
          setSelectedAccessoryData(null);
          setAccessoryQuantity(1);
          setAccessoryLength(0);
          setAccessoryWidth(0);
          setAccessoryMeters(0);
          setGiftBoxLength(0);
          setGiftBoxWidth(0);
          setGiftBoxHeight(0);
          setMaterialSpacing(1);
        }}
        okText="确认添加"
        cancelText="取消"
        width={500}
        okButtonProps={{
          disabled: selectedAccessoryType === 'giftBox' && !isGiftBoxParametersValid()
        }}
      >
        <div style={{ padding: '16px 0' }}>
          <Alert
            message="配件参数设置"
            description="请根据配件单位类型输入相应的参数，系统将自动计算价格。"
            type="info"
            style={{ marginBottom: 16 }}
          />

          {selectedAccessoryData && (
            <Card size="small" style={{ marginBottom: 16 }}>
              <Typography.Text strong>配件信息：</Typography.Text>
              <div>名称：{selectedAccessoryData.name}</div>
              <div>单位：{selectedAccessoryData.unit}</div>
              <div>价格：¥{Number(selectedAccessoryData.price || 0).toFixed(2)}</div>
              {selectedAccessoryType === 'normal' && Number((selectedAccessoryData as Accessory).initialPrice || 0) > 0 && (
                <div>起步价：¥{Number((selectedAccessoryData as Accessory).initialPrice).toFixed(2)}</div>
              )}
            </Card>
          )}

          {selectedAccessoryType === 'normal' && selectedAccessoryData && (
            <div>
              <Title level={5}>普通配件参数</Title>
              <Form layout="vertical">
                {selectedAccessoryData.unit === '元/米' && (
                  <Form.Item label="每个盒子使用米数" required>
                    <InputNumber
                      value={accessoryMeters}
                      onChange={(value) => setAccessoryMeters(value || 0)}
                      min={0}
                      step={0.1}
                      precision={1}
                      style={{ width: '100%' }}
                      placeholder="请输入每个盒子使用的米数"
                      addonAfter="米"
                    />
                  </Form.Item>
                )}
                {selectedAccessoryData.unit === '元/平方' && (
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item label="长度 (mm)" required>
                        <InputNumber
                          value={accessoryLength}
                          onChange={(value) => setAccessoryLength(value || 0)}
                          min={0}
                          precision={1}
                          style={{ width: '100%' }}
                          placeholder="请输入长度"
                          addonAfter="mm"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="宽度 (mm)" required>
                        <InputNumber
                          value={accessoryWidth}
                          onChange={(value) => setAccessoryWidth(value || 0)}
                          min={0}
                          precision={1}
                          style={{ width: '100%' }}
                          placeholder="请输入宽度"
                          addonAfter="mm"
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                )}
                {(selectedAccessoryData.unit === '元/条' || selectedAccessoryData.unit === '元/对') && (
                  <Alert
                    message="此配件按盒子数量计算"
                    description={`单位：${selectedAccessoryData.unit}，将按照盒子数量 ${state.basicInfo.quantity || 1} 进行计算。`}
                    type="info"
                    style={{ marginBottom: 16 }}
                  />
                )}
              </Form>
            </div>
          )}

          {selectedAccessoryType === 'giftBox' && selectedAccessoryData && (
            <div>
              <Title level={5}>礼盒配件参数</Title>
              <Form layout="vertical">
                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item label="长度 (mm)" required>
                      <InputNumber
                        value={giftBoxLength}
                        onChange={(value) => setGiftBoxLength(value || 0)}
                        min={0}
                        precision={1}
                        style={{ width: '100%' }}
                        placeholder="请输入盒子长度"
                        addonAfter="mm"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="宽度 (mm)" required>
                      <InputNumber
                        value={giftBoxWidth}
                        onChange={(value) => setGiftBoxWidth(value || 0)}
                        min={0}
                        precision={1}
                        style={{ width: '100%' }}
                        placeholder="请输入盒子宽度"
                        addonAfter="mm"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="高度 (mm)" required>
                      <InputNumber
                        value={giftBoxHeight}
                        onChange={(value) => setGiftBoxHeight(value || 0)}
                        min={0}
                        precision={1}
                        style={{ width: '100%' }}
                        placeholder="请输入盒子高度"
                        addonAfter="mm"
                      />
                    </Form.Item>
                  </Col>
                </Row>

                {(selectedAccessoryData as GiftBoxAccessory).isStockSize && (
                  <Form.Item label="材料间距 (mm)" required>
                    <InputNumber
                      value={materialSpacing}
                      onChange={(value) => setMaterialSpacing(value || 1)}
                      min={0}
                      precision={1}
                      style={{ width: '100%' }}
                      placeholder="请输入材料间距"
                      addonAfter="mm"
                    />
                  </Form.Item>
                )}

                {selectedAccessoryData.unit === '元/平方' && (
                  <Alert
                    message="按盒子表面积计算"
                    description="将根据盒子的长、宽、高计算表面积，然后乘以盒子数量。"
                    type="info"
                    style={{ marginBottom: 16 }}
                  />
                )}

                {selectedAccessoryData.unit === '元/立方' && (
                  <Alert
                    message="按体积计算"
                    description={
                      (selectedAccessoryData as GiftBoxAccessory).isStockSize
                        ? "将根据现货材料尺寸和盒子摆放数量计算材料体积。"
                        : "将根据盒子体积乘以盒子数量计算。"
                    }
                    type="info"
                    style={{ marginBottom: 16 }}
                  />
                )}

                {/* 现货尺寸验证提示 */}
                {selectedAccessoryType === 'giftBox' &&
                 (selectedAccessoryData as GiftBoxAccessory).isStockSize &&
                 giftBoxLength > 0 && giftBoxWidth > 0 && !isGiftBoxParametersValid() && (
                  <Alert
                    message="无法摆放"
                    description="当前盒子尺寸无法在现货材料上摆放，请调整盒子尺寸或材料间距。"
                    type="error"
                    style={{ marginBottom: 16 }}
                  />
                )}
              </Form>
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default AccessoryStep;
