import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getPaperCuttingDetailSchema, GetPaperCuttingDetailParams } from '@/lib/validations/admin/paperCutting';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation<GetPaperCuttingDetailParams>(
  getPaperCuttingDetailSchema,
  async (request: NextRequest, validatedQuery: GetPaperCuttingDetailParams) => {
    const data = validatedQuery;

    // 查询分切尺寸详细信息
    const paperCutting = await prisma.paperCutting.findFirst({
      where: {
        id: data.id,
        isDel: false,
      },
      select: {
        id: true,
        name: true,
        initialCutPrice: true,
        sizes: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    assertExists(paperCutting, ErrorCode.MATERIAL_NOT_FOUND, '分切尺寸不存在');

    // 处理sizes字段，确保返回的是数组
    const formattedData = {
      ...paperCutting,
      sizes: paperCutting.sizes ? (Array.isArray(paperCutting.sizes) ? paperCutting.sizes : JSON.parse(String(paperCutting.sizes))) : []
    };

    return successResponse(formattedData, '获取分切尺寸详情成功');
  }
); 