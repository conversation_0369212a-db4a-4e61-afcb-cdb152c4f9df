import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { updateGreyBoardCuttingSchema, UpdateGreyBoardCuttingParams } from '@/lib/validations/admin/greyBoardCutting';
import { withValidation, assert, assertExists } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation<UpdateGreyBoardCuttingParams>(
  updateGreyBoardCuttingSchema,
  async (request: NextRequest, validatedData: UpdateGreyBoardCuttingParams) => {
    // 检查记录是否存在
    const existingRecord = await prisma.greyBoardCutting.findUnique({
      where: { id: validatedData.id, isDel: false },
    });
    assertExists(existingRecord, ErrorCode.MATERIAL_NOT_FOUND, '分切尺寸不存在');

    // 检查名称重复（排除自己）
    const duplicateName = await prisma.greyBoardCutting.findFirst({
      where: {
        name: validatedData.name,
        id: { not: validatedData.id },
        isDel: false,
      },
    });
    assert(!duplicateName, ErrorCode.MATERIAL_NAME_EXISTS, '分切尺寸名称已存在');

    // 更新记录
    const result = await prisma.greyBoardCutting.update({
      where: { id: validatedData.id },
      data: {
        name: validatedData.name,
        initialCutPrice: validatedData.initialCutPrice,
        sizes: validatedData.sizes,
        updatedAt: new Date(),
      },
      select: {
        id: true,
        name: true,
        initialCutPrice: true,
        sizes: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return successResponse(result, '更新分切尺寸成功');
  }
); 