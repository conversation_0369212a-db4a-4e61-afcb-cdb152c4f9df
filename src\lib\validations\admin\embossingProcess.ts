import { z } from 'zod';
import { 
  TEXTURING_PROCESS_UNITS, 
  EMBOSSING_HYDRAULIC_PROCESS_UNITS 
} from '@/types/craftSalary';

// ===========================================
// 压纹工艺验证模式
// ===========================================

// 压纹工艺表单验证模式
export const texturingProcessFormSchema = z.object({
  name: z.string()
    .min(1, '请输入压纹工艺名称')
    .max(100, '名称长度不能超过100字符'),
  
  textureVersion: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '压纹版必须是大于等于0的数字'),
  
  unit: z.enum(TEXTURING_PROCESS_UNITS, {
    required_error: '请选择计价单位',
    invalid_type_error: '无效的计价单位',
  }),
  
  priceBelow1000: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '数量1000以下价格必须是大于等于0的数字'),
  
  price1000_1999: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '数量1000-1999价格必须是大于等于0的数字'),
  
  price2000_3999: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '数量2000-3999价格必须是大于等于0的数字'),
  
  price4000Plus: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num > 0;
    }, '数量4000以上单价必须是大于0的数字'),
  
  remark: z.string()
    .max(500, '备注长度不能超过500字符')
    .optional()
    .or(z.literal(''))
});

// 压纹工艺创建请求验证模式
export const createTexturingProcessSchema = z.object({
  name: z.string().min(1).max(100),
  textureVersion: z.number().min(0),
  unit: z.enum(TEXTURING_PROCESS_UNITS),
  priceBelow1000: z.number().min(0),
  price1000_1999: z.number().min(0),
  price2000_3999: z.number().min(0),
  price4000Plus: z.number().positive(),
  remark: z.string().max(500).optional()
});

// 压纹工艺更新请求验证模式
export const updateTexturingProcessSchema = createTexturingProcessSchema.extend({
  id: z.number().positive()
});

// ===========================================
// 凹凸工艺验证模式
// ===========================================

// 凹凸工艺表单验证模式
export const embossingProcessFormSchema = z.object({
  name: z.string()
    .min(1, '请输入凹凸工艺名称')
    .max(100, '名称长度不能超过100字符'),
  
  price: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '价格必须是大于等于0的数字'),
  
  unit: z.enum(EMBOSSING_HYDRAULIC_PROCESS_UNITS, {
    required_error: '请选择计价单位',
    invalid_type_error: '无效的计价单位',
  }),
  
  basePrice: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '起步价必须是大于等于0的数字'),
  
  salary: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '工资单价必须是大于等于0的数字'),
  
  salaryBasePrice: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '工资起步价必须是大于等于0的数字'),
  
  remark: z.string()
    .max(500, '备注长度不能超过500字符')
    .optional()
    .or(z.literal(''))
});

// 凹凸工艺创建请求验证模式
export const createEmbossingProcessSchema = z.object({
  name: z.string().min(1).max(100),
  price: z.number().min(0),
  unit: z.enum(EMBOSSING_HYDRAULIC_PROCESS_UNITS),
  basePrice: z.number().min(0),
  salary: z.number().min(0),
  salaryBasePrice: z.number().min(0),
  remark: z.string().max(500).optional()
});

// 凹凸工艺更新请求验证模式
export const updateEmbossingProcessSchema = createEmbossingProcessSchema.extend({
  id: z.number().positive()
});

// ===========================================
// 液压工艺验证模式
// ===========================================

// 液压工艺表单验证模式
export const hydraulicProcessFormSchema = z.object({
  name: z.string()
    .min(1, '请输入液压工艺名称')
    .max(100, '名称长度不能超过100字符'),
  
  price: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '价格必须是大于等于0的数字'),
  
  unit: z.enum(EMBOSSING_HYDRAULIC_PROCESS_UNITS, {
    required_error: '请选择计价单位',
    invalid_type_error: '无效的计价单位',
  }),
  
  basePrice: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '起步价必须是大于等于0的数字'),
  
  salary: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '工资单价必须是大于等于0的数字'),
  
  salaryBasePrice: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '工资起步价必须是大于等于0的数字'),
  
  remark: z.string()
    .max(500, '备注长度不能超过500字符')
    .optional()
    .or(z.literal(''))
});

// 液压工艺创建请求验证模式
export const createHydraulicProcessSchema = z.object({
  name: z.string().min(1).max(100),
  price: z.number().min(0),
  unit: z.enum(EMBOSSING_HYDRAULIC_PROCESS_UNITS),
  basePrice: z.number().min(0),
  salary: z.number().min(0),
  salaryBasePrice: z.number().min(0),
  remark: z.string().max(500).optional()
});

// 液压工艺更新请求验证模式
export const updateHydraulicProcessSchema = createHydraulicProcessSchema.extend({
  id: z.number().positive()
});

// ===========================================
// 通用查询参数验证模式
// ===========================================

// 列表查询参数验证模式
export const embossingProcessListSchema = z.object({
  page: z.number().min(1).optional(),
  pageSize: z.number().min(1).max(100).optional(),
  search: z.string().max(100).optional(),
  unit: z.enum(EMBOSSING_HYDRAULIC_PROCESS_UNITS).optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
});

// ID参数验证模式
export const idParamSchema = z.object({
  id: z.number().positive('ID必须是正整数')
}); 