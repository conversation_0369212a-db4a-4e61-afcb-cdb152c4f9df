import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createGreyBoardSchema, CreateGreyBoardParams, validateStockSize } from '@/lib/validations/admin/greyBoard';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation<CreateGreyBoardParams>(
  createGreyBoardSchema,
  async (request: AuthenticatedRequest, validatedData: CreateGreyBoardParams) => {
    // 检查灰板纸名称是否已存在
    const existingGreyBoard = await prisma.greyBoard.findFirst({
      where: { 
        name: validatedData.name,
        isDel: false 
      }
    });
    
    assert(
      !existingGreyBoard,
      ErrorCode.DUPLICATE_ENTRY,
      '灰板纸名称已存在'
    );

    // 现货尺寸条件验证
    const stockSizeValidation = validateStockSize(validatedData);
    assert(
      stockSizeValidation.success,
      ErrorCode.INVALID_PARAMETERS,
      stockSizeValidation.error
    );

    // 创建灰板纸
    const greyBoard = await prisma.greyBoard.create({
      data: {
        name: validatedData.name,
        price: validatedData.price,
        unit: validatedData.unit,
        weight: validatedData.weight,
        thickness: validatedData.thickness,
        isRegular: validatedData.isRegular,
        isLarge: validatedData.isLarge,
        isStockSize: validatedData.isStockSize,
        stockLength: validatedData.isStockSize ? validatedData.stockLength : null,
        stockWidth: validatedData.isStockSize ? validatedData.stockWidth : null,
        category: validatedData.category,
        remark: validatedData.remark || null,
        isDel: false,
      },
    });

    return successResponse(
      greyBoard,
      '创建灰板纸成功'
    );
  }
); 
export const POST = withInternalAuth(handler);