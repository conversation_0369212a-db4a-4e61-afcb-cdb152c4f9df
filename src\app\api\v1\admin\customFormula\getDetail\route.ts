import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCustomFormulaDetailSchema, GetCustomFormulaDetailParams } from '@/lib/validations/admin/customFormula';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation<GetCustomFormulaDetailParams>(
  getCustomFormulaDetailSchema,
  async (request: AuthenticatedRequest, validatedQuery: GetCustomFormulaDetailParams) => {
    const data = validatedQuery;

    // 查询公式详细信息
    const formula = await prisma.customFormula.findFirst({
      where: {
        id: data.id,
        isDel: false,
      },
      include: {
        attributes: {
          select: {
            id: true,
            name: true,
            value: true,
          },
        },
      },
    });

    assertExists(formula, ErrorCode.FORMULA_NOT_FOUND, '自定义公式不存在');

    return successResponse(formula, '获取自定义公式详情成功');
  }
);

export const POST = withInternalAuth(handler);