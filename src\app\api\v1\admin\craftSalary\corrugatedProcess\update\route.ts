import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { UpdateCorrugatedProcessData, updateCorrugatedProcessSchema } from '@/lib/validations/admin/corrugatedProcess';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation(
  updateCorrugatedProcessSchema,
  async (request: AuthenticatedRequest, validatedData: UpdateCorrugatedProcessData) => {
    const { id, ...data } = validatedData;

    // 检查瓦楞工艺是否存在
    const existingCorrugatedProcess = await prisma.corrugatedProcess.findFirst({
      where: {
        id: id,
        isDel: false,
      },
    });

    assert(!!existingCorrugatedProcess, ErrorCode.NOT_FOUND, '瓦楞工艺不存在');

    // 检查材质名称是否与其他记录重复
    const duplicateCorrugatedProcess = await prisma.corrugatedProcess.findFirst({
      where: {
        materialName: data.materialName,
        id: { not: id },
        isDel: false,
      },
    });

    assert(!duplicateCorrugatedProcess, ErrorCode.DUPLICATE_ENTRY, '材质名称已存在');

    // 更新瓦楞工艺
    const corrugatedProcess = await prisma.corrugatedProcess.update({
      where: { id },
      data: {
        code: data.code,
        materialName: data.materialName,
        price: data.price,
        unit: data.unit,
        setupFee: data.setupFee,
        thickness: data.thickness,
        coreWeight1: data.coreWeight1,
        fluteType1: data.fluteType1,
        linerWeight1: data.linerWeight1,
        coreWeight2: data.coreWeight2,
        fluteType2: data.fluteType2,
        linerWeight2: data.linerWeight2,
        remark: data.remark,
      },
    });

    return successResponse(corrugatedProcess, '更新瓦楞工艺成功');
  }
); 
export const POST = withInternalAuth(handler);