import { z } from 'zod';
import { MaterialUnit, PaperUnit, GreyBoardUnit, StickerUnit, AccessoryUnit, GiftBoxAccessoryUnit } from '@/types/material';

// 纸类材料验证
export const paperSchema = z.object({
  id: z.number().optional(),
  name: z.string().min(1, '纸张名称不能为空'),
  price: z.number().min(0, '价格不能小于0'),
  unit: z.nativeEnum(MaterialUnit).refine(
    (val) => [MaterialUnit.YUAN_PER_SQUARE, MaterialUnit.YUAN_PER_TON, MaterialUnit.YUAN_PER_SHEET].includes(val),
    '无效的纸类材料单位'
  ),
  weight: z.number().min(0, '克重不能小于0'),
  thickness: z.number().min(0, '厚度不能小于0'),
  regularPrice: z.number().min(0, '正度价格不能小于0').optional(),
  largePrice: z.number().min(0, '大度价格不能小于0').optional(),
  category: z.string().min(1, '材料品类不能为空'),
  remark: z.string().optional(),
  isDel: z.boolean().default(false),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

// 灰板纸验证
export const greyBoardSchema = z.object({
  id: z.number().optional(),
  name: z.string().min(1, '名称不能为空'),
  price: z.number().min(0, '价格不能小于0'),
  unit: z.nativeEnum(MaterialUnit).refine(
    (val) => [MaterialUnit.YUAN_PER_SHEET, MaterialUnit.YUAN_PER_TON].includes(val),
    '无效的灰板纸单位'
  ),
  weight: z.number().min(0, '克重不能小于0'),
  thickness: z.number().min(0, '厚度不能小于0'),
  isRegular: z.boolean(),
  isLarge: z.boolean(),
  isStockSize: z.boolean(),
  stockLength: z.number().min(0, '现货长度不能小于0').optional(),
  stockWidth: z.number().min(0, '现货宽度不能小于0').optional(),
  category: z.string().min(1, '品类不能为空'),
  remark: z.string().optional(),
  isDel: z.boolean().default(false),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

// 不干胶验证
export const stickerSchema = z.object({
  id: z.number().optional(),
  name: z.string().min(1, '名称不能为空'),
  price: z.number().min(0, '价格不能小于0'),
  unit: z.nativeEnum(MaterialUnit).refine(
    (val) => [MaterialUnit.YUAN_PER_SQUARE, MaterialUnit.YUAN_PER_SHEET].includes(val),
    '无效的不干胶单位'
  ),
  weight: z.number().min(0, '克重不能小于0'),
  category: z.string().min(1, '材料品类不能为空'),
  remark: z.string().optional(),
  isDel: z.boolean().default(false),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

// 配件验证
export const accessorySchema = z.object({
  id: z.number().optional(),
  name: z.string().min(1, '名称不能为空'),
  price: z.number().min(0, '价格不能小于0'),
  initialPrice: z.number().min(0, '起步价不能小于0'),
  weight: z.number().min(0, '重量不能小于0'),
  unit: z.nativeEnum(MaterialUnit).refine(
    (val) => [MaterialUnit.YUAN_PER_PAIR, MaterialUnit.YUAN_PER_METER, MaterialUnit.YUAN_PER_STRIP, MaterialUnit.YUAN_PER_SQUARE].includes(val),
    '无效的配件单位'
  ),
  remark: z.string().optional(),
  isDel: z.boolean().default(false),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

// 礼盒配件验证
export const giftBoxAccessorySchema = z.object({
  id: z.number().optional(),
  name: z.string().min(1, '名称不能为空'),
  price: z.number().min(0, '价格不能小于0'),
  initialPrice: z.number().min(0, '起步价不能小于0'),
  weight: z.number().min(0, '重量不能小于0'),
  unit: z.nativeEnum(MaterialUnit).refine(
    (val) => [MaterialUnit.YUAN_PER_CUBIC, MaterialUnit.YUAN_PER_SQUARE].includes(val),
    '无效的礼盒配件单位'
  ),
  remark: z.string().optional(),
  isDel: z.boolean().default(false),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

// 创建纸类材料验证
export const createPaperSchema = paperSchema.omit({ id: true, isDel: true, createdAt: true, updatedAt: true });

// 更新纸类材料验证
export const updatePaperSchema = paperSchema.omit({ createdAt: true, updatedAt: true });

// 创建灰板纸验证
export const createGreyBoardSchema = greyBoardSchema.omit({ id: true, isDel: true, createdAt: true, updatedAt: true });

// 更新灰板纸验证
export const updateGreyBoardSchema = greyBoardSchema.omit({ createdAt: true, updatedAt: true });

// 创建不干胶验证
export const createStickerSchema = stickerSchema.omit({ id: true, isDel: true, createdAt: true, updatedAt: true });

// 更新不干胶验证
export const updateStickerSchema = stickerSchema.omit({ createdAt: true, updatedAt: true });

// 创建配件验证
export const createAccessorySchema = accessorySchema.omit({ id: true, isDel: true, createdAt: true, updatedAt: true });

// 更新配件验证
export const updateAccessorySchema = accessorySchema.omit({ createdAt: true, updatedAt: true });

// 创建礼盒配件验证
export const createGiftBoxAccessorySchema = giftBoxAccessorySchema.omit({ id: true, isDel: true, createdAt: true, updatedAt: true });

// 更新礼盒配件验证
export const updateGiftBoxAccessorySchema = giftBoxAccessorySchema.omit({ createdAt: true, updatedAt: true });

// 获取材料列表验证
export const getMaterialListSchema = z.object({
  page: z.number().min(1, '页码必须大于0'),
  pageSize: z.number().min(1, '每页数量必须大于0'),
  name: z.string().optional(),
  category: z.string().optional(),
  type: z.enum(['paper', 'greyBoard', 'sticker', 'accessory', 'giftBoxAccessory']).optional(),
});

// 获取材料详情验证
export const getMaterialDetailSchema = z.object({
  id: z.number().min(1, 'ID必须大于0'),
  type: z.enum(['paper', 'greyBoard', 'sticker', 'accessory', 'giftBoxAccessory']),
});

// 删除材料验证
export const deleteMaterialSchema = z.object({
  id: z.number().min(1, 'ID必须大于0'),
  type: z.enum(['paper', 'greyBoard', 'sticker', 'accessory', 'giftBoxAccessory']),
});

// 导出类型
export type CreatePaperParams = z.infer<typeof createPaperSchema>;
export type UpdatePaperParams = z.infer<typeof updatePaperSchema>;
export type CreateGreyBoardParams = z.infer<typeof createGreyBoardSchema>;
export type UpdateGreyBoardParams = z.infer<typeof updateGreyBoardSchema>;
export type CreateStickerParams = z.infer<typeof createStickerSchema>;
export type UpdateStickerParams = z.infer<typeof updateStickerSchema>;
export type CreateAccessoryParams = z.infer<typeof createAccessorySchema>;
export type UpdateAccessoryParams = z.infer<typeof updateAccessorySchema>;
export type CreateGiftBoxAccessoryParams = z.infer<typeof createGiftBoxAccessorySchema>;
export type UpdateGiftBoxAccessoryParams = z.infer<typeof updateGiftBoxAccessorySchema>;
export type GetMaterialListParams = z.infer<typeof getMaterialListSchema>;
export type GetMaterialDetailParams = z.infer<typeof getMaterialDetailSchema>;
export type DeleteMaterialParams = z.infer<typeof deleteMaterialSchema>; 