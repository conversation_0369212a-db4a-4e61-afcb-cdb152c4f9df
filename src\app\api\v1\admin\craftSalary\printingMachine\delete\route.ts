import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const deletePrintingMachineSchema = z.object({
  id: z.coerce.number().positive('ID必须是正整数')
});

const handler = withValidation(
  deletePrintingMachineSchema,
  async (request: AuthenticatedRequest, validatedQuery: any) => {
    const data = validatedQuery;

    // 检查印刷机数据是否存在
    const existingMachine = await prisma.printingMachine.findFirst({
      where: {
        id: data.id,
        isDel: false,
      },
    });

    assertExists(existingMachine, ErrorCode.RESOURCE_NOT_FOUND, '印刷机数据不存在');

    // 软删除印刷机数据
    const result = await prisma.printingMachine.update({
      where: { id: data.id },
      data: {
        isDel: true,
      },
    });

    return successResponse(
      { id: result.id },
      '删除印刷机成功'
    );
  }
); 
export const POST = withInternalAuth(handler);