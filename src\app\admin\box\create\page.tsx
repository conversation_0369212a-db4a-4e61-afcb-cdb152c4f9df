'use client';

import React, { useState, useEffect } from 'react';
import {
  Typography, Form, Input, Select, Button, Card, Space, Divider, Breadcrumb, Upload,
  message, Row, Col, Tooltip, Tag, Alert, Modal, InputNumber, Empty
} from 'antd';
import {
  PlusOutlined, DeleteOutlined, SaveOutlined, ArrowLeftOutlined, InfoCircleOutlined,
  FileTextOutlined, BookOutlined, SettingOutlined, FormOutlined,
  EyeOutlined, HolderOutlined, QuestionCircleOutlined, ReloadOutlined
} from '@ant-design/icons';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { arrayMove, SortableContext, horizontalListSortingStrategy, useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import type { RcFile, UploadFile, UploadProps } from 'antd/es/upload/interface';
import { BoxAttribute, BoxCreateParams } from '@/types/box';
import { boxApi } from '@/services/adminApi';
import { useErrorHandler, useAsyncError } from '@/lib/hooks/useErrorHandler';
import { translateChineseToPinyin } from '@/lib/utils/formula';
import { createBoxSchema } from '@/lib/validations/admin/box';
import { handleError } from '@/lib/utils/handler';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

// 默认公式
const DEFAULT_HARDCOVER_BOX_FORMULAS = [
  { name: '面纸长度' },
  { name: '面纸宽度' },
  { name: '灰板纸长度' },
  { name: '灰板纸宽度' },
];

// 属性显示组件
const AttributesList = ({ attributes }: { attributes: BoxAttribute[] }) => {
  if (!attributes || attributes.length === 0) {
    return (
      <Alert
        message="尚未添加属性"
        description="请先在上方「盒型属性」区域添加属性，添加后可在公式中引用"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />
    );
  }

  // 过滤掉无效的属性
  const validAttributes = attributes.filter(attr => attr && attr.name);

  if (validAttributes.length === 0) {
    return (
      <Alert
        message="请完成属性填写"
        description="已添加属性但未填写名称，请完善属性信息后再继续"
        type="warning"
        showIcon
      />
    );
  }

  return (
    <div style={{ marginBottom: 16 }}>
      <Text strong>可用属性：</Text>
      <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8, marginTop: 8 }}>
        {validAttributes.map((attr, index) => (
          <Tag key={index} color="blue">
            {attr.name}{attr.value !== undefined && attr.value !== null ? `（默认值: ${attr.value}）` : ''}
          </Tag>
        ))}
      </div>
    </div>
  );
};

// 可排序的上传项组件
const SortableItem = ({ file, onPreview, onRemove }: {
  file: UploadFile,
  fileList: UploadFile[],
  onPreview: (file: UploadFile) => void,
  onRemove: (file: UploadFile) => void
}) => {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({
    id: file.uid,
    disabled: false
  });

  const [isDragging, setIsDragging] = useState(false);

  const handleDragStart = () => {
    setIsDragging(true);
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  const handlePreviewClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isDragging) {
      onPreview(file);
    }
  };

  const handleRemoveClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isDragging) {
      onRemove(file);
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={{
        transform: CSS.Transform.toString(transform),
        transition,
        width: 100,
        height: 100,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        background: '#fafafa',
        border: '1px solid #f0f0f0',
        borderRadius: 4,
        position: 'relative' as const,
        opacity: isDragging ? 0.6 : 1,
      }}
    >
      {/* 拖动手柄区域 */}
      <div
        {...attributes}
        {...listeners}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '16px',
          cursor: 'move',
          background: 'rgba(0, 0, 0, 0.02)',
          borderBottom: '1px solid #f0f0f0',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1,
          borderTopLeftRadius: 4,
          borderTopRightRadius: 4
        }}
        onMouseDown={handleDragStart}
        onMouseUp={handleDragEnd}
      >
        <HolderOutlined style={{ fontSize: 12, color: '#999' }} />
      </div>

      {/* 图片内容区域 */}
      <div
        className="image-container"
        style={{
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '16px 4px 4px',
          position: 'relative' as const
        }}
      >
        <img
          src={file.thumbUrl}
          alt={file.name}
          style={{
            maxWidth: '100%',
            maxHeight: '100%',
            objectFit: 'contain' as const,
            borderRadius: 2
          }}
        />

        {/* 操作按钮层 */}
        <div className="image-actions">
          <Space size={4}>
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={handlePreviewClick}
              style={{ color: '#fff', padding: '4px' }}
              size="small"
            />
            <Button
              type="text"
              icon={<DeleteOutlined />}
              onClick={handleRemoveClick}
              style={{ color: '#fff', padding: '4px' }}
              size="small"
            />
          </Space>
        </div>
      </div>
    </div>
  );
};

// 获取文件的 base64 编码
const getBase64 = (file: RcFile): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });

// 添加到文件顶部的样式
const styles = {
  uploadButton: `
    .upload-button:hover {
      border-color: #1890ff !important;
    }
  `,
  imageActions: `
    .image-actions {
      position: absolute;
      top: 16px;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.45);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s;
      border-radius: 2px;
    }
    .image-container:hover .image-actions {
      opacity: 1;
    }
  `,
  body: `
    .body {
      padding: isFullscreen ? 0 : undefined;
      height: isFullscreen ? '100vh' : undefined;
    }
  `
};

export default function CreateBoxPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { clearError, errorState } = useErrorHandler();
  const { execute, loading: asyncLoading } = useAsyncError();

  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');
  const [scale, setScale] = useState(1);
  const [isFullscreen] = useState(false);
  // 添加属性列表状态，用于实时更新属性显示
  const [attributesList, setAttributesList] = useState<BoxAttribute[]>([]);

  // DnD 传感器
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor)
  );

  // 监听表单值变化，更新属性列表
  useEffect(() => {
    // 初始加载时获取属性
    const initialAttributes = form.getFieldValue('attributes') || [];
    const formatted = initialAttributes.map((attr: any) => ({
      name: attr?.name || '',
      value: attr?.value !== undefined ? attr.value : null
    })).filter((attr: any) => attr.name);

    setAttributesList(formatted);
  }, [form]);

  // 表单值变化处理函数
  const handleFormValuesChange = (changedValues: any, allValues: any) => {
    // 如果属性值发生变化
    if (changedValues.attributes) {
      const attributes = allValues.attributes || [];
      const formatted = attributes.map((attr: any) => ({
        name: attr?.name || '',
        value: attr?.value !== undefined ? attr.value : null
      })).filter((attr: any) => attr.name);

      setAttributesList(formatted);
    }

    // 公式绑定验证
    if (changedValues.parts) {
      const parts = allValues.parts || [];
      parts.forEach((part: any, partIndex: number) => {
        if (part && part.formulas) {
          const formulas = part.formulas;

          // 检查面纸长度和宽度的绑定
          const faceLength = formulas.find((f: any) => f?.name === '面纸长度');
          const faceWidth = formulas.find((f: any) => f?.name === '面纸宽度');

          if (faceLength && faceWidth) {
            const faceLengthHasValue = faceLength.expression && faceLength.expression.trim();
            const faceWidthHasValue = faceWidth.expression && faceWidth.expression.trim();

            // 如果一个有值另一个没有，清空有值的那个
            if (faceLengthHasValue && !faceWidthHasValue) {
              // 面纸长度有值但宽度没有，需要提示用户
              setTimeout(() => {
                form.setFields([{
                  name: ['parts', partIndex, 'formulas', formulas.findIndex((f: any) => f?.name === '面纸宽度'), 'expression'],
                  errors: ['面纸长度和宽度必须同时填写或同时为空']
                }]);
              }, 0);
            } else if (!faceLengthHasValue && faceWidthHasValue) {
              // 面纸宽度有值但长度没有
              setTimeout(() => {
                form.setFields([{
                  name: ['parts', partIndex, 'formulas', formulas.findIndex((f: any) => f?.name === '面纸长度'), 'expression'],
                  errors: ['面纸长度和宽度必须同时填写或同时为空']
                }]);
              }, 0);
            }
          }

          // 检查灰板纸长度和宽度的绑定
          const greyLength = formulas.find((f: any) => f?.name === '灰板纸长度');
          const greyWidth = formulas.find((f: any) => f?.name === '灰板纸宽度');

          if (greyLength && greyWidth) {
            const greyLengthHasValue = greyLength.expression && greyLength.expression.trim();
            const greyWidthHasValue = greyWidth.expression && greyWidth.expression.trim();

            // 如果一个有值另一个没有，清空有值的那个
            if (greyLengthHasValue && !greyWidthHasValue) {
              setTimeout(() => {
                form.setFields([{
                  name: ['parts', partIndex, 'formulas', formulas.findIndex((f: any) => f?.name === '灰板纸宽度'), 'expression'],
                  errors: ['灰板纸长度和宽度必须同时填写或同时为空']
                }]);
              }, 0);
            } else if (!greyLengthHasValue && greyWidthHasValue) {
              setTimeout(() => {
                form.setFields([{
                  name: ['parts', partIndex, 'formulas', formulas.findIndex((f: any) => f?.name === '灰板纸长度'), 'expression'],
                  errors: ['灰板纸长度和宽度必须同时填写或同时为空']
                }]);
              }, 0);
            }
          }
        }
      });
    }
  };

  // 初始化部件，只在组件挂载时执行一次
  useEffect(() => {
    form.setFieldsValue({
      parts: [{
        name: '盒身',
        formulas: DEFAULT_HARDCOVER_BOX_FORMULAS.map(formula => ({
          name: formula.name,
          expression: '',
        }))
      }]
    });
  }, [form]);

  // 添加部件
  const addPart = (add: Function) => {
    // 检查当前部件数量
    const currentParts = form.getFieldValue('parts') || [];

    if (currentParts.length >= 4) {
      message.warning('最多只能添加4个部件');
      return;
    }

    // 添加新部件
    const newPart = {
      name: `部件${currentParts.length + 1}`,
      formulas: DEFAULT_HARDCOVER_BOX_FORMULAS.map(formula => ({
        name: formula.name,
        expression: '',
      }))
    };

    add(newPart);
  };

  // 表单提交
  const handleSubmit = async (values: any) => {
    // 处理图片数据
    const imageData = await Promise.all(
      fileList.map(async (file, index) => {
        if (file.originFileObj) {
          const base64 = await getBase64(file.originFileObj);
          return {
            name: file.name,
            imageData: base64,
            mimeType: file.type,
            sortOrder: index
          };
        }
        return null;
      })
    );

    // 过滤掉空值
    const filteredImages = imageData.filter(img => img !== null).map((img: any) => ({
      ...img,
      // 只保留base64 部分
      imageData: img.imageData.startsWith('data:') ? img.imageData.split(';base64,')[1] : img.imageData
    }));

    // 构建提交数据
    const submitData: BoxCreateParams = {
      ...values,
      status: parseInt(values.status),
      images: filteredImages,
      attributes: values.attributes.map((attr: any, index: number) => ({
        name: attr.name,
        code: translateChineseToPinyin(attr.name),
        value: attr.value !== undefined && attr.value !== null && attr.value !== '' ? parseFloat(attr.value) : null,
        sortOrder: index // 保持属性顺序
      })),
      processingFee: parseFloat(values.processingFee) || 0,
      processingBasePrice: parseFloat(values.processingBasePrice) || 0,
      packaging: {
        lengthFormula: values.packaging?.lengthFormula || '',
        widthFormula: values.packaging?.widthFormula || '',
        heightFormula: values.packaging?.heightFormula || ''
      }
    };

    const validationResult = createBoxSchema.safeParse(submitData);

    if (!validationResult.success) {
      handleError(validationResult.error);
      return;
    }

    const result = await execute(() => boxApi.create(validationResult.data), '创建盒型');

    if (result) {
      router.push('/admin/box');
    }
  };

  // 处理预览图片
  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as RcFile);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewOpen(true);
    setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1));
  };

  // 处理关闭预览
  const handleCancel = () => {
    setPreviewOpen(false);
    setScale(1); // 重置缩放
  };

  // 处理文件列表变化
  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    // 处理新上传的文件
    Promise.all(
      newFileList.map(async file => {
        // 如果是新上传的文件且没有 thumbUrl
        if (file.originFileObj && !file.thumbUrl) {
          // 生成缩略图
          const thumbUrl = await getBase64(file.originFileObj);
          return {
            ...file,
            thumbUrl,
            status: 'done' as const
          };
        }
        return file;
      })
    ).then(processedFiles => {
      setFileList(prevList => {
        // 获取新上传的文件（不在之前的列表中的文件）
        const newFiles = processedFiles.filter(
          newFile => !prevList.some(prevFile => prevFile.uid === newFile.uid)
        );

        // 合并现有列表和新文件
        return [...prevList, ...newFiles] as UploadFile<any>[];
      });
    });
  };

  // 处理文件上传前的验证
  const beforeUpload = (file: RcFile) => {
    // 验证文件类型
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件！');
      return false;
    }

    // 验证文件大小（限制为 5MB）
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error('图片大小不能超过 5MB！');
      return false;
    }

    // 验证图片数量
    if (fileList.length >= 9) {
      message.error('最多只能上传 9 张图片！');
      return false;
    }

    return false; // 返回 false 以使用自定义上传逻辑
  };

  // 清理预览URL，防止内存泄漏
  useEffect(() => {
    return () => {
      // 组件卸载时清理所有预览URL
      fileList.forEach(file => {
        if (file.thumbUrl && file.thumbUrl.startsWith('blob:')) {
          URL.revokeObjectURL(file.thumbUrl);
        }
      });
    };
  }, [fileList]);

  // 处理拖拽结束
  const handleDragEnd = (event: any) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      setFileList((items) => {
        const oldIndex = items.findIndex(item => item.uid === active.id);
        const newIndex = items.findIndex(item => item.uid === over.id);
        return arrayMove(items, oldIndex, newIndex);
      });
    }
  };

  // 添加样式到页面
  useEffect(() => {
    const styleElement = document.createElement('style');
    styleElement.innerHTML = `${styles.uploadButton}\n${styles.imageActions}`;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  return (
    <div style={{ margin: '0 auto' }}>
      <div style={{ marginBottom: 24 }}>
        <Breadcrumb
          items={[
            { title: <Link href="/admin/box">盒型管理</Link> },
            { title: '新建盒型' },
          ]}
        />

        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: 16 }}>
          <Title level={2} style={{ margin: 0 }}>
            <Space>
              <BookOutlined />新建盒型
            </Space>
          </Title>
          <Link href="/admin/box">
            <Button icon={<ArrowLeftOutlined />}>返回列表</Button>
          </Link>
        </div>
      </div>

      {/* 错误状态显示 */}
      {errorState.hasError && (
        <Alert
          message="操作失败"
          description={errorState.error?.message}
          type="error"
          showIcon
          closable
          onClose={clearError}
          action={
            <Button size="small" icon={<ReloadOutlined />} onClick={() => clearError()}>
              重试
            </Button>
          }
          style={{ marginBottom: 16 }}
        />
      )}

      <Form
        form={form}
        onFinish={handleSubmit}
        onValuesChange={handleFormValuesChange}
        layout="vertical"
        autoComplete="off"
        initialValues={{
          status: "0", // 默认为草稿状态
        }}
      >
        {/* 基本信息 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={24}>
            <Card title={
              <Space>
                <SettingOutlined />
                <span>基本信息</span>
              </Space>
            }>
              <Row gutter={16}>
                <Col span={4}>
                  <Form.Item
                    name="name"
                    label="盒型名称"
                    rules={[{ required: true, message: '请输入盒型名称' }]}
                  >
                    <Input placeholder="请输入盒型名称" />
                  </Form.Item>
                </Col>
                <Col span={2}>
                  <Form.Item
                    name="status"
                    label="状态"
                    rules={[{ required: true, message: '请选择盒型状态' }]}
                  >
                    <Select>
                      <Option value="0">草稿</Option>
                      <Option value="1">发布</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={4}>
                  <Form.Item
                    label="加工费"
                    name="processingFee"
                    rules={[{ required: true, message: '请输入加工费' }, { type: 'number', message: '请输入有效的数字' }]}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      placeholder="请输入加工费"
                      min={0}
                      precision={2}
                      step={0.01}
                    />
                  </Form.Item>
                </Col>
                <Col span={4}>
                  <Form.Item
                    label="加工费起步价"
                    name="processingBasePrice"
                    rules={[{ required: true, message: '请输入加工费起步价' }, { type: 'number', message: '请输入有效的数字' }]}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      placeholder="请输入加工费起步价"
                      min={0}
                      precision={2}
                      step={0.01}
                    />
                  </Form.Item>
                </Col>
                <Col span={10}>
                  <Form.Item
                    name="description"
                    label="盒型描述"
                  >
                    <TextArea
                      placeholder="请输入盒型描述信息"
                      rows={1}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Form.Item
                label={
                  <Space>
                    <span>盒型图片</span>
                    <Tooltip title="可拖动调整图片顺序">
                      <InfoCircleOutlined />
                    </Tooltip>
                  </Space>
                }
                extra="拖拽图片可调整顺序，第一张图片将作为盒型预览图"
              >
                <div className="upload-list-wrapper" style={{
                  display: 'flex',
                  gap: 8,
                  padding: '8px',
                  border: '1px solid #f0f0f0',
                }}>
                  <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragEnd={handleDragEnd}
                  >
                    <SortableContext
                      items={fileList.map(f => f.uid)}
                      strategy={horizontalListSortingStrategy}
                    >
                      {fileList.map(file => (
                        <SortableItem
                          key={file.uid}
                          file={file}
                          fileList={fileList}
                          onPreview={handlePreview}
                          onRemove={(file) => {
                            const newFileList = fileList.filter(item => item.uid !== file.uid);
                            setFileList(newFileList);
                          }}
                        />
                      ))}
                      {fileList.length < 9 && (
                        <Upload
                          listType="picture-card"
                          fileList={[]}
                          beforeUpload={beforeUpload}
                          multiple
                          onChange={handleChange}
                          showUploadList={false}
                          accept="image/*"
                        >
                          <div
                            className="upload-button"
                            style={{
                              display: 'flex',
                              flexDirection: 'column',
                              alignItems: 'center',
                              justifyContent: 'center',
                            }}
                          >
                            <PlusOutlined style={{ fontSize: 16, marginBottom: 4 }} />
                            <div style={{ fontSize: 12 }}>上传图片</div>
                          </div>
                        </Upload>
                      )}
                    </SortableContext>
                  </DndContext>
                </div>
              </Form.Item>

              <Modal
                open={previewOpen}
                title={previewTitle}
                footer={null}
                onCancel={handleCancel}
                width={isFullscreen ? '100%' : 800}
                style={{
                  top: isFullscreen ? 0 : 100,
                  padding: isFullscreen ? 0 : undefined,
                  maxWidth: '100vw'
                }}
                styles={{
                  body: {
                    padding: isFullscreen ? 0 : undefined,
                    height: isFullscreen ? '100vh' : undefined
                  }
                }}
              >
                <div style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: '100%',
                  height: isFullscreen ? 'calc(100vh - 55px)' : '70vh',
                  overflow: 'hidden',
                  backgroundColor: '#f0f0f0'
                }}>
                  <img
                    alt="预览图片"
                    style={{
                      maxWidth: '100%',
                      maxHeight: '100%',
                      objectFit: 'contain',
                      transform: `scale(${scale})`,
                      transition: 'transform 0.3s',
                    }}
                    src={previewImage}
                  />
                </div>
              </Modal>
            </Card>
          </Col>
        </Row>

        {/* 盒型属性 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={24}>
            <Form.List name="attributes">
              {(fields, { add, remove }) => (
                <Card
                  title={
                    <Space>
                      <SettingOutlined />
                      <span>盒型属性</span>
                      <Tooltip title="添加用于计算的参数，例如：长、宽、高、糊口、头部、底部">
                        <QuestionCircleOutlined style={{ color: '#999' }} />
                      </Tooltip>
                      <Text type="secondary" style={{ fontSize: 14 }}>
                        这些属性可在公式中引用
                      </Text>
                    </Space>
                  }
                  extra={
                    <Button
                      type="primary"
                      ghost
                      icon={<PlusOutlined />}
                      onClick={() => add()}
                      disabled={fields.length >= 8}
                    >
                      添加属性
                    </Button>
                  }
                >
                  {/* 属性列表 */}
                  {fields.length === 0 ? (
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description="暂无属性，请添加属性"
                    >
                    </Empty>
                  ) : (
                    <Row gutter={[16, 16]}>
                      {fields.map(({ key, name, ...restField }) => (
                        <Col key={key} span={6}>
                          <Card
                            size="small"
                            title={`属性 #${name + 1}`}
                            extra={
                              <Button
                                type="text"
                                danger
                                icon={<DeleteOutlined />}
                                onClick={() => remove(name)}
                              >
                                删除
                              </Button>
                            }
                          >
                            <Form.Item
                              {...restField}
                              name={[name, 'name']}
                              style={{ marginBottom: 12 }}
                              rules={[{ required: true, message: '请输入属性名称' }]}
                            >
                              <Input placeholder="属性名称" />
                            </Form.Item>
                            <Form.Item
                              {...restField}
                              name={[name, 'value']}
                              style={{ marginBottom: 0 }}
                            >
                              <Input placeholder="默认值（可选）" type="number" />
                            </Form.Item>
                          </Card>
                        </Col>
                      ))}
                    </Row>
                  )}
                </Card>
              )}
            </Form.List>
          </Col>
        </Row>

        {/* 打包信息 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={24}>
            <Card
              title={
                <Space>
                  <FormOutlined />
                  <span>打包信息</span>
                </Space>
              }
            >
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    label="长度计算公式"
                    name={['packaging', 'lengthFormula']}
                    rules={[{ required: true, message: '请输入长度计算公式' }]}
                  >
                    <Input
                      placeholder="请输入长度计算公式"
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="宽度计算公式"
                    name={['packaging', 'widthFormula']}
                    rules={[{ required: true, message: '请输入宽度计算公式' }]}
                  >
                    <Input
                      placeholder="请输入宽度计算公式"
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="高度计算公式"
                    name={['packaging', 'heightFormula']}
                    rules={[{ required: false, message: '请输入高度计算公式' }]}
                  >
                    <Input
                      placeholder="请输入高度计算公式"
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>

        {/* 部件和公式 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={24}>
            <Form.List name="parts">
              {(fields, { add, remove }) => (
                <Card
                  title={
                    <Space>
                      <FormOutlined />
                      <span>部件和公式</span>
                      <Text type="secondary" style={{ fontSize: 14 }}>
                        可以添加最多4个部件
                      </Text>
                    </Space>
                  }
                  extra={
                    <Button
                      type="primary"
                      ghost
                      onClick={() => addPart(add)}
                      icon={<PlusOutlined />}
                      disabled={fields.length >= 4}
                    >
                      添加部件
                    </Button>
                  }
                >
                  {/* 部件列表 */}
                  <Row gutter={[16, 16]}>
                    {fields.map(({ key, name, ...restField }) => {
                      const formattedAttributes = attributesList;
                      return (
                        <Col key={key} span={12}>
                          <Card
                            title={form.getFieldValue(['parts', name, 'name']) || `部件 ${name + 1}`}
                            extra={
                              fields.length > 1 ? (
                                <Button
                                  type="text"
                                  danger
                                  icon={<DeleteOutlined />}
                                  onClick={() => remove(name)}
                                >
                                  删除部件
                                </Button>
                              ) : null
                            }
                          >
                            <Form.Item
                              {...restField}
                              name={[name, 'name']}
                              label="部件名称"
                              rules={[{ required: true, message: '请输入部件名称' }]}
                            >
                              <Input placeholder="部件名称" />
                            </Form.Item>

                            <Divider orientation="left" >部件公式</Divider>
                            <AttributesList attributes={formattedAttributes} />

                            <Form.List name={[name, 'formulas']}>
                              {(formulaFields) => (
                                <>
                                  {formulaFields.map(({ key: formulaKey, name: formulaName, ...formulaRestField }) => {
                                    const currentFormula = form.getFieldValue(['parts', name, 'formulas', formulaName]);

                                    return (
                                      <Card
                                        key={formulaKey}
                                        size="small"
                                        style={{ marginBottom: 8 }}
                                      >
                                        <Row gutter={16} align="middle">
                                          <Col span={4}>
                                            <Form.Item
                                              {...formulaRestField}
                                              name={[formulaName, 'name']}
                                              style={{ margin: 0 }}
                                              rules={[{ required: true, message: '请输入公式名称' }]}
                                            >
                                              <Input
                                                placeholder="公式名称"
                                                disabled={true}
                                              />
                                            </Form.Item>
                                          </Col>
                                          <Col span={20}>
                                            <Form.Item
                                              {...formulaRestField}
                                              name={[formulaName, 'expression']}
                                              style={{ margin: 0 }}
                                              rules={[{ required: true, message: '请输入公式表达式' }]}
                                            >
                                              <Input placeholder="公式表达式，示例: length * width + 10" />
                                            </Form.Item>
                                          </Col>
                                        </Row>
                                      </Card>
                                    );
                                  })}
                                  {/* 移除添加公式功能，只允许默认公式 */}
                                </>
                              )}
                            </Form.List>
                          </Card>
                        </Col>
                      );
                    })}
                  </Row>
                </Card>
              )}
            </Form.List>
          </Col>
        </Row>

        {/* 提交按钮 */}
        <div style={{ display: 'flex', justifyContent: 'center', marginBottom: 48 }}>
          <Space size="large">
            <Link href="/admin/box">
              <Button size="large">取消</Button>
            </Link>
            <Button
              type="primary"
              size="large"
              icon={<SaveOutlined />}
              htmlType="submit"
              loading={asyncLoading}
            >
              保存盒型
            </Button>
          </Space>
        </div>
      </Form>
    </div>
  );
} 