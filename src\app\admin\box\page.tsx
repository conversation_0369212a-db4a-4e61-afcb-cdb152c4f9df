'use client';

import dayjs from 'dayjs';
import Link from 'next/link';
import React, { useState, useEffect } from 'react';
import { Typography, Button, Input, Select, Table, Space, Card, Tabs, Segmented, Avatar, Badge, Tag, Popconfirm, message, DatePicker, Row, Col } from 'antd';
import { SearchOutlined, AppstoreOutlined, BarsOutlined, FileTextOutlined, BookOutlined, EditOutlined, DeleteOutlined, CalculatorOutlined } from '@ant-design/icons';
import { Box, BoxListParams, BoxStatus } from '@/types/box';
import { boxApi } from '@/services/adminApi';
import { useErrorHandler, useAsyncError } from '@/lib/hooks/useErrorHandler';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

export default function BoxManagementPage() {
  const [activeKey, setActiveKey] = useState<string>('all');
  const [viewMode, setViewMode] = useState<string | number>('list');
  const [searchText, setSearchText] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [boxList, setBoxList] = useState<Box[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  // 使用新的错误处理Hook
  const { errorState, clearError } = useErrorHandler();
  const { execute, loading: asyncLoading } = useAsyncError();

  // 获取盒型列表 - 使用新的错误处理机制
  const fetchBoxList = async (params: BoxListParams) => {
    const result = await execute(async () => {
      const { current, pageSize } = pagination;

      // 构建请求参数，只包含有效值
      const requestParams: BoxListParams = {
        page: current,
        pageSize,
        ...params,
      };

      return await boxApi.getList(requestParams);
    }, '获取盒型列表');

    if (result) {
      setBoxList(result.list || []);
      setPagination(prev => ({
        ...prev,
        total: result.pagination?.total || 0
      }));
    } else {
      setBoxList([]);
      setPagination(prev => ({ ...prev, total: 0 }));
    }
  };

  // 首次加载和筛选条件变化时获取数据
  useEffect(() => {
    // 切换到新的标签页时重置分页到第一页
    setPagination(prev => ({
      ...prev,
      current: 1
    }));
    fetchBoxList({ page: 1 })
  }, [activeKey]); // 标签页切换时自动查询

  // 重置筛选条件
  const handleReset = () => {
    setSearchText('');
    setStatusFilter('');
    setPagination(prev => ({
      ...prev,
      current: 1
    }));
    // 清除错误状态
    clearError();
    // 重置后立即查询，保留当前选中的标签页筛选
    fetchBoxList({ page: 1 });
  };

  // 处理分页变化
  const handleTableChange = (pagination: any) => {
    setPagination(prev => ({
      ...prev,
      current: pagination.current,
      pageSize: pagination.pageSize
    }));
    fetchBoxList({
      page: pagination.current,
      pageSize: pagination.pageSize
    });
  };

  // 处理筛选
  const handleSearch = () => {
    setPagination(prev => ({
      ...prev,
      current: 1
    }));

    // 构建搜索参数，只包含有效值
    const searchParams: any = {
      page: 1,
    };

    if (searchText) {
      searchParams.name = searchText;
    }

    if (statusFilter) {
      searchParams.status = parseInt(statusFilter);
    }

    // 清除之前的错误
    clearError();
    fetchBoxList(searchParams);
  };

  // 处理新建按钮点击
  const handleCreate = () => {
    // 根据盒型类型跳转到对应的创建页面
    window.location.href = `/admin/box/create`;
  };

  // 处理标签页切换
  const handleTabChange = (key: string) => {
    setActiveKey(key);
    // 清除错误状态
    clearError();
  };

  // 处理删除盒型 - 使用新的错误处理机制
  const handleDelete = async (id: number) => {
    const result = await execute(async () => {
      return await boxApi.delete(id);
    }, '删除盒型');

    if (result) {
      fetchBoxList({ page: 1 }); // 重新加载列表
    }
    // 错误已经由execute函数处理，不需要额外处理
  };

  // 表格列定义
  const columns = [
    {
      title: '盒型预览',
      key: 'thumbnail',
      width: 100,
      align: 'center' as const,
      render: (_: any, record: Box) => {
        // 获取第一张图片作为缩略图
        const hasImage = record._count?.images && record._count.images > 0 && record.images && record.images.length > 0;

        if (hasImage) {
          // 使用第一张图片
          const imageUrl = boxApi.getImageUrl(record.images![0].id || 0);
          return (
            <Avatar
              shape="square"
              size={64}
              src={imageUrl}
              alt={record.name}
              onError={() => {
                console.error(`图片加载失败: ${imageUrl}`);
                return true; // 显示fallback
              }}
            />
          );
        }

        // 没有图片时显示默认图标
        return (
          <Avatar shape="square" size={64} style={{ backgroundColor: '#f0f0f0' }}>
            <AppstoreOutlined />
          </Avatar>
        );
      },
    },
    {
      title: '盒型名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      align: 'center' as const,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      align: 'center' as const,
      render: (status: number) => (
        <Badge
          status={status === BoxStatus.PUBLISHED ? 'success' : 'default'}
          text={status === BoxStatus.PUBLISHED ? '已发布' : '草稿'}
        />
      ),
    },
    {
      title: '组件数量',
      key: 'components',
      width: 250,
      align: 'center' as const,
      render: (_: any, record: Box) => (
        <Space>
          <Tag color="blue">属性: {record._count?.attributes || 0}</Tag>
          <Tag color="green">部件: {record._count?.parts || 0}</Tag>
          <Tag color="purple">图片: {record._count?.images || 0}</Tag>
        </Space>
      ),
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 170,
      align: 'center' as const,
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      key: 'action',
      width: 220,
      align: 'center' as const,
      render: (_: any, record: Box) => (
        <Space size="small">
          <Link href={`/admin/box/detail?id=${record.id}`}>
            <Button type="primary" size="small" icon={<SearchOutlined />}>查看</Button>
          </Link>
          <Link href={`/admin/box/edit?id=${record.id}`}>
            <Button type="primary" size="small" icon={<EditOutlined />}>编辑</Button>
          </Link>
          <Link href={`/admin/box/calculation?boxId=${record.id}`}>
            <Button type="primary" size="small" icon={<CalculatorOutlined />} style={{ background: '#52c41a', borderColor: '#52c41a' }}>计算</Button>
          </Link>
          <Popconfirm
            title="确定要删除这个盒型吗?"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button danger size="small" icon={<DeleteOutlined />}>删除</Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 卡片视图
  const renderCardView = () => {
    return (
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))', gap: '16px', textAlign: 'center' }}>
        {boxList.map(item => {
          // 获取第一张图片作为缩略图
          const hasImage = item._count?.images && item._count.images > 0 && item.images && item.images.length > 0;
          let imageUrl = '';

          if (hasImage && item.images) {
            imageUrl = boxApi.getImageUrl(item.images[0].id || 0);
          }

          return (
            <Card
              key={item.id}
              hoverable
              cover={
                <div style={{
                  height: 180,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: '#f5f5f5'
                }}>
                  {imageUrl ? (
                    <img
                      alt={item.name}
                      src={imageUrl}
                      style={{ maxHeight: '100%', maxWidth: '100%', objectFit: 'contain' }}
                    />
                  ) : (
                    <AppstoreOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
                  )}
                </div>
              }
              actions={[
                <Link key="view" href={`/admin/box/detail?id=${item.id}`}>
                  <Button type="link" icon={<SearchOutlined />}>查看</Button>
                </Link>,
                <Link key="edit" href={`/admin/box/edit?id=${item.id}`}>
                  <Button type="link" icon={<EditOutlined />}>编辑</Button>
                </Link>,
                <Link key="calculate" href={`/admin/box/calculation?boxId=${item.id}`}>
                  <Button type="link" icon={<CalculatorOutlined />} style={{ color: '#52c41a' }}>计算</Button>
                </Link>,
                <Popconfirm
                  key="delete"
                  title="确定要删除这个盒型吗?"
                  onConfirm={() => handleDelete(item.id)}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button type="link" danger icon={<DeleteOutlined />}>删除</Button>
                </Popconfirm>
              ]}
            >
              <Card.Meta
                title={
                  <Space direction="vertical" size={4}>
                    <Text strong>{item.name}</Text>
                    <Space>
                      <Badge
                        status={item.status === BoxStatus.PUBLISHED ? 'success' : 'default'}
                        text={item.status === BoxStatus.PUBLISHED ? '已发布' : '草稿'}
                      />
                    </Space>
                  </Space>
                }
                description={
                  <Space direction="vertical" size={8} style={{ width: '100%', marginTop: 8 }}>
                    <div>
                      <Text type="secondary">加工费：</Text>
                      <Text>{item.processingFee !== null ? `¥${item.processingFee}` : '未设置'}</Text>
                    </div>
                    <div>
                      <Text type="secondary">加工费起步价：</Text>
                      <Text>{item.processingBasePrice !== null ? `¥${item.processingBasePrice}` : '未设置'}</Text>
                    </div>
                    <Space>
                      <Tag color="blue">属性: {item._count?.attributes || 0}</Tag>
                      <Tag color="green">部件: {item._count?.parts || 0}</Tag>
                      <Tag color="purple">图片: {item._count?.images || 0}</Tag>
                    </Space>
                  </Space>
                }
              />
            </Card>
          );
        })}
      </div>
    );
  };

  return (
    <div>
      <Title level={2}>盒型管理</Title>

      {/* 搜索和筛选 */}
      <Card>
        <Row gutter={16} align="middle" style={{ marginBottom: 16 }}>
          <Col span={4}>
            <Input
              placeholder="搜索盒型名称..."
              prefix={<SearchOutlined />}
              allowClear
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
            />
          </Col>
          <Col>
            <Select
              value={statusFilter}
              onChange={setStatusFilter}
              placeholder="状态筛选"
              allowClear
            >
              <Option value="">所有状态</Option>
              <Option value="1">已发布</Option>
              <Option value="0">草稿</Option>
            </Select>
          </Col>
          <Col>
            <Button onClick={handleReset}>
              重置
            </Button>
          </Col>
          <Col>
            <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
              筛选
            </Button>
          </Col>

          {/* 视图切换 */}
          <Col flex="auto" style={{ textAlign: 'right' }}>
            <Segmented
              style={{ marginRight: 16 }}
              value={viewMode}
              onChange={setViewMode}
              options={[
                { value: 'list', icon: <BarsOutlined />, label: '列表视图' },
                { value: 'card', icon: <AppstoreOutlined />, label: '卡片视图' }
              ]}
            />

            <Button
              type="primary"
              icon={<FileTextOutlined />}
              onClick={() => handleCreate()}
            >
              新建盒型
            </Button>
          </Col>
        </Row>


        {/* 错误状态显示 */}
        {errorState.hasError && (
          <Card style={{ marginBottom: 16, border: '1px solid #ff4d4f' }}>
            <div style={{ color: '#ff4d4f' }}>
              <Text type="danger">
                错误：{errorState.error?.message}
              </Text>
              {errorState.canRetry && (
                <Button
                  type="link"
                  size="small"
                  onClick={clearError}
                  style={{ marginLeft: 8 }}
                >
                  关闭
                </Button>
              )}
            </div>
          </Card>
        )}

        {/* 盒型列表 */}
        {viewMode === 'list' ? (
          <Table
            columns={columns}
            dataSource={boxList}
            rowKey="id"
            loading={asyncLoading}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showTotal: (total) => `共 ${total} 条`,
            }}
            onChange={handleTableChange}
            locale={{ emptyText: '暂无数据' }}
            scroll={{ x: 'max-content' }}
            bordered
            size="middle"
          />
        ) : (
          <div style={{ position: 'relative' }}>
            {asyncLoading && (
              <div style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: 'rgba(255, 255, 255, 0.8)',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                zIndex: 1000,
                minHeight: '200px'
              }}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '14px', color: '#999', marginTop: '8px' }}>
                    加载中...
                  </div>
                </div>
              </div>
            )}
            {renderCardView()}

          </div>
        )}
      </Card>
    </div>
  );
}