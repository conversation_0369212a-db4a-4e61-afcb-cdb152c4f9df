import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { updateStickerSchema, UpdateStickerParams } from '@/lib/validations/admin/sticker';
import { withValidation, assertExists, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation<UpdateStickerParams>(
  updateStickerSchema,
  async (request: NextRequest, validatedData: UpdateStickerParams) => {
    // 检查不干胶是否存在
    const existingSticker = await prisma.sticker.findUnique({
      where: {
        id: validatedData.id,
        isDel: false,
      },
    });

    assertExists(existingSticker, ErrorCode.MATERIAL_NOT_FOUND, '不干胶不存在');

    // 检查名称是否与其他不干胶重复
    const duplicateName = await prisma.sticker.findFirst({
      where: {
        name: validatedData.name,
        id: { not: validatedData.id },
        isDel: false,
      },
    });

    assert(!duplicateName, ErrorCode.MATERIAL_NAME_EXISTS, '不干胶名称已存在');

    // 更新不干胶
    const sticker = await prisma.sticker.update({
      where: {
        id: validatedData.id,
      },
      data: {
        name: validatedData.name,
        price: validatedData.price,
        unit: validatedData.unit,
        weight: validatedData.weight,
        category: validatedData.category,
        remark: validatedData.remark || null,
        updatedAt: new Date(),
      },
    });

    return successResponse(sticker, '更新不干胶成功');
  }
); 