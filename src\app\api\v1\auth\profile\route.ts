import { prisma } from '@/lib/prisma';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';
import { withAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { UserRole, CurrentUser } from '@/types/user';

export const POST = withAuth(
  async (request: AuthenticatedRequest) => {
    try {
      const user = request.user;

      if (!user) {
        return errorResponse(ErrorCode.UNAUTHORIZED, '未授权访问', null, 401)
      }
      // 从数据库获取最新的用户信息
      const dbUser = await prisma.user.findUnique({
        where: {
          id: user.userId,
          isDel: false
        },
        select: {
          id: true,
          name: true,
          phone: true,
          email: true,
          role: true,
          expiresAt: true,
          state: true,
          lastLoginAt: true,
          createdAt: true,
          updatedAt: true
        }
      });

      if (!dbUser) {
        return errorResponse(ErrorCode.USER_NOT_FOUND, '用户不存在', null, 404)
      }

      // 检查用户状态
      if (dbUser.state === 0) {
        return errorResponse(ErrorCode.USER_DISABLED, '账户已被禁用', null, 403)
      }

      // 检查超级用户是否过期
      if (dbUser.role === UserRole.SUPER_USER && dbUser.expiresAt) {
        if (new Date() > dbUser.expiresAt) {
          return errorResponse(ErrorCode.USER_EXPIRED, '账户已过期', null, 403)
        }
      }

      // 准备返回的用户信息
      const currentUser: CurrentUser = {
        id: dbUser.id,
        name: dbUser.name,
        phone: dbUser.phone,
        email: dbUser.email,
        role: dbUser.role as UserRole,
        expiresAt: dbUser.expiresAt
      };

      return successResponse(currentUser, '获取用户信息成功');
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return errorResponse(ErrorCode.INTERNAL_ERROR, '获取用户信息失败，请稍后重试')
    }
  }
);
