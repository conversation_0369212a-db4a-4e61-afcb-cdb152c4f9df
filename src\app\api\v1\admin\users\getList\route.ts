import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { queryUserSchema, QueryUserParams } from '@/lib/validations/user';
import { withValidation } from '@/lib/middleware/errorHandler';
import { paginatedResponse } from '@/lib/utils/apiResponse';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { Prisma } from '@prisma/client';
import { UserRole, UserState } from '@/types/user';

const handler = withValidation<QueryUserParams>(
  queryUserSchema,
  async (request: AuthenticatedRequest, validatedQuery: QueryUserParams) => {
    const data = validatedQuery;
    const skip = (data.page - 1) * data.pageSize;

    // 构建查询条件
    const where: Prisma.UserWhereInput = {
      isDel: false
    };

    // 关键词搜索（姓名、手机号、邮箱）
    if (data.keyword) {
      where.OR = [
        { name: { contains: data.keyword } },
        { phone: { contains: data.keyword } },
        { email: { contains: data.keyword } }
      ];
    }

    // 角色筛选
    if (data.role) {
      where.role = data.role;
    }

    // 状态筛选
    if (data.state !== undefined) {
      where.state = data.state;
    }

    // 手机号筛选
    if (data.phone) {
      where.phone = { contains: data.phone };
    }

    // 邮箱筛选
    if (data.email) {
      where.email = { contains: data.email };
    }

    // 姓名筛选
    if (data.name) {
      where.name = { contains: data.name };
    }

    // 时间范围筛选
    if (data.startTime || data.endTime) {
      where.createdAt = {};

      if (data.startTime) {
        where.createdAt.gte = new Date(data.startTime);
      }

      if (data.endTime) {
        where.createdAt.lte = new Date(data.endTime);
      }
    }

    try {
      // 查询总数和列表数据
      const [total, list] = await Promise.all([
        prisma.user.count({ where }),
        prisma.user.findMany({
          where,
          select: {
            id: true,
            name: true,
            phone: true,
            email: true,
            role: true,
            expiresAt: true,
            lastLoginAt: true,
            lastLoginIp: true,
            state: true,
            createdAt: true,
            updatedAt: true
          },
          skip,
          take: data.pageSize,
          orderBy: { createdAt: 'desc' }
        })
      ]);

      return paginatedResponse(
        list,
        {
          page: data.page,
          pageSize: data.pageSize,
          total
        },
        '获取用户列表成功'
      );

    } catch (error) {
      console.error('获取用户列表失败:', error);
      return NextResponse.json(
        { code: 500, message: '获取用户列表失败，请稍后重试', data: null },
        { status: 500 }
      );
    }
  }
);

export const POST = withInternalAuth(handler);
