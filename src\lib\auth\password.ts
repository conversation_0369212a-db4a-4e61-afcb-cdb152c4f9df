import bcrypt from 'bcrypt';

// 盐轮数，推荐值为10-12
const SALT_ROUNDS = 12;

/**
 * 哈希密码
 * @param password 明文密码
 * @returns Promise<string> 哈希后的密码
 */
export async function hashPassword(password: string): Promise<string> {
  try {
    const hashedPassword = await bcrypt.hash(password, SALT_ROUNDS);
    return hashedPassword;
  } catch (error) {
    console.error('密码哈希失败:', error);
    throw new Error('密码加密失败');
  }
}

/**
 * 验证密码
 * @param password 明文密码
 * @param hashedPassword 哈希后的密码
 * @returns Promise<boolean> 密码是否匹配
 */
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  try {
    const isMatch = await bcrypt.compare(password, hashedPassword);
    return isMatch;
  } catch (error) {
    console.error('密码验证失败:', error);
    return false;
  }
}

/**
 * 生成随机密码
 * @param length 密码长度，默认12位
 * @returns string 随机密码
 */
export function generateRandomPassword(length: number = 12): string {
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  const symbols = '@$!%*#?&';
  
  const allChars = lowercase + uppercase + numbers + symbols;
  
  let password = '';
  
  // 确保密码包含至少一个小写字母、大写字母、数字和符号
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  password += symbols[Math.floor(Math.random() * symbols.length)];
  
  // 填充剩余长度
  for (let i = 4; i < length; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)];
  }
  
  // 打乱密码字符顺序
  return password.split('').sort(() => Math.random() - 0.5).join('');
}

/**
 * 验证密码强度
 * @param password 密码
 * @returns object 密码强度信息
 */
export function validatePasswordStrength(password: string): {
  isValid: boolean;
  score: number;
  feedback: string[];
} {
  const feedback: string[] = [];
  let score = 0;
  
  // 长度检查
  if (password.length < 8) {
    feedback.push('密码长度至少8位');
  } else if (password.length >= 8) {
    score += 1;
  }
  
  if (password.length >= 12) {
    score += 1;
  }
  
  // 包含小写字母
  if (/[a-z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('密码应包含小写字母');
  }
  
  // 包含大写字母
  if (/[A-Z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('密码应包含大写字母');
  }
  
  // 包含数字
  if (/\d/.test(password)) {
    score += 1;
  } else {
    feedback.push('密码应包含数字');
  }
  
  // 包含特殊字符
  if (/[@$!%*#?&]/.test(password)) {
    score += 1;
  } else {
    feedback.push('密码应包含特殊字符(@$!%*#?&)');
  }
  
  // 不包含常见弱密码模式
  const weakPatterns = [
    /123456/,
    /password/i,
    /qwerty/i,
    /abc123/i,
    /admin/i,
    /^(.)\1+$/, // 重复字符
  ];
  
  for (const pattern of weakPatterns) {
    if (pattern.test(password)) {
      feedback.push('密码不能包含常见的弱密码模式');
      score = Math.max(0, score - 2);
      break;
    }
  }
  
  const isValid = score >= 4 && feedback.length === 0;
  
  return {
    isValid,
    score,
    feedback
  };
}

/**
 * 获取密码强度等级
 * @param score 密码强度分数
 * @returns string 强度等级
 */
export function getPasswordStrengthLevel(score: number): string {
  if (score <= 2) return '弱';
  if (score <= 4) return '中等';
  if (score <= 5) return '强';
  return '很强';
}

/**
 * 获取密码强度颜色
 * @param score 密码强度分数
 * @returns string 颜色值
 */
export function getPasswordStrengthColor(score: number): string {
  if (score <= 2) return '#ff4d4f'; // 红色
  if (score <= 4) return '#faad14'; // 橙色
  if (score <= 5) return '#52c41a'; // 绿色
  return '#1890ff'; // 蓝色
}
