import { NextRequest } from 'next/server';
import { updatePaperSchema, UpdatePaperParams } from '@/lib/validations/admin/paper';
import { prisma } from '@/lib/prisma';
import { withValidation, assert, assertExists } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation<UpdatePaperParams>(
  updatePaperSchema,
  async (request: AuthenticatedRequest, validatedData: UpdatePaperParams) => {
    // 检查纸张是否存在
    const existingPaper = await prisma.paper.findFirst({
      where: { 
        id: validatedData.id,
        isDel: false 
      }
    });

    assertExists(
      existingPaper,
      ErrorCode.RESOURCE_NOT_FOUND,
      '纸张材料不存在'
    );

    // 检查名称是否与其他纸张冲突
    const duplicatePaper = await prisma.paper.findFirst({
      where: { 
        name: validatedData.name,
        isDel: false,
        NOT: { id: validatedData.id }
      }
    });
    
    assert(
      !duplicatePaper,
      ErrorCode.DUPLICATE_ENTRY,
      '纸张材料名称已存在'
    );

    // 更新纸张材料
    const paper = await prisma.paper.update({
      where: { id: validatedData.id },
      data: {
        name: validatedData.name,
        price: validatedData.price,
        unit: validatedData.unit,
        weight: validatedData.weight,
        thickness: validatedData.thickness,
        regularPrice: validatedData.regularPrice || null,
        largePrice: validatedData.largePrice || null,
        category: validatedData.category,
        remark: validatedData.remark || null,
      },
    });

    return successResponse(
      paper,
      '更新纸张材料成功'
    );
  }
); 
export const POST = withInternalAuth(handler);