import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';
import { z } from 'zod';

const deleteSchema = z.object({
  id: z.number().positive('ID必须是正整数'),
});

const handler = withValidation(
  deleteSchema,
  async (request: AuthenticatedRequest, validatedData: any) => {
    const { id } = validatedData;

    // 检查压纹工艺是否存在
    const existingTexturingProcess = await prisma.texturingProcess.findFirst({
      where: {
        id,
        isDel: false,
      },
    });

    assert(!!existingTexturingProcess, ErrorCode.NOT_FOUND, '压纹工艺不存在');

    // 软删除压纹工艺
    await prisma.texturingProcess.update({
      where: { id },
      data: { isDel: true },
    });

    return successResponse(
      { id },
      '删除压纹工艺成功'
    );
  }
); 
export const POST = withInternalAuth(handler);