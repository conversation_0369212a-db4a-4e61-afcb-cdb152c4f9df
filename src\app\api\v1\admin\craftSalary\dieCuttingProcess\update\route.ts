import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { UpdateDieCuttingProcessData, updateDieCuttingProcessSchema } from '@/lib/validations/admin/dieCuttingProcess';
import { withValidation, assert, assertExists } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation(
  updateDieCuttingProcessSchema,
  async (request: NextRequest, validatedData: UpdateDieCuttingProcessData) => {
    const data = validatedData;

    // 检查模切工艺是否存在
    const existingProcess = await prisma.dieCuttingProcess.findFirst({
      where: {
        id: data.id,
        isDel: false,
      },
    });

    assertExists(existingProcess, ErrorCode.RESOURCE_NOT_FOUND, '模切工艺不存在');

    // 检查名称是否与其他记录重复
    const duplicateProcess = await prisma.dieCuttingProcess.findFirst({
      where: {
        name: data.name,
        id: { not: data.id },
        isDel: false,
      },
    });

    assert(!duplicateProcess, ErrorCode.DUPLICATE_ENTRY, '模切工艺名称已存在');

    // 更新模切工艺数据
    const updatedProcess = await prisma.dieCuttingProcess.update({
      where: { id: data.id },
      data: {
        name: data.name,
        price: data.price,
        unit: data.unit,
        basePrice: data.basePrice,
        remark: data.remark,
      },
    });

    return successResponse(updatedProcess, '更新模切工艺成功');
  }
);
