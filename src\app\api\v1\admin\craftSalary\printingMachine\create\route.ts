import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { CreatePrintingMachineData, createPrintingMachineSchema } from '@/lib/validations/admin/printing';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation(
  createPrintingMachineSchema,
  async (request: AuthenticatedRequest, validatedData: CreatePrintingMachineData) => {
    const data = validatedData;

    // 检查印刷机名称是否重复
    const existingMachine = await prisma.printingMachine.findFirst({
      where: {
        machineName: data.machineName,
        isDel: false,
      },
    });

    assert(!existingMachine, ErrorCode.DUPLICATE_ENTRY, '印刷机名称已存在');

    // 创建印刷机数据
    const printingMachine = await prisma.printingMachine.create({
      data: {
        machineName: data.machineName,
        maxLength: data.maxLength,
        maxWidth: data.maxWidth,
        remark: data.remark,
      },
    });

    return successResponse(printingMachine, '创建印刷机成功');
  }
); 
export const POST = withInternalAuth(handler);