import { prisma } from '@/lib/prisma';
import { changePasswordSchema, ChangePasswordParams } from '@/lib/validations/user';
import { withValidation } from '@/lib/middleware/errorHandler';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';
import { withAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { verifyPassword, hashPassword } from '@/lib/auth/password';

const handler = withValidation<ChangePasswordParams>(
  changePasswordSchema,
  async (request: AuthenticatedRequest, validatedData: ChangePasswordParams) => {
    const { oldPassword, newPassword } = validatedData;
    const user = request.user;

    if (!user) {
      return errorResponse(ErrorCode.UNAUTHORIZED, '未授权访问', null, 401)
    }

    try {
      // 获取用户当前密码
      const dbUser = await prisma.user.findUnique({
        where: {
          id: user.userId,
          isDel: false
        },
        select: {
          id: true,
          password: true,
          state: true
        }
      });

      if (!dbUser) {
        return errorResponse(ErrorCode.USER_NOT_FOUND, '用户不存在', null, 404)
      }

      // 检查用户状态
      if (dbUser.state === 0) {
        return errorResponse(ErrorCode.USER_DISABLED, '账户已被禁用', null, 403)
      }

      // 验证原密码
      const isOldPasswordValid = await verifyPassword(oldPassword, dbUser.password);
      if (!isOldPasswordValid) {
        return errorResponse(ErrorCode.INVALID_CREDENTIALS, '原密码错误', null, 400)
      }

      // 哈希新密码
      const hashedNewPassword = await hashPassword(newPassword);

      // 更新密码
      await prisma.user.update({
        where: { id: user.userId },
        data: {
          password: hashedNewPassword,
          // 清除当前登录Token，强制重新登录
          currentLoginToken: null
        }
      });

      return successResponse(null, '密码修改成功，请重新登录');

    } catch (error) {
      console.error('修改密码失败:', error);
      return errorResponse(ErrorCode.INTERNAL_ERROR, '修改密码失败，请稍后重试')
    }
  }
);

export const POST = withAuth(handler);
