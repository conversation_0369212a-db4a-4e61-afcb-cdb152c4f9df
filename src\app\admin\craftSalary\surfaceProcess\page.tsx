'use client';

import React, { useState, useEffect } from 'react';
import {
  Table, Button, Space, Modal, Form, Input,
  Select, InputNumber, Popconfirm, Card, Typography,
  message, Row, Col, Tag
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined,
  SearchOutlined, ReloadOutlined
} from '@ant-design/icons';
import { surfaceProcessApi } from '@/services/adminApi';
import { SurfaceProcess, SURFACE_PROCESS_UNITS, SurfaceProcessUnit } from '@/types/craftSalary';
import { useAsyncError } from '@/lib/hooks/useErrorHandler';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

/**
 * 覆膜工艺管理页面
 */
export default function SurfaceProcessManagementPage() {
  // 错误处理Hook
  const { execute: executeSurfaceProcess, loading: surfaceProcessLoading } = useAsyncError();

  // 数据相关状态
  const [surfaceProcessList, setSurfaceProcessList] = useState<SurfaceProcess[]>([]);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [keyword, setKeyword] = useState('');
  const [unit, setUnit] = useState<SurfaceProcessUnit | ''>('');

  // 模态框相关状态
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [form] = Form.useForm();

  // 初始加载数据
  useEffect(() => {
    fetchSurfaceProcessList();
  }, []);

  // 获取覆膜工艺数据列表
  const fetchSurfaceProcessList = async (page = current, pageSize_ = pageSize, search = keyword, unit_ = unit) => {
    const requestParams: any = {
      page,
      pageSize: pageSize_,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    };
    
    if (search) {
      requestParams.search = search;
    }
    
    if (unit_) {
      requestParams.unit = unit_;
    }
    
    const result = await executeSurfaceProcess(async () => {
      return await surfaceProcessApi.getList(requestParams);
    }, '获取覆膜工艺列表');

    if (result) {
      setSurfaceProcessList(result.list || []);
      setTotal(result.pagination?.total || 0);
    } else {
      setSurfaceProcessList([]);
      setTotal(0);
    }
  };

  // 处理分页变化
  const handleTableChange = (pagination: any) => {
    setCurrent(pagination.current);
    setPageSize(pagination.pageSize);
    fetchSurfaceProcessList(pagination.current, pagination.pageSize);
  };

  // 打开添加模态框
  const showAddModal = () => {
    setModalTitle('添加覆膜工艺');
    setEditingRecord(null);
    form.resetFields();
    form.setFieldsValue({
      price: 0,
      basePrice: 0,
      thickness: 0,
      density: 0,
      unit: '元/平方'
    });
    setModalVisible(true);
  };

  // 打开编辑模态框
  const showEditModal = (record: SurfaceProcess) => {
    setModalTitle('编辑覆膜工艺');
    setEditingRecord(record);
    form.setFieldsValue({
      name: record.name,
      price: record.price,
      unit: record.unit,
      basePrice: record.basePrice,
      thickness: record.thickness,
      density: record.density,
      remark: record.remark || ''
    });
    setModalVisible(true);
  };

  // 处理表单提交
  const handleFormSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      const requestData = {
        name: values.name,
        price: Number(values.price),
        unit: values.unit,
        basePrice: Number(values.basePrice),
        thickness: Number(values.thickness),
        density: Number(values.density),
        remark: values.remark || ''
      };

      const result = await executeSurfaceProcess(async () => {
        if (editingRecord) {
          return await surfaceProcessApi.update({ ...requestData, id: editingRecord.id });
        } else {
          return await surfaceProcessApi.create(requestData);
        }
      }, editingRecord ? '更新覆膜工艺' : '创建覆膜工艺');

      if (result) {
        message.success('保存成功');
        setModalVisible(false);
        fetchSurfaceProcessList();
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 删除覆膜工艺
  const handleDelete = async (id: number) => {
    const result = await executeSurfaceProcess(async () => {
      return await surfaceProcessApi.delete(id);
    }, '删除覆膜工艺');

    if (result) {
      fetchSurfaceProcessList();
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center' as const,
      width: 150,
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      align: 'center' as const,
      width: 100,
      render: (price: number) => `¥${price.toFixed(2)}`
    },
    {
      title: '计价单位',
      dataIndex: 'unit',
      key: 'unit',
      align: 'center' as const,
      width: 100,
      render: (unit: string) => (
        <Tag color="blue">{unit}</Tag>
      )
    },
    {
      title: '起步价',
      dataIndex: 'basePrice',
      key: 'basePrice',
      align: 'center' as const,
      width: 100,
      render: (price: number) => price > 0 ? `¥${price.toFixed(2)}` : '-'
    },
    {
      title: '厚度',
      dataIndex: 'thickness',
      key: 'thickness',
      align: 'center' as const,
      width: 80,
      render: (thickness: number) => thickness > 0 ? `${thickness}μm` : '-'
    },
    {
      title: '密度',
      dataIndex: 'density',
      key: 'density',
      align: 'center' as const,
      width: 80,
      render: (density: number) => density > 0 ? `${density}g/cm³` : '-'
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      align: 'center' as const,
      width: 200,
      ellipsis: true,
      render: (remark: string) => remark || '-'
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      align: 'center' as const,
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'action',
      align: 'center' as const,
      fixed: 'right' as const,
      width: 150,
      render: (_: any, record: SurfaceProcess) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => showEditModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此覆膜工艺吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="是"
            cancelText="否"
          >
            <Button
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>覆膜工艺管理</Title>

      <Card>
        <div style={{ marginBottom: 16 }}>
          <Row gutter={16} align="middle">
            <Col span={4}>
              <Input
                placeholder="搜索名称或备注"
                prefix={<SearchOutlined />}
                allowClear
                value={keyword}
                onChange={(e) => setKeyword(e.target.value)}
                onPressEnter={() => fetchSurfaceProcessList(1, pageSize, keyword, unit)}
              />
            </Col>
            <Col>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={() => fetchSurfaceProcessList(1, pageSize, keyword, unit)}
              >
                搜索
              </Button>
            </Col>
            <Col>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => {
                  setKeyword('');
                  setUnit('');
                  fetchSurfaceProcessList(1, pageSize, '', '');
                }}
              >
                重置
              </Button>
            </Col>
            <Col flex="auto" style={{ textAlign: 'right' }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={showAddModal}
              >
                添加覆膜工艺
              </Button>
            </Col>
          </Row>
        </div>

        <Table
          columns={columns}
          dataSource={surfaceProcessList}
          rowKey="id"
          pagination={{
            current: current,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
          }}
          loading={surfaceProcessLoading}
          onChange={handleTableChange}
          bordered
          size="middle"
          scroll={{ x: 1000 }}
          locale={{ emptyText: '暂无数据' }}
        />
      </Card>

      {/* 表单模态框 */}
      <Modal
        title={modalTitle}
        open={modalVisible}
        onOk={handleFormSubmit}
        onCancel={() => setModalVisible(false)}
        width={600}
        okText="确定"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="名称"
            rules={[
              { required: true, message: '请输入覆膜工艺名称' },
              { max: 100, message: '名称长度不能超过100字符' }
            ]}
          >
            <Input placeholder="请输入覆膜工艺名称" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="price"
                label="价格"
                rules={[{ required: true, message: '请输入价格' }]}
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="0.00"
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="unit"
                label="计价单位"
                rules={[{ required: true, message: '请选择计价单位' }]}
              >
                <Select placeholder="请选择计价单位">
                  {SURFACE_PROCESS_UNITS.map(unit => (
                    <Option key={unit} value={unit}>{unit}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="basePrice"
                label="起步价"
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="0.00"
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="thickness"
                label="厚度(μm)"
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="0.00"
                  addonAfter="μm"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="density"
                label="密度(g/cm³)"
              >
                <InputNumber
                  min={0}
                  precision={3}
                  style={{ width: '100%' }}
                  placeholder="0.000"
                  addonAfter="g/cm³"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="remark"
            label="备注"
          >
            <TextArea
              placeholder="请输入备注信息（可选）"
              rows={3}
              showCount
              maxLength={500}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
} 