import { message } from 'antd';
import { ErrorCode, getErrorMessage } from '@/lib/constants/errorCodes';
import { Result, createSuccess, createError } from '@/types/common';

interface ApiResponse<T = any> {
  code: number;
  message: string;
  data?: T;
  errors?: any;
  timestamp?: string;
}

interface RetryConfig {
  maxRetries?: number;
  retryDelay?: number;
  retryOn?: (error: any) => boolean;
}

type RequestOptions = RequestInit & {
  showError?: boolean;
  showSuccess?: boolean;
  params?: Record<string, any>;
  timeout?: number;
  retry?: RetryConfig | boolean;
  onError?: (error: any) => void;
  onSuccess?: (data: any) => void;
  enableLoading?: boolean;
};

// 全局加载状态管理
let loadingCount = 0;
const loadingCallbacks: Set<(loading: boolean) => void> = new Set();

export function subscribeLoading(callback: (loading: boolean) => void) {
  loadingCallbacks.add(callback);
  return () => loadingCallbacks.delete(callback);
}

function updateLoadingState(increment: boolean) {
  if (increment) {
    loadingCount++;
  } else {
    loadingCount = Math.max(0, loadingCount - 1);
  }
  
  const isLoading = loadingCount > 0;
  loadingCallbacks.forEach(callback => callback(isLoading));
}

/**
 * 清理请求参数，移除无效值
 * @param params 原始参数对象
 * @returns 清理后的参数对象
 */
function cleanParams(params: Record<string, any>): Record<string, any> {
  const cleaned: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(params)) {
    // 跳过无效值
    if (
      value !== undefined && 
      value !== null && 
      value !== '' && 
      value !== 'undefined' && 
      value !== 'null' &&
      !(typeof value === 'number' && isNaN(value))
    ) {
      cleaned[key] = value;
    }
  }
  
  return cleaned;
}

/**
 * 延迟函数
 * @param ms 毫秒数
 */
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 执行重试逻辑
 * @param fn 要重试的函数
 * @param retryConfig 重试配置
 */
async function executeWithRetry<T>(
  fn: () => Promise<T>,
  retryConfig: RetryConfig
): Promise<T> {
  const { maxRetries = 3, retryDelay = 1000, retryOn } = retryConfig;
  
  let lastError: any;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      // 如果是最后一次尝试，直接抛出错误
      if (attempt === maxRetries) {
        throw error;
      }
      
      // 检查是否应该重试
      if (retryOn && !retryOn(error)) {
        throw error;
      }
      
      // 等待后重试
      if (retryDelay > 0) {
        await delay(retryDelay * Math.pow(2, attempt)); // 指数退避
      }
    }
  }
  
  throw lastError;
}

/**
 * 超时处理
 * @param promise 原始Promise
 * @param timeoutMs 超时时间（毫秒）
 */
function withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error(`请求超时（${timeoutMs}ms）`)), timeoutMs)
    )
  ]);
}

/**
 * 统一的请求处理函数 - 返回Result类型
 * @param url 请求地址
 * @param options 请求选项
 * @returns Result<T>类型的结果
 */
export async function requestResult<T = any>(
  url: string,
  options: RequestOptions = {}
): Promise<Result<T>> {
  const {
    showError = true,
    showSuccess = false,
    params,
    timeout = 30000,
    retry = false,
    onError,
    onSuccess,
    enableLoading = true,
    ...fetchOptions
  } = options;

  // 处理查询参数
  let finalUrl = url;
  if (params) {
    const cleanedParams = cleanParams(params);
    const searchParams = new URLSearchParams();
    
    Object.entries(cleanedParams).forEach(([key, value]) => {
      searchParams.append(key, String(value));
    });
    
    const queryString = searchParams.toString();
    if (queryString) {
      finalUrl = `${url}${url.includes('?') ? '&' : '?'}${queryString}`;
    }
  }

  // 更新加载状态
  if (enableLoading) {
    updateLoadingState(true);
  }

  try {
    // 构建请求函数
    const executeRequest = async (): Promise<Result<T>> => {
      console.log(`[API Request] ${fetchOptions.method || 'GET'} ${finalUrl}`);
      
      const fetchPromise = fetch(finalUrl, {
        ...fetchOptions,
        headers: {
          'Content-Type': 'application/json',
          ...fetchOptions.headers,
        },
      });

      // 应用超时
      const response = await withTimeout(fetchPromise, timeout);

      // 检查 HTTP 状态码
      if (!response.ok) {
        const errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        console.error('[API Error] HTTP错误:', errorMessage);
        
        const error = {
          code: response.status,
          message: errorMessage,
          type: 'HTTP_ERROR',
        };
        
        if (onError) onError(error);
        if (showError) {
          message.error(`网络错误: ${errorMessage}`);
        }
        
        return createError(response.status, errorMessage);
      }

      // 解析响应
      let data: ApiResponse<T>;
      try {
        data = await response.json();
      } catch (parseError) {
        console.error('[API Error] JSON解析失败:', parseError);
        
        const error = {
          code: ErrorCode.INVALID_FORMAT,
          message: '服务器响应格式错误',
          type: 'PARSE_ERROR',
          details: parseError,
        };
        
        if (onError) onError(error);
        if (showError) {
          message.error('服务器响应格式错误');
        }
        
        return createError(ErrorCode.INVALID_FORMAT, '服务器响应格式错误');
      }

      console.log(`[API Response] Code: ${data.code}, Message: ${data.message}`);

      // 处理业务错误码
      if (data.code >= 400) {
        const errorMessage = data.message || '请求失败';
        console.error('[API Error] 业务错误:', {
          code: data.code,
          message: errorMessage,
          errors: data.errors,
          url: finalUrl
        });
        
        const error = {
          code: data.code,
          message: errorMessage,
          type: 'BUSINESS_ERROR',
          details: data.errors,
        };
        
        if (onError) onError(error);
        if (showError) {
          // 根据错误码显示不同的错误消息
          const displayMessage = getErrorMessage(data.code as ErrorCode) || errorMessage;
          message.error(displayMessage);
        }
        
        return createError(data.code, errorMessage, data.errors);
      }

      // 处理成功响应
      if (data.code === 200) {
        if (showSuccess && data.message) {
          message.success(data.message);
        }
        
        if (onSuccess) onSuccess(data.data);
        return createSuccess(data.data as T);
      }

      // 默认成功处理
      if (onSuccess) onSuccess(data.data);
      return createSuccess(data.data as T);
    };

    // 执行请求（带重试机制）
    if (retry) {
      const retryConfig: RetryConfig = typeof retry === 'boolean' 
        ? {
            maxRetries: 3,
            retryDelay: 1000,
            retryOn: (error: any) => {
              // 默认只对网络错误和5xx错误重试
              return error.type === 'HTTP_ERROR' && 
                     (error.code >= 500 || error.message.includes('fetch'));
            }
          }
        : retry;
        
      return await executeWithRetry(executeRequest, retryConfig);
    } else {
      return await executeRequest();
    }

  } catch (error) {
    console.error('[API Error] 网络请求失败:', error);
    
    const errorObj = {
      code: ErrorCode.NETWORK_ERROR,
      message: error instanceof Error ? error.message : '网络连接失败',
      type: 'NETWORK_ERROR',
      details: error,
    };
    
    if (onError) onError(errorObj);
    if (showError) {
      message.error('网络连接失败，请检查网络后重试');
    }
    
    return createError(ErrorCode.NETWORK_ERROR, '网络连接失败，请检查网络后重试');
  } finally {
    // 更新加载状态
    if (enableLoading) {
      updateLoadingState(false);
    }
  }
}

// 新的Result类型API
export const resultApi = {
  post: <T = any>(url: string, data?: any, options: RequestOptions = {}) =>
    requestResult<T>(url, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : '{}',
    })
};