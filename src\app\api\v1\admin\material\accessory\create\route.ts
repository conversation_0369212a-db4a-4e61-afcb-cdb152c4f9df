import { NextRequest } from 'next/server';
import { createAccessorySchema, CreateAccessoryParams } from '@/lib/validations/admin/accessory';
import { prisma } from '@/lib/prisma';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation<CreateAccessoryParams>(
  createAccessorySchema,
  async (request: NextRequest, validatedData: CreateAccessoryParams) => {
    const { name, price, initialPrice, weight, unit, remark } = validatedData;

    // 检查名称是否已存在
    const existingAccessory = await prisma.accessory.findFirst({
      where: {
        name,
        isDel: false,
      },
    });

    assert(!existingAccessory, ErrorCode.MATERIAL_NAME_EXISTS, '配件名称已存在');

    // 创建配件
    const accessory = await prisma.accessory.create({
      data: {
        name,
        price,
        initialPrice,
        weight,
        unit,
        remark: remark || null,
        isDel: false,
      },
    });

    return successResponse(accessory, '创建配件成功');
  }
); 