import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getSpecialPaperDetailSchema, GetSpecialPaperDetailParams } from '@/lib/validations/admin/specialPaper';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation<GetSpecialPaperDetailParams>(
  getSpecialPaperDetailSchema,
  async (request: AuthenticatedRequest, validatedQuery: GetSpecialPaperDetailParams) => {
    // 查询特种纸详情
    const data = await prisma.specialPaper.findUnique({
      where: {
        id: validatedQuery.id,
        isDel: false,
      },
    });

    assertExists(data, ErrorCode.MATERIAL_NOT_FOUND, '特种纸不存在');

    return successResponse(data, '获取特种纸详情成功');
  }
); 
export const POST = withInternalAuth(handler);