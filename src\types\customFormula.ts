// 自定义公式相关类型定义

// 公式状态
export enum FormulaStatus {
  DISABLED = 0,
  ENABLED = 1
}

// 自定义公式属性
export interface CustomFormulaAttribute {
  id?: number;
  name: string;
  value?: number | null;
}

// 自定义公式基本信息
export interface CustomFormula {
  id: number;
  name: string;
  initialAmount: number;
  expression?: string;
  status: FormulaStatus;
  createdAt: string;
  updatedAt: string;
  attributes?: CustomFormulaAttribute[];
  _count?: {
    attributes: number;
  };
}

// 获取自定义公式列表的参数
export interface CustomFormulaListParams {
  page?: number;
  pageSize?: number;
  name?: string;
  status?: number;
  startTime?: string;
  endTime?: string;
}

// 创建自定义公式的参数
export interface CustomFormulaCreateParams {
  name: string;
  initialAmount: number;
  expression?: string;
  status: number;
  attributes?: CustomFormulaAttribute[];
}

// 更新自定义公式的参数
export interface CustomFormulaUpdateParams {
  id: number;
  name?: string;
  initialAmount?: number;
  expression?: string;
  status?: number;
  attributes?: CustomFormulaAttribute[];
  deleteAttributes?: number[];
} 