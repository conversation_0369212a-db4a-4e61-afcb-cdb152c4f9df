# 用户认证系统使用指南

## 概述

本系统实现了完整的用户认证功能，包括用户注册、登录、权限管理等功能。

## 功能特性

### 1. 用户角色系统
- **普通用户 (USER)**: 基础用户权限
- **超级用户 (SUPER_USER)**: 有到期时间限制的高级用户
- **内部用户 (INTERNAL_USER)**: 内部员工用户
- **管理员 (ADMIN)**: 系统管理员，拥有最高权限

### 2. 安全特性
- 密码使用bcrypt哈希加密存储
- JWT Token认证，支持httpOnly Cookie
- 登录IP地址记录
- 用户状态管理（启用/禁用）
- 软删除机制

### 3. 登录方式
- 手机号登录
- 邮箱登录
- 用户名登录

## 初始用户账号

系统已创建以下测试账号：

| 角色 | 手机号 | 邮箱 | 密码 |
|------|--------|------|------|
| 管理员 | 13800138000 | <EMAIL> | admin123456 |
| 测试用户 | 13800138001 | <EMAIL> | user123456 |
| 超级用户 | 13800138002 | <EMAIL> | super123456 |
| 内部用户 | 13800138003 | <EMAIL> | internal123456 |

## API接口

### 前台认证接口

#### 1. 用户登录
- **URL**: `POST /api/v1/auth/login`
- **参数**:
  ```json
  {
    "username": "13800138000", // 手机号/邮箱/用户名
    "password": "admin123456",
    "rememberMe": true
  }
  ```

#### 2. 用户登出
- **URL**: `POST /api/v1/auth/logout`
- **需要认证**: 是

#### 3. 获取用户信息
- **URL**: `GET /api/v1/auth/profile`
- **需要认证**: 是

#### 4. 修改密码
- **URL**: `POST /api/v1/auth/change-password`
- **需要认证**: 是
- **参数**:
  ```json
  {
    "oldPassword": "old123456",
    "newPassword": "new123456",
    "confirmPassword": "new123456"
  }
  ```

### 管理后台用户管理接口

#### 1. 获取用户列表
- **URL**: `POST /api/v1/admin/users/getList`
- **权限**: 内部用户或管理员
- **参数**:
  ```json
  {
    "page": 1,
    "pageSize": 10,
    "keyword": "搜索关键词",
    "role": "USER",
    "state": 1
  }
  ```

#### 2. 创建用户
- **URL**: `POST /api/v1/admin/users/create`
- **权限**: 内部用户或管理员

#### 3. 更新用户
- **URL**: `POST /api/v1/admin/users/update`
- **权限**: 内部用户或管理员

#### 4. 删除用户
- **URL**: `POST /api/v1/admin/users/delete`
- **权限**: 内部用户或管理员

#### 5. 更新用户状态
- **URL**: `POST /api/v1/admin/users/updateState`
- **权限**: 内部用户或管理员

## 前端页面

### 1. 登录页面
- **路径**: `/login`
- **功能**: 支持手机号、邮箱、用户名登录
- **特性**: 响应式设计，适配移动端

### 2. 用户管理页面
- **路径**: `/admin/users`
- **权限**: 内部用户或管理员
- **功能**: 
  - 用户列表展示
  - 搜索和筛选
  - 创建/编辑用户
  - 启用/禁用用户
  - 删除用户

## 开发指南

### 1. 环境配置

复制环境变量配置文件：
```bash
cp .env.example .env
```

修改`.env`文件中的配置：
```env
DATABASE_URL="mysql://username:password@localhost:3306/ycbz"
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
JWT_EXPIRES_IN="7d"
```

### 2. 数据库迁移

```bash
# 推送schema到数据库
npm run db:push

# 生成Prisma客户端
npm run prisma:generate

# 创建初始用户（可选）
npx tsx prisma/seed-users.ts
```

### 3. 权限控制

在API路由中使用认证中间件：

```typescript
import { withAuth, withAdminAuth, withInternalAuth } from '@/lib/auth/middleware';

// 需要登录
export const POST = withAuth(handler);

// 需要管理员权限
export const POST = withAdminAuth(handler);

// 需要内部用户权限
export const POST = withInternalAuth(handler);
```

### 4. 前端认证状态管理

```typescript
import { getCurrentUser, login, logout } from '@/services/auth';

// 获取当前用户
const user = await getCurrentUser();

// 登录
const result = await login({
  username: 'phone_or_email',
  password: 'password',
  rememberMe: true
});

// 登出
await logout();
```

## 安全注意事项

1. **生产环境配置**:
   - 修改默认的JWT_SECRET
   - 使用强密码策略
   - 启用HTTPS

2. **密码安全**:
   - 密码最少8位，包含字母和数字
   - 定期提醒用户修改密码
   - 限制登录失败次数

3. **Token安全**:
   - Token存储在httpOnly Cookie中
   - 设置合理的过期时间
   - 登出时清除Token

4. **权限控制**:
   - 严格验证用户权限
   - 敏感操作需要二次确认
   - 记录重要操作日志

## 故障排除

### 1. 登录失败
- 检查用户名和密码是否正确
- 确认用户状态是否为启用
- 检查超级用户是否已过期

### 2. 权限不足
- 确认用户角色是否正确
- 检查API接口的权限要求
- 验证Token是否有效

### 3. 数据库连接问题
- 检查DATABASE_URL配置
- 确认数据库服务是否运行
- 验证数据库用户权限
