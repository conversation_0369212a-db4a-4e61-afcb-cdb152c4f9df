import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';
import { z } from 'zod';

const getDetailSchema = z.object({
  id: z.number().positive('ID必须是正整数'),
});

const handler = withValidation(
  getDetailSchema,
  async (request: AuthenticatedRequest, validatedData: any) => {
    const { id } = validatedData;

    // 查询液压工艺详情
    const hydraulicProcess = await prisma.hydraulicProcess.findFirst({
      where: {
        id,
        isDel: false,
      },
    });

    assert(!!hydraulicProcess, ErrorCode.NOT_FOUND, '液压工艺不存在');

    return successResponse(
      hydraulicProcess,
      '获取液压工艺详情成功'
    );
  }
);
export const POST = withInternalAuth(handler);