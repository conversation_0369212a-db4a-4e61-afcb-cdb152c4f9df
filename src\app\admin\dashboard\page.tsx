'use client';

import React from 'react';
import { Card, Row, Col, Statistic, Button, List, Typography, Alert, Space, Tag } from 'antd';
import {
  DashboardOutlined,
  InboxOutlined,
  FileOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  UserOutlined
} from '@ant-design/icons';
import { useAuth } from '@/hooks/useAuth';
import { UserRole } from '@/types/user';

const { Title, Paragraph } = Typography;

export default function DashboardPage() {
  const { user } = useAuth();

  const getUserRoleLabel = (role: UserRole): string => {
    const roleLabels: Record<UserRole, string> = {
      [UserRole.USER]: '普通用户',
      [UserRole.SUPER_USER]: '超级用户',
      [UserRole.INTERNAL_USER]: '内部用户',
      [UserRole.ADMIN]: '管理员'
    };
    return roleLabels[role] || '未知角色';
  };

  const getRoleColor = (role: UserRole): string => {
    const roleColors: Record<UserRole, string> = {
      [UserRole.USER]: 'default',
      [UserRole.SUPER_USER]: 'orange',
      [UserRole.INTERNAL_USER]: 'blue',
      [UserRole.ADMIN]: 'red'
    };
    return roleColors[role] || 'default';
  };

  return (
    <div>
      <Title level={2}>仪表盘</Title>

      {/* 欢迎信息 */}
      {user && (
        <Alert
          message={
            <Space>
              <CheckCircleOutlined style={{ color: '#52c41a' }} />
              <span>欢迎访问管理后台，{user.name}！</span>
            </Space>
          }
          description={
            <Space direction="vertical" size="small">
              <div>
                <UserOutlined style={{ marginRight: 8 }} />
                当前角色：
                <Tag color={getRoleColor(user.role)} style={{ marginLeft: 8 }}>
                  {getUserRoleLabel(user.role)}
                </Tag>
              </div>
              <div>您拥有管理后台的完整访问权限，可以管理系统的各项功能。</div>
            </Space>
          }
          type="success"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}
      
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic 
              title="盒型总数" 
              value={0} 
              prefix={<InboxOutlined />} 
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic 
              title="已发布盒型" 
              value={0} 
              prefix={<FileOutlined />} 
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic 
              title="草稿盒型" 
              value={0} 
              prefix={<FileOutlined />} 
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic 
              title="系统运行天数" 
              value={0} 
              prefix={<CalendarOutlined />} 
            />
          </Card>
        </Col>
      </Row>
      
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col xs={24} md={12}>
          <Card title="最近活动">
            <List
              size="small"
              locale={{ emptyText: '暂无活动记录' }}
              dataSource={[]}
              renderItem={(item) => <List.Item>{item}</List.Item>}
            />
          </Card>
        </Col>
        
        <Col xs={24} md={12}>
          <Card title="快速操作">
            <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
              <Button type="primary" size="middle">新建盒型</Button>
              <Button>导入盒型</Button>
              <Button>系统设置</Button>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
} 