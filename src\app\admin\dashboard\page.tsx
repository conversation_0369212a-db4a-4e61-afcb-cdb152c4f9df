'use client';

import React from 'react';
import { Card, Row, Col, Statistic, Button, List, Typography } from 'antd';
import { DashboardOutlined, InboxOutlined, FileOutlined, CalendarOutlined } from '@ant-design/icons';

const { Title } = Typography;

export default function DashboardPage() {
  return (
    <div>
      <Title level={2}>仪表盘</Title>
      
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic 
              title="盒型总数" 
              value={0} 
              prefix={<InboxOutlined />} 
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic 
              title="已发布盒型" 
              value={0} 
              prefix={<FileOutlined />} 
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic 
              title="草稿盒型" 
              value={0} 
              prefix={<FileOutlined />} 
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic 
              title="系统运行天数" 
              value={0} 
              prefix={<CalendarOutlined />} 
            />
          </Card>
        </Col>
      </Row>
      
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col xs={24} md={12}>
          <Card title="最近活动">
            <List
              size="small"
              locale={{ emptyText: '暂无活动记录' }}
              dataSource={[]}
              renderItem={(item) => <List.Item>{item}</List.Item>}
            />
          </Card>
        </Col>
        
        <Col xs={24} md={12}>
          <Card title="快速操作">
            <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
              <Button type="primary" size="middle">新建盒型</Button>
              <Button>导入盒型</Button>
              <Button>系统设置</Button>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
} 