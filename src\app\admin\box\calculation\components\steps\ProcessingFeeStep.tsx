'use client';

import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import {
  Card, Button, Space, Table, Modal, Form, Input,
  InputNumber, Select, Typography, Alert, message,
  Row, Col, Tag, Popconfirm, Tooltip
} from 'antd';
import {
  PlusOutlined, DeleteOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { CalculationState, ProcessingFeeItem } from '../../types/calculation';
import { ProcessingFee, ProcessingParams } from '@/types/craftSalary';
import { processingFeeApi, processingParamsApi } from '@/services/adminApi';
import { perfLog } from '@/lib/utils/perfLog';


const { Title, Text } = Typography;

interface ProcessingFeeStepProps {
  state: CalculationState;
  onUpdate: {
    processingFeeConfig: (data: Partial<CalculationState["processingFeeConfig"]>) => void;
  };
}

/**
 * 加工费选择步骤组件
 */
const ProcessingFeeStep: React.FC<ProcessingFeeStepProps> = ({ state, onUpdate }) => {

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [fixedFeeModalVisible, setFixedFeeModalVisible] = useState(false);
  const [optionalFeeModalVisible, setOptionalFeeModalVisible] = useState(false);

  // 数据状态
  const [availableProcessingFees, setAvailableProcessingFees] = useState<ProcessingFee[]>([]);
  const [processingParams, setProcessingParams] = useState<ProcessingParams | null>(null);

  // 可选加工费配置状态
  const [selectedOptionalFee, setSelectedOptionalFee] = useState<ProcessingFee | null>(null);
  const [optionalFeeLength, setOptionalFeeLength] = useState<number>(100);
  const [optionalFeeWidth, setOptionalFeeWidth] = useState<number>(100);

  // 表单
  const [optionalFeeForm] = Form.useForm();

  // 使用 ref 来跟踪状态，避免依赖项问题
  const loadingRef = useRef(false);
  const dataInitializedRef = useRef(false);

  // 获取加工费数据 - 移除所有依赖项，使用 ref 跟踪状态
  const fetchProcessingFeeData = useCallback(async () => {
    // 防止重复调用
    if (loadingRef.current || dataInitializedRef.current) {
      return;
    }

    loadingRef.current = true;
    setLoading(true);

    try {
      const [feeResult, paramsResult] = await Promise.all([
        processingFeeApi.getList({ page: 1, pageSize: 100 }),
        processingParamsApi.get()
      ]);

      if (feeResult?.data?.list) {
        setAvailableProcessingFees(feeResult.data.list);
      }
      if (paramsResult?.data) {
        setProcessingParams(paramsResult.data);
      }

      dataInitializedRef.current = true;
    } catch (error) {
      perfLog.error('获取加工费数据失败:', error);
      message.error('获取加工费数据失败');
    } finally {
      loadingRef.current = false;
      setLoading(false);
    }
  }, []); // 空依赖数组，函数永远不会重新创建

  // 初始化数据 - 只在组件挂载时执行一次
  useEffect(() => {
    let isMounted = true;

    const initializeData = async () => {
      if (!dataInitializedRef.current && isMounted) {
        await fetchProcessingFeeData();
      }
    };

    initializeData();

    // 清理函数
    return () => {
      isMounted = false;
    };
  }, [fetchProcessingFeeData]); // 只依赖 fetchProcessingFeeData，它永远不会变化

  // 计算加工费总成本
  const calculateTotalProcessingFeeCost = useCallback((customFees: ProcessingFeeItem[], fixedFees: ProcessingFeeItem[]) => {
    const customTotal = customFees.reduce((sum, fee) => sum + (fee.totalPrice || 0), 0);
    const fixedTotal = fixedFees.reduce((sum, fee) => sum + (fee.totalPrice || 0), 0);
    return customTotal + fixedTotal;
  }, []);

  // 初始化加工费总成本计算 - 使用 ref 避免依赖项问题
  const onUpdateRef = useRef(onUpdate);
  onUpdateRef.current = onUpdate;

  // 使用 useMemo 来稳定化依赖项
  const processingFeeState = useMemo(() => ({
    customFeesLength: state.processingFeeConfig?.customFees?.length || 0,
    fixedFeesLength: state.processingFeeConfig?.fixedFees?.length || 0,
    processingFeeCost: state.processingFeeConfig?.processingFeeCost
  }), [
    state.processingFeeConfig?.customFees?.length,
    state.processingFeeConfig?.fixedFees?.length,
    state.processingFeeConfig?.processingFeeCost
  ]);

  useEffect(() => {
    const customFees = state.processingFeeConfig?.customFees || [];
    const fixedFees = state.processingFeeConfig?.fixedFees || [];

    // 只有当加工费存在但总费用未设置时才计算
    if ((processingFeeState.customFeesLength > 0 || processingFeeState.fixedFeesLength > 0) &&
      processingFeeState.processingFeeCost === undefined) {
      const totalCost = calculateTotalProcessingFeeCost(customFees, fixedFees);
      onUpdateRef.current.processingFeeConfig({
        processingFeeCost: totalCost
      });
    }
  }, [processingFeeState, calculateTotalProcessingFeeCost]); // 使用稳定化的状态对象

  // 计算面积（毫米转平方米）
  const calculateArea = (length: number, width: number): number => {
    // 确保输入值有效，使用默认值100而不是0
    const validLength = isNaN(length) || length <= 0 ? 100 : length;
    const validWidth = isNaN(width) || width <= 0 ? 100 : width;
    return (validLength * validWidth) / 1000000;
  };

  // 更新面积计算
  const updateAreaCalculation = useCallback(() => {
    if (selectedOptionalFee?.unit === '元/平方') {
      const boxQuantity = state.basicInfo?.quantity || 1;
      // 直接使用当前状态值，calculateArea函数内部会处理默认值
      const singleBoxArea = calculateArea(optionalFeeLength, optionalFeeWidth);
      const totalArea = singleBoxArea * boxQuantity;

      // 确保计算结果有效
      const finalArea = isNaN(totalArea) ? 0 : totalArea;

      optionalFeeForm.setFieldsValue({
        quantity: finalArea
      });

      // 调试信息
      perfLog.debug('面积计算更新:', {
        optionalFeeLength,
        optionalFeeWidth,
        singleBoxArea,
        boxQuantity,
        totalArea: finalArea
      });
    }
  }, [selectedOptionalFee?.unit, optionalFeeLength, optionalFeeWidth, state.basicInfo?.quantity, optionalFeeForm]);

  // 当模态框打开且为面积单位时，初始化面积计算
  useEffect(() => {
    if (fixedFeeModalVisible && selectedOptionalFee?.unit === '元/平方') {
      updateAreaCalculation();
    }
  }, [fixedFeeModalVisible, selectedOptionalFee?.unit, updateAreaCalculation]);

  // 打开可选加工费选择模态框
  const handleOpenOptionalFeeModal = () => {
    setOptionalFeeModalVisible(true);
  };

  // 添加可选加工费
  const handleAddOptionalFee = (fee: ProcessingFee) => {
    setSelectedOptionalFee(fee);

    // 获取盒子数量
    const boxQuantity = state.basicInfo?.quantity || 1;

    // 根据单位类型设置默认值
    let defaultQuantity = 1;
    if (fee.unit === '元/个') {
      defaultQuantity = boxQuantity;
    } else if (fee.unit === '元/平方') {
      // 对于面积单位，先设置默认尺寸
      const defaultLength = 100;
      const defaultWidth = 100;
      setOptionalFeeLength(defaultLength);
      setOptionalFeeWidth(defaultWidth);
      // 计算默认面积，确保计算结果有效
      defaultQuantity = calculateArea(defaultLength, defaultWidth) * boxQuantity;

      perfLog.debug('设置面积单位默认值:', {
        defaultLength,
        defaultWidth,
        boxQuantity,
        defaultQuantity
      });
    }

    optionalFeeForm.resetFields();
    optionalFeeForm.setFieldsValue({
      name: fee.name,
      unitPrice: fee.unitPrice,
      unit: fee.unit,
      basePrice: fee.basePrice,
      quantity: defaultQuantity
    });

    setOptionalFeeModalVisible(false);
    setFixedFeeModalVisible(true);
  };

  // 确认可选加工费
  const handleOptionalFeeConfirm = async () => {
    try {
      const values = await optionalFeeForm.validateFields();

      // 检查是否已经添加过相同的可选加工费
      const currentOptionalFees = state.processingFeeConfig?.customFees || [];
      const existingFee = currentOptionalFees.find(fee => fee.name === values.name);

      if (existingFee) {
        message.warning(`${values.name}已经添加过了`);
        return;
      }

      // 确保所有数值有效
      const unitPrice = isNaN(values.unitPrice) ? 0 : values.unitPrice;
      const quantity = isNaN(values.quantity) ? 0 : values.quantity;
      const basePrice = isNaN(values.basePrice) ? 0 : (values.basePrice || 0);

      const totalPrice = Math.max(
        unitPrice * quantity,
        basePrice
      );

      // 确保总价有效
      const finalTotalPrice = isNaN(totalPrice) ? 0 : totalPrice;

      const feeItem: ProcessingFeeItem = {
        id: Date.now(),
        name: values.name,
        unitPrice,
        unit: values.unit,
        basePrice,
        quantity,
        totalPrice: finalTotalPrice,
        isCustom: false
      };

      perfLog.debug('可选加工费确认数据:', {
        originalValues: values,
        processedValues: {
          unitPrice,
          quantity,
          basePrice,
          totalPrice: finalTotalPrice
        }
      });

      // 添加到可选加工费
      const currentFixedFees = state.processingFeeConfig?.fixedFees || [];
      const newOptionalFees = [...currentOptionalFees, feeItem];

      // 计算更新后的总费用
      const newTotalCost = calculateTotalProcessingFeeCost(newOptionalFees, currentFixedFees);

      onUpdateRef.current.processingFeeConfig({
        customFees: newOptionalFees,
        processingFeeCost: newTotalCost
      });

      setFixedFeeModalVisible(false);
      // 重置状态
      setSelectedOptionalFee(null);
      setOptionalFeeLength(100);
      setOptionalFeeWidth(100);

      message.success('添加成功');
      perfLog.debug('可选加工费添加成功:', feeItem);
    } catch (error) {
      perfLog.error('可选加工费添加失败:', error);
      message.error('添加失败');
    }
  };

  // 删除可选加工费（原来的 customFees 现在用于存储选择的可选加工费）
  const handleRemoveOptionalFee = useCallback((index: number) => {
    const currentOptionalFees = state.processingFeeConfig?.customFees || [];
    const currentFixedFees = state.processingFeeConfig?.fixedFees || [];

    perfLog.debug('删除可选加工费:', {
      index,
      currentOptionalFeesLength: currentOptionalFees.length,
      itemToDelete: currentOptionalFees[index]?.name,
      allOptionalFees: currentOptionalFees.map((fee, i) => ({ index: i, name: fee.name, id: fee.id }))
    });

    // 验证索引有效性
    if (index < 0 || index >= currentOptionalFees.length) {
      perfLog.error('删除可选加工费失败: 无效的索引', {
        index,
        currentOptionalFeesLength: currentOptionalFees.length
      });
      message.error('删除失败：无效的项目索引');
      return;
    }

    // 验证要删除的项目存在
    const itemToDelete = currentOptionalFees[index];
    if (!itemToDelete) {
      perfLog.error('删除可选加工费失败: 项目不存在', {
        index,
        currentOptionalFees
      });
      message.error('删除失败：项目不存在');
      return;
    }

    // 使用filter创建新数组，确保索引正确
    const newOptionalFees = currentOptionalFees.filter((_, i) => i !== index);
    const newTotalCost = calculateTotalProcessingFeeCost(newOptionalFees, currentFixedFees);

    // 更新状态
    onUpdateRef.current.processingFeeConfig({
      customFees: newOptionalFees,
      processingFeeCost: newTotalCost
    });

    message.success(`删除${itemToDelete.name}成功`);
    perfLog.debug('删除可选加工费完成:', {
      deletedItem: itemToDelete.name,
      originalLength: currentOptionalFees.length,
      newLength: newOptionalFees.length,
      newTotalCost
    });
  }, [calculateTotalProcessingFeeCost, state.processingFeeConfig?.customFees]);



  // 删除固定选项加工费
  const handleRemoveFixedFee = useCallback((index: number) => {
    const currentCustomFees = state.processingFeeConfig?.customFees || [];
    const currentFixedFees = state.processingFeeConfig?.fixedFees || [];

    perfLog.debug('删除固定选项加工费:', {
      index,
      currentFixedFeesLength: currentFixedFees.length,
      itemToDelete: currentFixedFees[index]?.name,
      allFixedFees: currentFixedFees.map((fee, i) => ({ index: i, name: fee.name, id: fee.id }))
    });

    // 验证索引有效性
    if (index < 0 || index >= currentFixedFees.length) {
      perfLog.error('删除固定选项加工费失败: 无效的索引', {
        index,
        currentFixedFeesLength: currentFixedFees.length
      });
      message.error('删除失败：无效的项目索引');
      return;
    }

    // 验证要删除的项目存在
    const itemToDelete = currentFixedFees[index];
    if (!itemToDelete) {
      perfLog.error('删除固定选项加工费失败: 项目不存在', {
        index,
        currentFixedFees
      });
      message.error('删除失败：项目不存在');
      return;
    }

    // 使用filter创建新数组，确保索引正确
    const newFixedFees = currentFixedFees.filter((_, i) => i !== index);
    const newTotalCost = calculateTotalProcessingFeeCost(currentCustomFees, newFixedFees);

    // 更新状态
    onUpdateRef.current.processingFeeConfig({
      fixedFees: newFixedFees,
      processingFeeCost: newTotalCost
    });

    message.success(`删除${itemToDelete.name}成功`);
    perfLog.debug('删除固定选项加工费完成:', {
      deletedItem: itemToDelete.name,
      originalLength: currentFixedFees.length,
      newLength: newFixedFees.length,
      newTotalCost
    });
  }, [calculateTotalProcessingFeeCost, state.processingFeeConfig?.fixedFees]);

  // 使用 useMemo 确保表格数据源的一致性 - 简化索引处理
  const fixedFeesDataSource = useMemo(() => {
    const fees = state.processingFeeConfig?.fixedFees || [];
    perfLog.debug('固定选项加工费数据源更新:', {
      feesLength: fees.length,
      fees: fees.map((fee, index) => ({ index, name: fee.name, id: fee.id }))
    });
    // 直接使用数组索引，不需要额外的 originalIndex
    return fees.map((item, index) => ({ ...item, key: `fixed-${index}` }));
  }, [state.processingFeeConfig?.fixedFees]);

  const optionalFeesDataSource = useMemo(() => {
    const fees = state.processingFeeConfig?.customFees || [];
    perfLog.debug('可选加工费数据源更新:', {
      feesLength: fees.length,
      fees: fees.map((fee, index) => ({ index, name: fee.name, id: fee.id }))
    });
    // 直接使用数组索引，不需要额外的 originalIndex
    return fees.map((item, index) => ({ ...item, key: `optional-${index}` }));
  }, [state.processingFeeConfig?.customFees]);

  // 添加固定参数加工费 - 直接使用盒子数量
  const handleAddFixedParamFee = (_paramKey: string, name: string, unitPrice: number, unit: string, basePrice: number = 0) => {
    // 检查是否已经添加过相同的固定参数
    const currentFixedFees = state.processingFeeConfig?.fixedFees || [];
    const existingFee = currentFixedFees.find(fee => fee.name === name);

    perfLog.debug('添加固定参数加工费开始:', {
      name,
      currentFixedFeesLength: currentFixedFees.length,
      currentFixedFees: currentFixedFees.map(fee => fee.name),
      existingFee: existingFee?.name
    });

    if (existingFee) {
      message.warning(`${name}已经添加过了`);
      return;
    }

    // 获取盒子数量，默认为1
    const boxQuantity = state.basicInfo?.quantity || 1;

    // 计算总价
    const totalPrice = Math.max(
      unitPrice * boxQuantity,
      basePrice
    );

    const feeItem: ProcessingFeeItem = {
      id: Date.now(),
      name,
      unitPrice,
      unit,
      basePrice,
      quantity: boxQuantity,
      totalPrice,
      isCustom: false
    };

    const currentOptionalFees = state.processingFeeConfig?.customFees || [];
    const newFixedFees = [...currentFixedFees, feeItem];

    // 计算更新后的总费用
    const newTotalCost = calculateTotalProcessingFeeCost(currentOptionalFees, newFixedFees);

    perfLog.debug('添加固定参数加工费，准备更新状态:', {
      currentFixedFeesLength: currentFixedFees.length,
      newFixedFeesLength: newFixedFees.length,
      newFixedFees: newFixedFees.map(fee => ({ name: fee.name, id: fee.id })),
      newTotalCost
    });

    onUpdateRef.current.processingFeeConfig({
      fixedFees: newFixedFees,
      processingFeeCost: newTotalCost
    });

    message.success(`添加${name}成功，数量：${boxQuantity}个`);
    perfLog.debug('固定参数加工费添加成功:', feeItem);
  };

  // 可选加工费表格列定义（用于已选择的可选加工费显示）
  const optionalFeeColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      width: 100,
      render: (price: number, record: ProcessingFeeItem) => (
        <Text>¥{price.toFixed(2)}/{record.unit}</Text>
      )
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
      align: 'center' as const,
      render: (quantity: number, record: ProcessingFeeItem) => {
        if (isNaN(quantity)) return '0';
        // 如果是面积单位，显示更多小数位
        if (record.unit === '元/平方') {
          return quantity.toFixed(6);
        }
        return quantity.toString();
      }
    },
    {
      title: '起步价',
      dataIndex: 'basePrice',
      key: 'basePrice',
      width: 100,
      render: (price: number) => <Text>¥{price.toFixed(2)}</Text>
    },
    {
      title: '小计',
      dataIndex: 'totalPrice',
      key: 'totalPrice',
      width: 100,
      render: (price: number) => {
        const validPrice = isNaN(price) ? 0 : price;
        return (
          <Text strong style={{ color: '#52c41a' }}>¥{validPrice.toFixed(2)}</Text>
        );
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_: any, _record: any, index: number) => (
        <Popconfirm
          title="确定要删除吗？"
          onConfirm={() => handleRemoveOptionalFee(index)}
          okText="确定"
          cancelText="取消"
        >
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
          >
            删除
          </Button>
        </Popconfirm>
      )
    }
  ];

  return (
    <div className="processing-fee-step">
      <Row gutter={[0, 24]}>
        {/* 固定参数选项 */}
        <Col span={24}>
          <Card
            title={
              <Space>
                <InfoCircleOutlined />
                <span>加工选择</span>
              </Space>
            }
          >
            {processingParams && (
              <div style={{ marginBottom: 16 }}>
                <Row gutter={[16, 16]}>
                  <Col xs={24} sm={12} md={8} >
                    <Button
                      size="large"
                      style={{ width: '100%', height: '60px' }}
                      onClick={() => handleAddFixedParamFee('slottingSalary', '开槽', processingParams.slottingSalary, '元/个', processingParams.slottingBasePrice)}
                    >
                      <div style={{ textAlign: 'center' }}>
                        <div style={{ fontWeight: 'bold' }}>开槽</div>
                        <div style={{ fontSize: '12px', color: '#666' }}>¥{processingParams.slottingSalary}/个</div>
                      </div>
                    </Button>
                  </Col>
                  <Col xs={24} sm={12} md={8} >
                    <Button
                      size="large"
                      style={{ width: '100%', height: '60px' }}
                      onClick={() => handleAddFixedParamFee('sprayCodeFee', '喷码费用', processingParams.sprayCodeFee, '元/个', processingParams.sprayCodeBasePrice)}
                    >
                      <div style={{ textAlign: 'center' }}>
                        <div style={{ fontWeight: 'bold' }}>喷码费用</div>
                        <div style={{ fontSize: '12px', color: '#666' }}>¥{processingParams.sprayCodeFee}/个</div>
                      </div>
                    </Button>
                  </Col>
                  <Col xs={24} sm={12} md={8}>
                    <Button
                      size="large"
                      style={{ width: '100%', height: '60px' }}
                      onClick={() => handleAddFixedParamFee('inspectionFee', '检验费用', processingParams.inspectionFee, '元/个', processingParams.inspectionBasePrice)}
                    >
                      <div style={{ textAlign: 'center' }}>
                        <div style={{ fontWeight: 'bold' }}>检验费用</div>
                        <div style={{ fontSize: '12px', color: '#666' }}>¥{processingParams.inspectionFee}/个</div>
                      </div>
                    </Button>
                  </Col>
                </Row>
              </div>
            )}



            {/* 已选择的固定选项加工费 */}
            {state.processingFeeConfig?.fixedFees && state.processingFeeConfig.fixedFees.length > 0 && (
              <div style={{ marginTop: 16 }}>
                <Text strong style={{ marginBottom: 8, display: 'block' }}>已选择：</Text>
                <Table
                  dataSource={fixedFeesDataSource}
                  columns={[
                    {
                      title: '名称',
                      dataIndex: 'name',
                      key: 'name',
                      align: 'center' as const,
                    },
                    {
                      title: '单价',
                      dataIndex: 'unitPrice',
                      key: 'unitPrice',
                      align: 'center' as const,
                      render: (price: number, record: ProcessingFeeItem) => (
                        <Text>¥{price.toFixed(2)}/{record.unit}</Text>
                      )
                    },
                    {
                      title: '数量',
                      dataIndex: 'quantity',
                      key: 'quantity',
                      align: 'center' as const,
                    },
                    {
                      title: '小计',
                      dataIndex: 'totalPrice',
                      key: 'totalPrice',
                      align: 'center' as const,
                      render: (price: number) => (
                        <Text strong style={{ color: '#52c41a' }}>¥{price.toFixed(2)}</Text>
                      )
                    },
                    {
                      title: '操作',
                      key: 'action',
                      align: 'center' as const,
                      render: (_: any, _record: any, index: number) => (
                        <Popconfirm
                          title="确定要删除吗？"
                          onConfirm={() => handleRemoveFixedFee(index)}
                          okText="确定"
                          cancelText="取消"
                        >
                          <Button
                            type="link"
                            size="small"
                            danger
                            icon={<DeleteOutlined />}
                          >
                            删除
                          </Button>
                        </Popconfirm>
                      )
                    }
                  ]}
                  pagination={false}
                  size="small"
                />
              </div>
            )}
          </Card>
        </Col>

        {/* 可选加工费 */}
        <Col span={24}>
          <Card
            title={
              <Space>
                <PlusOutlined />
                <span>可选加工费</span>
              </Space>
            }
            extra={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleOpenOptionalFeeModal}
              >
                添加可选加工费
              </Button>
            }
          >
            {/* 已选择的可选加工费 */}
            {state.processingFeeConfig?.customFees && state.processingFeeConfig.customFees.length > 0 ? (
              <Table
                dataSource={optionalFeesDataSource}
                columns={optionalFeeColumns}
                pagination={false}
                size="small"
              />
            ) : (
              <Text type="secondary">暂无可选加工费，点击"添加可选加工费"按钮选择</Text>
            )}
          </Card>
        </Col>
      </Row>

      {/* 加工费总费用显示 */}
      {((state.processingFeeConfig?.customFees?.length || 0) > 0 || (state.processingFeeConfig?.fixedFees?.length || 0) > 0) && (
        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <Title level={4}>
            加工费总费用: <Text type="success">¥{(state.processingFeeConfig?.processingFeeCost || 0).toFixed(2)}</Text>
          </Title>
        </div>
      )}

      {/* 可选加工费选择模态框 */}
      <Modal
        title="选择可选加工费"
        open={optionalFeeModalVisible}
        onCancel={() => setOptionalFeeModalVisible(false)}
        footer={null}
        width={800}
      >
        <Table
          dataSource={availableProcessingFees}
          columns={[
            {
              title: '名称',
              dataIndex: 'name',
              key: 'name',
            },
            {
              title: '单价',
              dataIndex: 'unitPrice',
              key: 'unitPrice',
              render: (price: number, record: ProcessingFee) => (
                <Text>¥{price.toFixed(2)}/{record.unit}</Text>
              )
            },
            {
              title: '起步价',
              dataIndex: 'basePrice',
              key: 'basePrice',
              render: (price: number) => <Text>¥{price.toFixed(2)}</Text>
            },
            {
              title: '备注',
              dataIndex: 'remark',
              key: 'remark',
              render: (remark: string) => remark || '-'
            },
            {
              title: '操作',
              key: 'action',
              render: (_: any, record: ProcessingFee) => (
                <Button
                  type="primary"
                  size="small"
                  onClick={() => handleAddOptionalFee(record)}
                >
                  选择
                </Button>
              )
            }
          ]}
          rowKey="id"
          pagination={false}
          size="small"
        />
      </Modal>

      {/* 可选加工费配置模态框 */}
      <Modal
        title={`添加可选加工费 - ${selectedOptionalFee?.name || ''}`}
        open={fixedFeeModalVisible}
        onOk={handleOptionalFeeConfirm}
        onCancel={() => {
          setFixedFeeModalVisible(false);
          // 重置状态
          setSelectedOptionalFee(null);
          setOptionalFeeLength(100);
          setOptionalFeeWidth(100);
        }}
        confirmLoading={loading}
        width={selectedOptionalFee?.unit === '元/平方' ? 600 : 500}
      >
        <Form
          form={optionalFeeForm}
          layout="vertical"
          preserve={false}
        >
          {/* 根据单位类型显示不同的说明 */}
          {selectedOptionalFee?.unit === '元/平方' && (
            <Alert
              message="面积计算说明"
              description={`请输入每个盒子的长度和宽度，系统将自动计算总面积（单个面积 × ${state.basicInfo?.quantity || 1}个盒子）`}
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
          )}
          {selectedOptionalFee?.unit === '元/个' && (
            <Alert
              message="数量计算说明"
              description={`默认数量为盒子数量（${state.basicInfo?.quantity || 1}个），您可以根据需要修改`}
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
          )}

          <Form.Item
            name="name"
            label="名称"
          >
            <Input disabled />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="unitPrice"
                label="单价"
              >
                <InputNumber
                  disabled
                  style={{ width: '100%' }}
                  addonAfter="元"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="unit"
                label="单位"
              >
                <Input disabled />
              </Form.Item>
            </Col>
          </Row>

          {/* 根据单位类型显示不同的输入界面 */}
          {selectedOptionalFee?.unit === '元/平方' ? (
            // 面积单位：显示长宽输入
            <>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="长度 (mm)" required>
                    <InputNumber
                      value={optionalFeeLength}
                      onChange={(value) => {
                        // 确保输入值有效，如果为空或无效则设为100
                        const newLength = value && !isNaN(value) && value > 0 ? value : 100;
                        setOptionalFeeLength(newLength);
                        // 延迟执行面积计算，确保状态已更新
                        setTimeout(() => updateAreaCalculation(), 0);
                      }}
                      min={0.1}
                      precision={1}
                      style={{ width: '100%' }}
                      placeholder="请输入长度"
                      addonAfter="mm"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="宽度 (mm)" required>
                    <InputNumber
                      value={optionalFeeWidth}
                      onChange={(value) => {
                        // 确保输入值有效，如果为空或无效则设为100
                        const newWidth = value && !isNaN(value) && value > 0 ? value : 100;
                        setOptionalFeeWidth(newWidth);
                        // 延迟执行面积计算，确保状态已更新
                        setTimeout(() => updateAreaCalculation(), 0);
                      }}
                      min={0.1}
                      precision={1}
                      style={{ width: '100%' }}
                      placeholder="请输入宽度"
                      addonAfter="mm"
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="quantity"
                    label={`总面积 (盒子数量: ${state.basicInfo?.quantity || 1}个)`}
                  >
                    <InputNumber
                      disabled
                      style={{ width: '100%' }}
                      addonAfter="㎡"
                      precision={6}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="basePrice"
                    label="起步价"
                  >
                    <InputNumber
                      disabled
                      style={{ width: '100%' }}
                      addonAfter="元"
                    />
                  </Form.Item>
                </Col>
              </Row>
            </>
          ) : (
            // 其他单位：显示数量输入
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="quantity"
                  label={selectedOptionalFee?.unit === '元/个' ? `数量 (默认: ${state.basicInfo?.quantity || 1}个)` : '数量'}
                  rules={[{ required: true, message: '请输入数量' }]}
                >
                  <InputNumber
                    placeholder="请输入数量"
                    min={0}
                    precision={2}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="basePrice"
                  label="起步价"
                >
                  <InputNumber
                    disabled
                    style={{ width: '100%' }}
                    addonAfter="元"
                  />
                </Form.Item>
              </Col>
            </Row>
          )}
        </Form>
      </Modal>
    </div>
  );
};

export default ProcessingFeeStep;
