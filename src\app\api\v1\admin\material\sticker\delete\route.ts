import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { deleteStickerSchema, DeleteStickerParams } from '@/lib/validations/admin/sticker';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation<DeleteStickerParams>(
  deleteStickerSchema,
  async (request: NextRequest, validatedQuery: DeleteStickerParams) => {
    const { id } = validatedQuery;

    // 检查不干胶是否存在
    const existingSticker = await prisma.sticker.findUnique({
      where: {
        id,
        isDel: false,
      },
    });

    assertExists(existingSticker, ErrorCode.MATERIAL_NOT_FOUND, '不干胶不存在');

    // 软删除不干胶
    const sticker = await prisma.sticker.update({
      where: {
        id,
      },
      data: {
        isDel: true,
        updatedAt: new Date(),
      },
    });

    return successResponse(sticker, '删除不干胶成功');
  }
); 