import { z } from 'zod';
import { UserRole, UserState } from '@/types/user';

// 手机号验证正则
const PHONE_REGEX = /^1[3-9]\d{9}$/;

// 邮箱验证正则
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// 密码验证正则（至少8位，包含字母和数字）
const PASSWORD_REGEX = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/;

// 用户角色验证
export const userRoleSchema = z.nativeEnum(UserRole, {
  errorMap: () => ({ message: '无效的用户角色' })
});

// 用户状态验证
export const userStateSchema = z.nativeEnum(UserState, {
  errorMap: () => ({ message: '无效的用户状态' })
});

// 用户登录验证
export const loginSchema = z.object({
  username: z.string()
    .min(1, '用户名不能为空')
    .max(100, '用户名长度不能超过100个字符'),
  password: z.string()
    .min(1, '密码不能为空')
    .max(255, '密码长度不能超过255个字符'),
  rememberMe: z.boolean().optional().default(false)
});

export type LoginParams = z.infer<typeof loginSchema>;

// 用户创建验证
export const createUserSchema = z.object({
  name: z.string()
    .min(1, '姓名不能为空')
    .max(100, '姓名长度不能超过100个字符')
    .trim(),
  phone: z.string()
    .regex(PHONE_REGEX, '请输入有效的手机号')
    .trim(),
  email: z.string()
    .regex(EMAIL_REGEX, '请输入有效的邮箱地址')
    .max(100, '邮箱长度不能超过100个字符')
    .trim()
    .optional()
    .or(z.literal('')),
  password: z.string()
    .regex(PASSWORD_REGEX, '密码至少8位，必须包含字母和数字')
    .max(255, '密码长度不能超过255个字符'),
  role: userRoleSchema,
  expiresAt: z.string()
    .datetime('请输入有效的日期时间格式')
    .optional()
    .or(z.literal('')),
  state: userStateSchema.optional().default(UserState.ENABLED)
}).refine((data) => {
  // 超级用户必须设置到期时间
  if (data.role === UserRole.SUPER_USER && !data.expiresAt) {
    return false;
  }
  return true;
}, {
  message: '超级用户必须设置到期时间',
  path: ['expiresAt']
}).refine((data) => {
  // 非超级用户不应该设置到期时间
  if (data.role !== UserRole.SUPER_USER && data.expiresAt) {
    return false;
  }
  return true;
}, {
  message: '只有超级用户可以设置到期时间',
  path: ['expiresAt']
});

export type CreateUserParams = z.infer<typeof createUserSchema>;

// 用户更新验证
export const updateUserSchema = z.object({
  id: z.number().int().positive('用户ID必须是正整数'),
  name: z.string()
    .min(1, '姓名不能为空')
    .max(100, '姓名长度不能超过100个字符')
    .trim()
    .optional(),
  phone: z.string()
    .regex(PHONE_REGEX, '请输入有效的手机号')
    .trim()
    .optional(),
  email: z.string()
    .regex(EMAIL_REGEX, '请输入有效的邮箱地址')
    .max(100, '邮箱长度不能超过100个字符')
    .trim()
    .optional()
    .or(z.literal(''))
    .or(z.null()),
  password: z.string()
    .regex(PASSWORD_REGEX, '密码至少8位，必须包含字母和数字')
    .max(255, '密码长度不能超过255个字符')
    .optional(),
  role: userRoleSchema.optional(),
  expiresAt: z.string()
    .datetime('请输入有效的日期时间格式')
    .optional()
    .or(z.literal(''))
    .or(z.null()),
  state: userStateSchema.optional()
}).refine((data) => {
  // 如果设置了角色，检查超级用户的到期时间
  if (data.role === UserRole.SUPER_USER && !data.expiresAt) {
    return false;
  }
  return true;
}, {
  message: '超级用户必须设置到期时间',
  path: ['expiresAt']
}).refine((data) => {
  // 如果设置了角色，检查非超级用户不应该有到期时间
  if (data.role && data.role !== UserRole.SUPER_USER && data.expiresAt) {
    return false;
  }
  return true;
}, {
  message: '只有超级用户可以设置到期时间',
  path: ['expiresAt']
});

export type UpdateUserParams = z.infer<typeof updateUserSchema>;

// 用户查询验证
export const queryUserSchema = z.object({
  page: z.number().int().min(1, '页码必须大于0').default(1),
  pageSize: z.number().int().min(1, '每页数量必须大于0').max(100, '每页数量不能超过100').default(10),
  keyword: z.string().max(100, '关键词长度不能超过100个字符').optional(),
  role: userRoleSchema.optional(),
  state: userStateSchema.optional(),
  phone: z.string()
    .max(20, '手机号长度不能超过20个字符')
    .optional(),
  email: z.string()
    .max(100, '邮箱长度不能超过100个字符')
    .optional(),
  name: z.string()
    .max(100, '姓名长度不能超过100个字符')
    .optional(),
  startTime: z.string()
    .datetime('请输入有效的开始时间格式')
    .optional(),
  endTime: z.string()
    .datetime('请输入有效的结束时间格式')
    .optional()
});

export type QueryUserParams = z.infer<typeof queryUserSchema>;

// 用户状态更新验证
export const updateUserStateSchema = z.object({
  id: z.number().int().positive('用户ID必须是正整数'),
  state: userStateSchema
});

export type UpdateUserStateParams = z.infer<typeof updateUserStateSchema>;

// 用户删除验证
export const deleteUserSchema = z.object({
  id: z.number().int().positive('用户ID必须是正整数')
});

export type DeleteUserParams = z.infer<typeof deleteUserSchema>;

// 密码修改验证
export const changePasswordSchema = z.object({
  oldPassword: z.string()
    .min(1, '原密码不能为空')
    .max(255, '原密码长度不能超过255个字符'),
  newPassword: z.string()
    .regex(PASSWORD_REGEX, '新密码至少8位，必须包含字母和数字')
    .max(255, '新密码长度不能超过255个字符'),
  confirmPassword: z.string()
    .min(1, '确认密码不能为空')
    .max(255, '确认密码长度不能超过255个字符')
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword']
}).refine((data) => data.oldPassword !== data.newPassword, {
  message: '新密码不能与原密码相同',
  path: ['newPassword']
});

export type ChangePasswordParams = z.infer<typeof changePasswordSchema>;
