import { z } from 'zod';
import { SURFACE_PROCESS_UNITS } from '@/types/craftSalary';

// 覆膜工艺表单验证模式
export const surfaceProcessFormSchema = z.object({
  name: z.string()
    .min(1, '请输入覆膜工艺名称')
    .max(100, '名称长度不能超过100字符'),
  
  price: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '价格必须是大于等于0的数字'),
  
  unit: z.enum(SURFACE_PROCESS_UNITS, {
    required_error: '请选择计价单位',
    invalid_type_error: '无效的计价单位',
  }),
  
  basePrice: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '起步价必须是大于等于0的数字'),
  
  thickness: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '厚度必须是大于等于0的数字'),
  
  density: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '密度必须是大于等于0的数字'),
  
  remark: z.string()
    .max(500, '备注长度不能超过500字符')
    .optional()
    .or(z.literal(''))
});

// 覆膜工艺创建请求验证模式
export const createSurfaceProcessSchema = z.object({
  name: z.string().min(1).max(100),
  price: z.number().min(0),
  unit: z.enum(SURFACE_PROCESS_UNITS),
  basePrice: z.number().min(0),
  thickness: z.number().min(0),
  density: z.number().min(0),
  remark: z.string().max(500).optional()
});

// 覆膜工艺更新请求验证模式
export const updateSurfaceProcessSchema = createSurfaceProcessSchema.extend({
  id: z.number().int().positive()
});

// 覆膜工艺列表查询参数验证模式
export const surfaceProcessListParamsSchema = z.object({
  page: z.number().int().positive().optional(),
  pageSize: z.number().int().positive().max(100).optional(),
  search: z.string().optional(),
  unit: z.enum(SURFACE_PROCESS_UNITS).optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
});

// 表单数据转换函数
export function transformSurfaceProcessFormData(formData: z.infer<typeof surfaceProcessFormSchema>) {
  return {
    name: formData.name,
    price: Number(formData.price),
    unit: formData.unit,
    basePrice: Number(formData.basePrice),
    thickness: Number(formData.thickness),
    density: Number(formData.density),
    remark: formData.remark || undefined
  };
}

export type SurfaceProcessFormData = z.infer<typeof surfaceProcessFormSchema>;
export type CreateSurfaceProcessData = z.infer<typeof createSurfaceProcessSchema>;
export type UpdateSurfaceProcessData = z.infer<typeof updateSurfaceProcessSchema>; 