/**
 * 材料处理工具函数
 * 提供材料相关的通用处理逻辑
 */

import { PartMaterialConfig } from '../types/calculation';
import { PartGroup } from '../types/packaging';

/**
 * 材料价格信息接口
 */
export interface MaterialPriceInfo {
  id: number;
  name: string;
  price: number;
  unit: string;
  category?: string;
  weight?: number;  // 克重，用于按吨计价的计算
  thickness?: number; // 厚度
}

/**
 * 材料信息接口
 */
export interface MaterialInfo {
  name: string;
  spec: string;
  size: string;
  printingMachine?: string;
}

/**
 * 材料ID引用接口
 */
export interface MaterialIdReference {
  paperId?: number;
  specialPaperId?: number;
  greyBoardId?: number;
}

/**
 * 生成材料价格的唯一键
 * @param materialCategory 材料分类
 * @param materialId 材料ID
 * @returns 唯一键字符串
 */
export const getMaterialPriceKey = (materialCategory: string, materialId: number): string => {
  return `${materialCategory}_${materialId}`;
};

/**
 * 根据配置和部件组确定材料分类
 * @param config 部件材料配置
 * @param partGroup 部件组（可选，用于推断）
 * @param greyPartGroups 灰板部件组列表（用于判断）
 * @returns 材料分类
 */
export const determineMaterialCategory = (
  config: PartMaterialConfig,
  partGroup?: PartGroup,
  greyPartGroups: PartGroup[] = []
): string => {
  // 如果配置中已明确指定分类，直接使用
  if (config.materialCategory) {
    return config.materialCategory;
  }

  // 根据部件组类型判断
  if (partGroup && greyPartGroups.some(g => g.id === partGroup.id)) {
    return 'greyBoard';
  }

  // 默认为纸张
  return 'paper';
};

/**
 * 构建材料信息对象
 * @param materialConfig 材料配置
 * @param materialType 材料类型（用于显示）
 * @returns 材料信息对象
 */
export const buildMaterialInfo = (
  materialConfig: PartMaterialConfig,
  materialType: 'face' | 'grey' = 'face'
): MaterialInfo => {
  return {
    name: materialConfig.materialName || '未选择',
    spec: materialConfig.materialSpec || '未指定',
    size: materialConfig.materialSize
      ? `${materialConfig.materialSize.width}×${materialConfig.materialSize.height}mm`
      : '未指定',
    printingMachine: materialType === 'face' ? materialConfig.printingMachineName : undefined
  };
};

/**
 * 从部件材料配置中提取材料ID引用
 * @param partMaterialConfigs 部件材料配置
 * @returns 材料ID引用数组
 */
export const extractMaterialIdReferences = (
  partMaterialConfigs: Record<string, PartMaterialConfig>
): MaterialIdReference[] => {
  const materialIds: MaterialIdReference[] = [];

  Object.values(partMaterialConfigs).forEach(config => {
    if (config.materialId) {
      const materialRef: MaterialIdReference = {};
      
      const category = config.materialCategory || 'paper';
      
      switch (category) {
        case 'paper':
          materialRef.paperId = config.materialId;
          break;
        case 'specialPaper':
          materialRef.specialPaperId = config.materialId;
          break;
        case 'greyBoard':
          materialRef.greyBoardId = config.materialId;
          break;
        case 'corrugated':
          // 瓦楞材料不需要从API获取价格，使用配置中的结构价格
          break;
        default:
          // 默认作为纸类材料处理
          materialRef.paperId = config.materialId;
          break;
      }
      
      // 只添加有效的引用
      if (Object.keys(materialRef).length > 0) {
        materialIds.push(materialRef);
      }
    }
  });

  return materialIds;
};

/**
 * 验证材料配置是否完整
 * @param config 材料配置
 * @returns 是否完整
 */
export const validateMaterialConfig = (config: PartMaterialConfig): boolean => {
  return !!(config.materialId && config.materialName);
};

/**
 * 检查材料是否为瓦楞材料
 * @param config 材料配置
 * @returns 是否为瓦楞材料
 */
export const isCorrugatedMaterial = (config: PartMaterialConfig): boolean => {
  return config.materialCategory === 'corrugated';
};

/**
 * 获取瓦楞材料的结构价格
 * @param config 材料配置
 * @param materialName 材料名称（用于匹配）
 * @param partMaterialConfigs 所有部件材料配置
 * @returns 结构价格，如果找不到则返回0
 */
export const getCorrugatedStructurePrice = (
  config: PartMaterialConfig,
  materialName: string,
  partMaterialConfigs: Record<string, PartMaterialConfig>
): number => {
  if (!isCorrugatedMaterial(config)) {
    return 0;
  }

  // 从配置中获取结构价格，使用面纸/里纸组合匹配
  const matchingConfig = Object.values(partMaterialConfigs).find(c =>
    c.materialCategory === 'corrugated' &&
    c.facePaper && c.linerPaper && c.structure &&
    materialName.includes(c.facePaper) &&
    materialName.includes(c.linerPaper)
  );

  return matchingConfig?.structurePrice || 0;
};

/**
 * 按材料分组统计
 * @param partMaterialConfigs 部件材料配置
 * @param impositionResults 拼版结果（可选，用于统计张数）
 * @returns 按材料分组的统计信息
 */
export const groupByMaterial = (
  partMaterialConfigs: Record<string, PartMaterialConfig>,
  impositionResults?: Array<{ partGroup: { id: string }; sheetsNeeded: number; materialArea: number }>
): Record<string, {
  materialType: string;
  materialName: string;
  specification: string;
  materialId: number;
  materialCategory: string;
  totalSheets: number;
  totalMaterialArea: number;
  partGroups: string[];
  impositionResults: any[];
}> => {
  const materialGroups: Record<string, any> = {};

  Object.entries(partMaterialConfigs).forEach(([partGroupId, config]) => {
    if (!validateMaterialConfig(config)) {
      return;
    }

    const materialCategory = determineMaterialCategory(config);
    const key = getMaterialPriceKey(materialCategory, config.materialId!);

    if (!materialGroups[key]) {
      materialGroups[key] = {
        materialType: getMaterialTypeDisplayName(materialCategory),
        materialName: config.materialName!,
        specification: config.materialSpec || '未指定',
        materialId: config.materialId!,
        materialCategory,
        totalSheets: 0,
        totalMaterialArea: 0,
        partGroups: [],
        impositionResults: []
      };
    }

    // 如果有拼版结果，添加统计信息
    if (impositionResults) {
      const impositionResult = impositionResults.find(r => r.partGroup.id === partGroupId);
      if (impositionResult) {
        materialGroups[key].totalSheets += impositionResult.sheetsNeeded;
        materialGroups[key].totalMaterialArea += impositionResult.materialArea * impositionResult.sheetsNeeded;
        materialGroups[key].impositionResults.push(impositionResult);
      }
    }

    // 添加部件组名称（避免重复）
    if (!materialGroups[key].partGroups.includes(partGroupId)) {
      materialGroups[key].partGroups.push(partGroupId);
    }
  });

  return materialGroups;
};

/**
 * 获取材料类型的中文显示名称
 * @param materialCategory 材料分类
 * @returns 中文显示名称
 */
const getMaterialTypeDisplayName = (materialCategory: string): string => {
  const typeMap: Record<string, string> = {
    'paper': '纸张',
    'specialPaper': '特种纸',
    'greyBoard': '灰板',
    'corrugated': '瓦楞'
  };
  return typeMap[materialCategory] || materialCategory;
};
