import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { successResponse } from '@/lib/utils/apiResponse';
import { MaterialSizeType, MaterialSizeConfig } from '@/types/material';

export async function POST(request: NextRequest) {
  try {
    // 默认配置值
    const defaultConfig: MaterialSizeConfig = {
      regularLength: 1092,
      regularWidth: 787,
      largeLength: 1194,
      largeWidth: 889,
      specialLength: 787,
      specialWidth: 1092,
    };

    // 批量重置所有尺寸为默认值
    const resetPromises = [
      prisma.materialSize.updateMany({
        where: { type: MaterialSizeType.REGULAR_LENGTH },
        data: { size: defaultConfig.regularLength, updatedAt: new Date() },
      }),
      prisma.materialSize.updateMany({
        where: { type: MaterialSizeType.REGULAR_WIDTH },
        data: { size: defaultConfig.regularWidth, updatedAt: new Date() },
      }),
      prisma.materialSize.updateMany({
        where: { type: MaterialSizeType.LARGE_LENGTH },
        data: { size: defaultConfig.largeLength, updatedAt: new Date() },
      }),
      prisma.materialSize.updateMany({
        where: { type: MaterialSizeType.LARGE_WIDTH },
        data: { size: defaultConfig.largeWidth, updatedAt: new Date() },
      }),
      prisma.materialSize.updateMany({
        where: { type: MaterialSizeType.SPECIAL_LENGTH },
        data: { size: defaultConfig.specialLength, updatedAt: new Date() },
      }),
      prisma.materialSize.updateMany({
        where: { type: MaterialSizeType.SPECIAL_WIDTH },
        data: { size: defaultConfig.specialWidth, updatedAt: new Date() },
      }),
    ];

    await Promise.all(resetPromises);

    return successResponse(defaultConfig, '材料尺寸已重置为默认值');

  } catch (error) {
    console.error('重置材料尺寸失败:', error);
    return Response.json(
      {
        code: 500,
        message: '重置材料尺寸失败',
        data: null,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
} 