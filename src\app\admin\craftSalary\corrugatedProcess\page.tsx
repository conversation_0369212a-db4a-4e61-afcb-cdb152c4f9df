'use client';

import React, { useState, useEffect } from 'react';
import {
  Table, Button, Space, Modal, Form, Input,
  Select, InputNumber, Popconfirm, Card, Typography,
  message, Row, Col, Tag, Tabs
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined,
  SearchOutlined, ReloadOutlined
} from '@ant-design/icons';
import { corrugatedProcessApi, corrugatedRateApi } from '@/services/adminApi';
import {
  CorrugatedProcess,
  CorrugatedRate,
  FLUTE_TYPES,
  CORRUGATED_PROCESS_UNITS,
  FluteType
} from '@/types/craftSalary';
import { useAsyncError } from '@/lib/hooks/useErrorHandler';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

/**
 * 瓦楞工艺管理页面
 */
export default function CorrugatedProcessManagementPage() {
  // 错误处理Hook
  const { execute: executeCorrugatedProcess, loading: corrugatedProcessLoading } = useAsyncError();
  const { execute: executeCorrugatedRate, loading: corrugatedRateLoading } = useAsyncError();

  // Tab状态
  const [activeTab, setActiveTab] = useState('process');

  // 瓦楞工艺数据相关状态
  const [processList, setProcessList] = useState<CorrugatedProcess[]>([]);
  const [processTotal, setProcessTotal] = useState(0);
  const [processCurrent, setProcessCurrent] = useState(1);
  const [processPageSize, setProcessPageSize] = useState(10);
  const [processKeyword, setProcessKeyword] = useState('');

  // 瓦楞率配置数据相关状态
  const [rateList, setRateList] = useState<CorrugatedRate[]>([]);
  const [rateTotal, setRateTotal] = useState(0);
  const [rateCurrent, setRateCurrent] = useState(1);
  const [ratePageSize, setRatePageSize] = useState(10);

  // 瓦楞工艺模态框相关状态
  const [processModalVisible, setProcessModalVisible] = useState(false);
  const [processModalTitle, setProcessModalTitle] = useState('');
  const [editingProcessRecord, setEditingProcessRecord] = useState<any>(null);
  const [processForm] = Form.useForm();

  // 瓦楞率配置模态框相关状态
  const [rateModalVisible, setRateModalVisible] = useState(false);
  const [rateModalTitle, setRateModalTitle] = useState('');
  const [editingRateRecord, setEditingRateRecord] = useState<any>(null);
  const [rateForm] = Form.useForm();

  // 初始加载数据
  useEffect(() => {
    fetchProcessList();
    fetchRateList();
  }, []);

  // 获取瓦楞工艺数据列表
  const fetchProcessList = async (page = processCurrent, pageSize_ = processPageSize, search = processKeyword) => {
    const requestParams: any = {
      page,
      pageSize: pageSize_,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    };

    if (search) {
      requestParams.search = search;
    }

    const result = await executeCorrugatedProcess(async () => {
      return await corrugatedProcessApi.getList(requestParams);
    }, '获取瓦楞工艺列表');

    if (result) {
      setProcessList(result.list || []);
      setProcessTotal(result.pagination?.total || 0);
    } else {
      setProcessList([]);
      setProcessTotal(0);
    }
  };

  // 获取瓦楞率配置列表
  const fetchRateList = async (page = rateCurrent, pageSize_ = ratePageSize) => {
    const requestParams: any = {
      page,
      pageSize: pageSize_,
      sortBy: 'fluteType',
      sortOrder: 'asc'
    };

    const result = await executeCorrugatedRate(async () => {
      return await corrugatedRateApi.getList(requestParams);
    }, '获取瓦楞率配置列表');

    if (result) {
      setRateList(result.list || []);
      setRateTotal(result.pagination?.total || 0);
    } else {
      setRateList([]);
      setRateTotal(0);
    }
  };

  // 处理瓦楞工艺分页变化
  const handleProcessTableChange = (pagination: any) => {
    setProcessCurrent(pagination.current);
    setProcessPageSize(pagination.pageSize);
    fetchProcessList(pagination.current, pagination.pageSize);
  };

  // 处理瓦楞率配置分页变化
  const handleRateTableChange = (pagination: any) => {
    setRateCurrent(pagination.current);
    setRatePageSize(pagination.pageSize);
    fetchRateList(pagination.current, pagination.pageSize);
  };

  // 打开添加瓦楞工艺模态框
  const showAddProcessModal = () => {
    setProcessModalTitle('添加瓦楞工艺');
    setEditingProcessRecord(null);
    processForm.resetFields();
    processForm.setFieldsValue({
      price: 0,
      unit: '元/平方',
      setupFee: 0,
      thickness: 0,
      coreWeight1: 0,
      linerWeight1: 0,
      coreWeight2: 0,
      linerWeight2: 0
    });
    setProcessModalVisible(true);
  };

  // 打开编辑瓦楞工艺模态框
  const showEditProcessModal = (record: CorrugatedProcess) => {
    setProcessModalTitle('编辑瓦楞工艺');
    setEditingProcessRecord(record);
    processForm.setFieldsValue({
      code: record.code || '',
      materialName: record.materialName,
      price: record.price,
      unit: record.unit,
      setupFee: record.setupFee,
      thickness: record.thickness,
      coreWeight1: record.coreWeight1,
      fluteType1: record.fluteType1 || undefined,
      linerWeight1: record.linerWeight1,
      coreWeight2: record.coreWeight2,
      fluteType2: record.fluteType2 || undefined,
      linerWeight2: record.linerWeight2,
      remark: record.remark || ''
    });
    setProcessModalVisible(true);
  };

  // 处理瓦楞工艺表单提交
  const handleProcessFormSubmit = async () => {
    try {
      const values = await processForm.validateFields();

      const requestData = {
        code: values.code || undefined,
        materialName: values.materialName,
        price: Number(values.price),
        unit: values.unit,
        setupFee: Number(values.setupFee),
        thickness: Number(values.thickness),
        coreWeight1: Number(values.coreWeight1),
        fluteType1: values.fluteType1 || undefined,
        linerWeight1: Number(values.linerWeight1),
        coreWeight2: Number(values.coreWeight2),
        fluteType2: values.fluteType2 || undefined,
        linerWeight2: Number(values.linerWeight2),
        remark: values.remark || undefined
      };

      const result = await executeCorrugatedProcess(async () => {
        if (editingProcessRecord) {
          return await corrugatedProcessApi.update({ ...requestData, id: editingProcessRecord.id });
        } else {
          return await corrugatedProcessApi.create(requestData);
        }
      }, editingProcessRecord ? '更新瓦楞工艺' : '创建瓦楞工艺');

      if (result) {
        message.success('保存成功');
        setProcessModalVisible(false);
        fetchProcessList();
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 删除瓦楞工艺
  const handleProcessDelete = async (id: number) => {
    const result = await executeCorrugatedProcess(async () => {
      return await corrugatedProcessApi.delete(id);
    }, '删除瓦楞工艺');

    if (result) {
      fetchProcessList();
    }
  };

  // 打开添加瓦楞率配置模态框
  const showAddRateModal = () => {
    setRateModalTitle('添加瓦楞率配置');
    setEditingRateRecord(null);
    rateForm.resetFields();
    rateForm.setFieldsValue({
      rate: 0
    });
    setRateModalVisible(true);
  };

  // 打开编辑瓦楞率配置模态框
  const showEditRateModal = (record: CorrugatedRate) => {
    setRateModalTitle('编辑瓦楞率配置');
    setEditingRateRecord(record);
    rateForm.setFieldsValue({
      fluteType: record.fluteType,
      rate: record.rate
    });
    setRateModalVisible(true);
  };

  // 处理瓦楞率配置表单提交
  const handleRateFormSubmit = async () => {
    try {
      const values = await rateForm.validateFields();

      const requestData = {
        fluteType: values.fluteType,
        rate: Number(values.rate)
      };

      const result = await executeCorrugatedRate(async () => {
        if (editingRateRecord) {
          return await corrugatedRateApi.update({ ...requestData, id: editingRateRecord.id });
        } else {
          return await corrugatedRateApi.create(requestData);
        }
      }, editingRateRecord ? '更新瓦楞率配置' : '创建瓦楞率配置');

      if (result) {
        message.success('保存成功');
        setRateModalVisible(false);
        fetchRateList();
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 删除瓦楞率配置
  const handleRateDelete = async (id: number) => {
    const result = await executeCorrugatedRate(async () => {
      return await corrugatedRateApi.delete(id);
    }, '删除瓦楞率配置');

    if (result) {
      fetchRateList();
    }
  };

  // 瓦楞工艺表格列定义
  const processColumns = [
    {
      title: '代号',
      dataIndex: 'code',
      key: 'code',
      align: 'center' as const,
      width: 100,
      render: (code: string) => code || '-'
    },
    {
      title: '材质名称',
      dataIndex: 'materialName',
      key: 'materialName',
      align: 'center' as const,
      width: 150,
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      align: 'center' as const,
      width: 100,
      render: (price: number) => `¥${price.toFixed(2)}`
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      align: 'center' as const,
      width: 80,
      render: (unit: string) => (
        <Tag color="green">{unit}</Tag>
      )
    },
    {
      title: '上机费',
      dataIndex: 'setupFee',
      key: 'setupFee',
      align: 'center' as const,
      width: 100,
      render: (fee: number) => fee > 0 ? `¥${fee.toFixed(2)}` : '-'
    },
    {
      title: '厚度(mm)',
      dataIndex: 'thickness',
      key: 'thickness',
      align: 'center' as const,
      width: 100,
      render: (thickness: number) => thickness > 0 ? `${thickness}mm` : '-'
    },
    {
      title: '芯纸1',
      dataIndex: 'coreWeight1',
      key: 'coreWeight1',
      align: 'center' as const,
      width: 80,
      render: (weight: number) => weight > 0 ? weight : '-'
    },
    {
      title: '楞形1',
      dataIndex: 'fluteType1',
      key: 'fluteType1',
      align: 'center' as const,
      width: 80,
      render: (type: string) => type ? <Tag color="blue">{type}</Tag> : '-'
    },
    {
      title: '里纸1',
      dataIndex: 'linerWeight1',
      key: 'linerWeight1',
      align: 'center' as const,
      width: 80,
      render: (weight: number) => weight > 0 ? weight : '-'
    },
    {
      title: '芯纸2',
      dataIndex: 'coreWeight2',
      key: 'coreWeight2',
      align: 'center' as const,
      width: 80,
      render: (weight: number) => weight > 0 ? weight : '-'
    },
    {
      title: '楞形2',
      dataIndex: 'fluteType2',
      key: 'fluteType2',
      align: 'center' as const,
      width: 80,
      render: (type: string) => type ? <Tag color="blue">{type}</Tag> : '-'
    },
    {
      title: '里纸2',
      dataIndex: 'linerWeight2',
      key: 'linerWeight2',
      align: 'center' as const,
      width: 80,
      render: (weight: number) => weight > 0 ? weight : '-'
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      align: 'center' as const,
      width: 150,
      ellipsis: true,
      render: (remark: string) => remark || '-'
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      align: 'center' as const,
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'action',
      align: 'center' as const,
      fixed: 'right' as const,
      width: 150,
      render: (_: any, record: CorrugatedProcess) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => showEditProcessModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此瓦楞工艺吗？"
            onConfirm={() => handleProcessDelete(record.id)}
            okText="是"
            cancelText="否"
          >
            <Button
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 瓦楞率配置表格列定义
  const rateColumns = [
    {
      title: '楞形',
      dataIndex: 'fluteType',
      key: 'fluteType',
      align: 'center' as const,
      width: 100,
      render: (type: string) => <Tag color="blue" style={{ fontSize: '14px' }}>{type}</Tag>
    },
    {
      title: '瓦楞率',
      dataIndex: 'rate',
      key: 'rate',
      align: 'center' as const,
      width: 150,
      render: (rate: number) => rate.toFixed(4)
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      align: 'center' as const,
      width: 180,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'action',
      align: 'center' as const,
      fixed: 'right' as const,
      width: 150,
      render: (_: any, record: CorrugatedRate) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => showEditRateModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此瓦楞率配置吗？"
            onConfirm={() => handleRateDelete(record.id)}
            okText="是"
            cancelText="否"
          >
            <Button
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // Tab内容
  const tabItems = [
    {
      key: 'process',
      label: '瓦楞工艺',
      children: (
        <Card>
          <Row gutter={16} align="middle" style={{ marginBottom: 16 }}>
            <Col span={4}>
              <Input
                placeholder="搜索代号、材质名称或备注"
                prefix={<SearchOutlined />}
                allowClear
                value={processKeyword}
                onChange={(e) => setProcessKeyword(e.target.value)}
                onPressEnter={() => fetchProcessList(1, processPageSize, processKeyword)}
              />
            </Col>
            <Col>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={() => fetchProcessList(1, processPageSize, processKeyword)}
              >
                搜索
              </Button>
            </Col>
            <Col>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => {
                  setProcessKeyword('');
                  fetchProcessList(1, processPageSize, '');
                }}
              >
                重置
              </Button>
            </Col>
            <Col flex="auto" style={{ textAlign: 'right' }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={showAddProcessModal}
              >
                添加瓦楞工艺
              </Button>
            </Col>
          </Row>

          <Table
            columns={processColumns}
            dataSource={processList}
            rowKey="id"
            pagination={{
              current: processCurrent,
              pageSize: processPageSize,
              total: processTotal,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条`,
            }}
            loading={corrugatedProcessLoading}
            onChange={handleProcessTableChange}
            bordered
            size="middle"
            scroll={{ x: 1400 }}
            locale={{ emptyText: '暂无数据' }}
          />
        </Card>
      )
    },
    {
      key: 'rate',
      label: '瓦楞率配置',
      children: (
        <Card>
          <div style={{ marginBottom: 16 }}>
            <Row gutter={16} align="middle">
              <Col flex="auto" style={{ textAlign: 'right' }}>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={showAddRateModal}
                >
                  添加瓦楞率配置
                </Button>
              </Col>
            </Row>
          </div>

          <Table
            columns={rateColumns}
            dataSource={rateList}
            rowKey="id"
            pagination={{
              current: rateCurrent,
              pageSize: ratePageSize,
              total: rateTotal,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条`,
            }}
            loading={corrugatedRateLoading}
            onChange={handleRateTableChange}
            bordered
            size="middle"
            locale={{ emptyText: '暂无数据' }}
          />
        </Card>
      )
    }
  ];

  return (
    <div>
      <Title level={2}>瓦楞工艺管理</Title>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
      />

      {/* 瓦楞工艺表单模态框 */}
      <Modal
        title={processModalTitle}
        open={processModalVisible}
        onOk={handleProcessFormSubmit}
        onCancel={() => setProcessModalVisible(false)}
        width={800}
        okText="确定"
        cancelText="取消"
      >
        <Form
          form={processForm}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="code"
                label="代号"
              >
                <Input placeholder="请输入代号（可选）" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="materialName"
                label="材质名称"
                rules={[
                  { required: true, message: '请输入材质名称' },
                  { max: 100, message: '名称长度不能超过100字符' }
                ]}
              >
                <Input placeholder="请输入材质名称" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="price"
                label="价格"
                rules={[{ required: true, message: '请输入价格' }]}
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="0.00"
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="unit"
                label="计价单位"
                rules={[{ required: true, message: '请选择计价单位' }]}
              >
                <Select placeholder="请选择计价单位">
                  {CORRUGATED_PROCESS_UNITS.map(unit => (
                    <Option key={unit} value={unit}>{unit}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="setupFee"
                label="上机费"
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="0.00"
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="thickness"
                label="厚度(mm)"
                rules={[{ required: true, message: '请输入厚度' }]}
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="0.00"
                  addonAfter="mm"
                />
              </Form.Item>
            </Col>
          </Row>

          <Card title="材料克重配置" size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="coreWeight1"
                  label="芯纸1"
                >
                  <InputNumber
                    min={0}
                    precision={2}
                    style={{ width: '100%' }}
                    placeholder="0.00"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="fluteType1"
                  label="楞形1"
                >
                  <Select placeholder="请选择楞形" allowClear>
                    {FLUTE_TYPES.map(type => (
                      <Option key={type} value={type}>{type}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="linerWeight1"
                  label="里纸1"
                >
                  <InputNumber
                    min={0}
                    precision={2}
                    style={{ width: '100%' }}
                    placeholder="0.00"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="coreWeight2"
                  label="芯纸2"
                >
                  <InputNumber
                    min={0}
                    precision={2}
                    style={{ width: '100%' }}
                    placeholder="0.00"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="fluteType2"
                  label="楞形2"
                >
                  <Select placeholder="请选择楞形" allowClear>
                    {FLUTE_TYPES.map(type => (
                      <Option key={type} value={type}>{type}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="linerWeight2"
                  label="里纸2"
                >
                  <InputNumber
                    min={0}
                    precision={2}
                    style={{ width: '100%' }}
                    placeholder="0.00"
                  />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          <Form.Item
            name="remark"
            label="备注"
          >
            <TextArea
              placeholder="请输入备注信息（可选）"
              rows={3}
              showCount
              maxLength={500}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 瓦楞率配置表单模态框 */}
      <Modal
        title={rateModalTitle}
        open={rateModalVisible}
        onOk={handleRateFormSubmit}
        onCancel={() => setRateModalVisible(false)}
        width={400}
        okText="确定"
        cancelText="取消"
      >
        <Form
          form={rateForm}
          layout="vertical"
        >
          <Form.Item
            name="fluteType"
            label="楞形"
            rules={[{ required: true, message: '请选择楞形' }]}
          >
            <Select placeholder="请选择楞形">
              {FLUTE_TYPES.map(type => (
                <Option key={type} value={type}>{type}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="rate"
            label="瓦楞率"
            rules={[{ required: true, message: '请输入瓦楞率' }]}
          >
            <InputNumber
              min={0}
              precision={4}
              style={{ width: '100%' }}
              placeholder="0.0000"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
} 