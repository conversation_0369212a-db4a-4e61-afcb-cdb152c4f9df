import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getStickerDetailSchema, GetStickerDetailParams } from '@/lib/validations/admin/sticker';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation<GetStickerDetailParams>(
  getStickerDetailSchema,
  async (request: AuthenticatedRequest, validatedQuery: GetStickerDetailParams) => {
    const { id } = validatedQuery;

    // 查询不干胶详情
    const sticker = await prisma.sticker.findUnique({
      where: {
        id,
        isDel: false,
      },
    });

    // 检查不干胶是否存在
    assertExists(sticker, ErrorCode.MATERIAL_NOT_FOUND, '不干胶不存在');

    return successResponse(sticker, '获取不干胶详情成功');
  }
); 
export const POST = withInternalAuth(handler);