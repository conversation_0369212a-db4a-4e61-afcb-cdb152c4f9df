import { NextRequest } from 'next/server';
import { updateGiftBoxAccessorySchema, UpdateGiftBoxAccessoryParams, validateStockSize } from '@/lib/validations/admin/giftBoxAccessory';
import { prisma } from '@/lib/prisma';
import { withValidation, assertExists, assert } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation<UpdateGiftBoxAccessoryParams>(
  updateGiftBoxAccessorySchema,
  async (request: AuthenticatedRequest, validatedData: UpdateGiftBoxAccessoryParams) => {
    const { id, name, price, unit, isStockSize, stockLength, stockWidth, remark } = validatedData;

    // 现货尺寸验证
    const stockSizeValidation = validateStockSize(validatedData);
    assert(stockSizeValidation.success, ErrorCode.INVALID_PARAMETERS, stockSizeValidation.error);

    // 检查礼盒配件是否存在
    const existingGiftBoxAccessory = await prisma.giftBoxAccessory.findFirst({
      where: {
        id,
        isDel: false,
      },
    });

    assertExists(existingGiftBoxAccessory, ErrorCode.MATERIAL_NOT_FOUND, '礼盒配件不存在');

    // 检查名称是否与其他礼盒配件重复
    const duplicateGiftBoxAccessory = await prisma.giftBoxAccessory.findFirst({
      where: {
        name,
        id: { not: id },
        isDel: false,
      },
    });

    assert(!duplicateGiftBoxAccessory, ErrorCode.MATERIAL_NAME_EXISTS, '礼盒配件名称已存在');

    // 更新礼盒配件
    const giftBoxAccessory = await prisma.giftBoxAccessory.update({
      where: { id },
      data: {
        name,
        price,
        unit,
        isStockSize,
        stockLength: isStockSize ? stockLength : null,
        stockWidth: isStockSize ? stockWidth : null,
        remark: remark || null,
      },
    });

    return successResponse(giftBoxAccessory, '更新礼盒配件成功');
  }
); 
export const POST = withInternalAuth(handler);