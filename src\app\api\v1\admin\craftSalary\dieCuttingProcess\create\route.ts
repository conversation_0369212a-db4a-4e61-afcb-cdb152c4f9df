import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { CreateDieCuttingProcessData, createDieCuttingProcessSchema } from '@/lib/validations/admin/dieCuttingProcess';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation(
  createDieCuttingProcessSchema,
  async (request: NextRequest, validatedData: CreateDieCuttingProcessData) => {
    const data = validatedData;

    // 检查模切工艺名称是否重复
    const existingProcess = await prisma.dieCuttingProcess.findFirst({
      where: {
        name: data.name,
        isDel: false,
      },
    });

    assert(!existingProcess, ErrorCode.DUPLICATE_ENTRY, '模切工艺名称已存在');

    // 创建模切工艺数据
    const dieCuttingProcess = await prisma.dieCuttingProcess.create({
      data: {
        name: data.name,
        price: data.price,
        unit: data.unit,
        basePrice: data.basePrice,
        remark: data.remark,
      },
    });

    return successResponse(dieCuttingProcess, '创建模切工艺成功');
  }
);
