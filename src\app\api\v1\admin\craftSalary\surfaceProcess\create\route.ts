import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { CreateSurfaceProcessData, createSurfaceProcessSchema } from '@/lib/validations/admin/surfaceProcess';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation(
  createSurfaceProcessSchema,
  async (request: NextRequest, validatedData: CreateSurfaceProcessData) => {
    const data = validatedData;

    // 检查覆膜工艺名称是否重复
    const existingSurfaceProcess = await prisma.surfaceProcess.findFirst({
      where: {
        name: data.name,
        isDel: false,
      },
    });

    assert(!existingSurfaceProcess, ErrorCode.DUPLICATE_ENTRY, '覆膜工艺名称已存在');

    // 创建覆膜工艺
    const surfaceProcess = await prisma.surfaceProcess.create({
      data: {
        name: data.name,
        price: data.price,
        unit: data.unit,
        basePrice: data.basePrice,
        thickness: data.thickness,
        density: data.density,
        remark: data.remark,
      },
    });

    return successResponse(surfaceProcess, '创建覆膜工艺成功');
  }
); 