import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';
import { DeleteSilkScreenProcessData, deleteSilkScreenProcessSchema } from '@/lib/validations/admin/silkScreenProcess';


export const POST = withValidation(
  deleteSilkScreenProcessSchema,
  async (request: NextRequest, validatedData: DeleteSilkScreenProcessData) => {
    const { id } = validatedData;

    // 查询丝印工艺详情
    const silkScreenProcess = await prisma.silkScreenProcess.findFirst({
      where: {
        id: id,
        isDel: false,
      },
    });

    assert(!!silkScreenProcess, ErrorCode.NOT_FOUND, '丝印工艺不存在');

    return successResponse(silkScreenProcess, '获取丝印工艺详情成功');
  }
); 