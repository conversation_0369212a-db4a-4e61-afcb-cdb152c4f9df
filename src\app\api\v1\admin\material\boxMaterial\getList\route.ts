import { NextRequest } from 'next/server';
import { BoxMaterialQueryData, boxMaterialQuerySchema } from '@/lib/validations/admin/boxMaterial';
import { prisma } from '@/lib/prisma';
import { withValidation } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';

const handler = withValidation(
  boxMaterialQuerySchema, 
  async (request: AuthenticatedRequest, query: BoxMaterialQueryData) => {
    const { page = 1, pageSize = 10, keyword = '', code = '' } = query;

    // 构建查询条件
    const where = {
      isDel: false,
      AND: [
        keyword ? {
          OR: [
            { facePaper: { contains: keyword } },
            { linerPaper: { contains: keyword } },
            { remark: { contains: keyword } },
          ],
        } : {},
        code ? { code: { contains: code } } : {},
      ],
    };

    // 查询总数
    const total = await prisma.boxMaterial.count({ where });

    // 查询列表
    const list = await prisma.boxMaterial.findMany({
      where,
      skip: (page - 1) * pageSize,
      take: pageSize,
      orderBy: {
        id: 'desc',
      },
    });

    return successResponse({
      list,
      pagination: {
        total,
        page,
        pageSize,
      },
    }, '获取纸箱材料列表成功');
  }
); 
export const POST = withInternalAuth(handler);