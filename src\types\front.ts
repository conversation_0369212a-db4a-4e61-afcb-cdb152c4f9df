/**
 * 前台页面相关类型定义
 */

/**
 * 产品信息接口
 */
export interface Product {
  /** 产品ID */
  id: number;
  /** 产品名称 */
  title: string;
  /** 产品副标题/描述 */
  subtitle?: string;
  /** 产品图片URL */
  imageUrl: string;
  /** 图片替代文本 */
  imageAlt?: string;
  /** 产品价格 */
  price?: number;
  /** 人气值 */
  popularity?: number;
  /** 产品分类 */
  category?: string;
  /** 产品标签 */
  tags?: string[];
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间 */
  updatedAt?: string;
}

/**
 * 轮播图项目接口
 */
export interface CarouselItem {
  /** 轮播图ID */
  id: number;
  /** 图片URL */
  image: string;
  /** 图片替代文本 */
  alt: string;
  /** 链接地址 */
  link?: string;
  /** 标题 */
  title?: string;
  /** 描述 */
  description?: string;
}

/**
 * 导航菜单项接口
 */
export interface MenuItem {
  /** 菜单项键值 */
  key: string;
  /** 菜单项标签 */
  label: React.ReactNode;
  /** 子菜单项 */
  children?: MenuItem[];
  /** 图标 */
  icon?: React.ReactNode;
  /** 是否禁用 */
  disabled?: boolean;
}

/**
 * 页面布局属性接口
 */
export interface LayoutProps {
  /** 子组件 */
  children: React.ReactNode;
  /** 页面标题 */
  title?: string;
  /** 页面描述 */
  description?: string;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * 响应式断点枚举
 */
export enum Breakpoint {
  XS = 'xs',    // < 576px
  SM = 'sm',    // >= 576px
  MD = 'md',    // >= 768px
  LG = 'lg',    // >= 992px
  XL = 'xl',    // >= 1200px
  XXL = 'xxl',  // >= 1600px
}

/**
 * 主题配置接口
 */
export interface ThemeConfig {
  /** 主色调 */
  primaryColor: string;
  /** 主色调悬停色 */
  primaryHoverColor: string;
  /** 主色调激活色 */
  primaryActiveColor: string;
  /** 成功色 */
  successColor: string;
  /** 警告色 */
  warningColor: string;
  /** 错误色 */
  errorColor: string;
  /** 边框圆角 */
  borderRadius: number;
  /** 字体大小 */
  fontSize: number;
}

/**
 * 搜索参数接口
 */
export interface SearchParams {
  /** 搜索关键词 */
  keyword?: string;
  /** 产品分类 */
  category?: string;
  /** 价格范围 - 最小值 */
  minPrice?: number;
  /** 价格范围 - 最大值 */
  maxPrice?: number;
  /** 排序方式 */
  sortBy?: 'price' | 'popularity' | 'createdAt';
  /** 排序顺序 */
  sortOrder?: 'asc' | 'desc';
  /** 页码 */
  page?: number;
  /** 每页数量 */
  pageSize?: number;
}

/**
 * API 响应数据接口
 */
export interface ApiResponse<T = unknown> {
  /** 是否成功 */
  success: boolean;
  /** 响应数据 */
  data?: T;
  /** 错误信息 */
  message?: string;
  /** 错误代码 */
  code?: number;
  /** 时间戳 */
  timestamp?: string;
}

/**
 * 分页数据接口
 */
export interface PaginatedResponse<T = unknown> {
  /** 数据列表 */
  list: T[];
  /** 总数量 */
  total: number;
  /** 当前页码 */
  page: number;
  /** 每页数量 */
  pageSize: number;
  /** 总页数 */
  totalPages: number;
}

/**
 * 用户信息接口
 */
export interface User {
  /** 用户ID */
  id: number;
  /** 用户名 */
  username: string;
  /** 邮箱 */
  email?: string;
  /** 手机号 */
  phone?: string;
  /** 头像URL */
  avatar?: string;
  /** 昵称 */
  nickname?: string;
  /** 用户状态 */
  status: 'active' | 'inactive' | 'banned';
  /** 注册时间 */
  createdAt: string;
  /** 最后登录时间 */
  lastLoginAt?: string;
}

/**
 * 联系信息接口
 */
export interface ContactInfo {
  /** 公司名称 */
  companyName: string;
  /** 联系电话 */
  phone: string;
  /** 邮箱地址 */
  email: string;
  /** 公司地址 */
  address: string;
  /** 工作时间 */
  workingHours: string;
  /** 网站URL */
  website?: string;
}
