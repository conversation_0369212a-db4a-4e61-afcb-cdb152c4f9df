import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { updateHydraulicProcessSchema } from '@/lib/validations/admin/embossingProcess';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation(
  updateHydraulicProcessSchema,
  async (request: NextRequest, validatedData: any) => {
    const { id, ...data } = validatedData;

    // 检查液压工艺是否存在
    const existingHydraulicProcess = await prisma.hydraulicProcess.findFirst({
      where: {
        id,
        isDel: false,
      },
    });

    assert(!!existingHydraulicProcess, ErrorCode.NOT_FOUND, '液压工艺不存在');

    // 检查名称是否重复（排除自己）
    const duplicateHydraulicProcess = await prisma.hydraulicProcess.findFirst({
      where: {
        name: data.name,
        isDel: false,
        id: { not: id },
      },
    });

    assert(!duplicateHydraulicProcess, ErrorCode.DUPLICATE_ENTRY, '液压工艺名称已存在');

    // 更新液压工艺
    const hydraulicProcess = await prisma.hydraulicProcess.update({
      where: { id },
      data: {
        name: data.name,
        price: data.price,
        unit: data.unit,
        basePrice: data.basePrice,
        salary: data.salary,
        salaryBasePrice: data.salaryBasePrice,
        remark: data.remark || null,
      },
    });

    return successResponse(
      hydraulicProcess,
      '更新液压工艺成功'
    );
  }
); 