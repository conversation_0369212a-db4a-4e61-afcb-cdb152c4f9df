import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { updateProcessingParamsSchema } from '@/lib/validations/admin/processingFee';
import { withValidation } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';

const handler = withValidation(
  updateProcessingParamsSchema,
  async (request: AuthenticatedRequest, validatedData: any) => {
    // 查找现有配置
    let params = await prisma.processingParams.findFirst();

    if (params) {
      // 更新现有配置
      params = await prisma.processingParams.update({
        where: { id: params.id },
        data: {
          pvcFilm: validatedData.pvcFilm,
          slottingSalary: validatedData.slottingSalary,
          slottingBasePrice: validatedData.slottingBasePrice,
          blisterPlate: validatedData.blisterPlate,
          blisterBasePrice: validatedData.blisterBasePrice,
          highFrequencyPlate: validatedData.highFrequencyPlate,
          highFrequencyBasePrice: validatedData.highFrequencyBasePrice,
          sprayCodeFee: validatedData.sprayCodeFee,
          sprayCodeBasePrice: validatedData.sprayCodeBasePrice,
          inspectionFee: validatedData.inspectionFee,
          inspectionBasePrice: validatedData.inspectionBasePrice
        }
      });
    } else {
      // 创建新配置
      params = await prisma.processingParams.create({
        data: {
          pvcFilm: validatedData.pvcFilm,
          slottingSalary: validatedData.slottingSalary,
          slottingBasePrice: validatedData.slottingBasePrice,
          blisterPlate: validatedData.blisterPlate,
          blisterBasePrice: validatedData.blisterBasePrice,
          highFrequencyPlate: validatedData.highFrequencyPlate,
          highFrequencyBasePrice: validatedData.highFrequencyBasePrice,
          sprayCodeFee: validatedData.sprayCodeFee,
          sprayCodeBasePrice: validatedData.sprayCodeBasePrice,
          inspectionFee: validatedData.inspectionFee,
          inspectionBasePrice: validatedData.inspectionBasePrice
        }
      });
    }

    return successResponse(params, '更新固定参数配置成功');
  }
); 
export const POST = withInternalAuth(handler);