import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { deleteSpecialPaperSchema, DeleteSpecialPaperParams } from '@/lib/validations/admin/specialPaper';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation<DeleteSpecialPaperParams>(
  deleteSpecialPaperSchema,
  async (request: NextRequest, validatedQuery: DeleteSpecialPaperParams) => {
    // 检查记录是否存在
    const existingMaterial = await prisma.specialPaper.findUnique({
      where: { id: validatedQuery.id, isDel: false },
    });
    assertExists(existingMaterial, ErrorCode.MATERIAL_NOT_FOUND, '特种纸不存在');

    // 软删除特种纸
    await prisma.specialPaper.update({
      where: { id: validatedQuery.id },
      data: {
        isDel: true,
        updatedAt: new Date(),
      },
    });

    return successResponse(null, '删除特种纸成功');
  }
); 