import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { deleteUserSchema, DeleteUserParams } from '@/lib/validations/user';
import { withValidation } from '@/lib/middleware/errorHandler';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';

const handler = withValidation<DeleteUserParams>(
  deleteUserSchema,
  async (request: AuthenticatedRequest, validatedData: DeleteUserParams) => {
    const { id } = validatedData;
    const currentUser = request.user;

    try {
      // 检查用户是否存在
      const existingUser = await prisma.user.findFirst({
        where: {
          id,
          isDel: false
        }
      });

      if (!existingUser) {
        return NextResponse.json(
          errorResponse(ErrorCode.USER_NOT_FOUND, '用户不存在'),
          { status: 404 }
        );
      }

      // 防止用户删除自己
      if (currentUser && currentUser.userId === id) {
        return NextResponse.json(
          errorResponse(ErrorCode.FORBIDDEN, '不能删除自己的账户'),
          { status: 403 }
        );
      }

      // 软删除用户
      await prisma.user.update({
        where: { id },
        data: {
          isDel: true,
          // 清除敏感信息
          currentLoginToken: null,
          updatedAt: new Date()
        }
      });

      return NextResponse.json(
        successResponse(null, '用户删除成功')
      );

    } catch (error) {
      console.error('删除用户失败:', error);
      return NextResponse.json(
        errorResponse(ErrorCode.INTERNAL_ERROR, '删除用户失败，请稍后重试'),
        { status: 500 }
      );
    }
  }
);

export const POST = withInternalAuth(handler);
