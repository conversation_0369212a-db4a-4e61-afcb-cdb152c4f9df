import { NextRequest } from 'next/server';
import { UpdateBoxMaterialData, updateBoxMaterialSchema } from '@/lib/validations/admin/boxMaterial';
import { prisma } from '@/lib/prisma';
import { withValidation } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';

const handler = withValidation(
  updateBoxMaterialSchema,
  async (request: AuthenticatedRequest, data: UpdateBoxMaterialData) => {
    const {
      id,
      code,
      facePaper,
      linerPaper,
      threeLayerBE,
      threeLayerAC,
      fiveLayerABBC,
      fiveLayerEB,
      sevenLayerEBA,
      remark,
    } = data;

    // 更新纸箱材料
    const boxMaterial = await prisma.boxMaterial.update({
      where: { id },
      data: {
        code,
        facePaper,
        linerPaper,
        threeLayerBE: threeLayerBE || null,
        threeLayerAC: threeLayerAC || null,
        fiveLayerABBC: fiveLayerABBC || null,
        fiveLayerEB: fiveLayerEB || null,
        sevenLayerEBA: sevenLayerEBA || null,
        remark: remark || null,
      },
    });

    return successResponse(boxMaterial, '更新纸箱材料成功');
  }
);

export const POST = withInternalAuth(handler);