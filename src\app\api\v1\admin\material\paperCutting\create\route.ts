import { NextRequest } from 'next/server';
import { createPaperCuttingSchema, CreatePaperCuttingParams } from '@/lib/validations/admin/paperCutting';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';
import { prisma } from '@/lib/prisma';

export const POST = withValidation<CreatePaperCuttingParams>(
  createPaperCuttingSchema,
  async (request: NextRequest, validatedData: CreatePaperCuttingParams) => {
    const data = validatedData;

    // 检查分切尺寸名称是否重复
    const existingPaperCutting = await prisma.paperCutting.findFirst({
      where: {
        name: data.name,
        isDel: false,
      },
    });

    assert(!existingPaperCutting, ErrorCode.MATERIAL_NAME_EXISTS, '分切尺寸名称已存在');

    // 创建分切尺寸
    const paperCutting = await prisma.paperCutting.create({
      data: {
        name: data.name,
        initialCutPrice: data.initialCutPrice,
        sizes: data.sizes,
        isDel: false,
      },
      select: {
        id: true,
        name: true,
        initialCutPrice: true,
        sizes: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return successResponse(paperCutting, '创建分切尺寸成功');
  }
); 