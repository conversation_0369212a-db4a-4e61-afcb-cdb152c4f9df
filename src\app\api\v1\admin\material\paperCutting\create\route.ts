import { NextRequest } from 'next/server';
import { createPaperCuttingSchema, CreatePaperCuttingParams } from '@/lib/validations/admin/paperCutting';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';
import { prisma } from '@/lib/prisma';

const handler = withValidation<CreatePaperCuttingParams>(
  createPaperCuttingSchema,
  async (request: AuthenticatedRequest, validatedData: CreatePaperCuttingParams) => {
    const data = validatedData;

    // 检查分切尺寸名称是否重复
    const existingPaperCutting = await prisma.paperCutting.findFirst({
      where: {
        name: data.name,
        isDel: false,
      },
    });

    assert(!existingPaperCutting, ErrorCode.MATERIAL_NAME_EXISTS, '分切尺寸名称已存在');

    // 创建分切尺寸
    const paperCutting = await prisma.paperCutting.create({
      data: {
        name: data.name,
        initialCutPrice: data.initialCutPrice,
        sizes: data.sizes,
        isDel: false,
      },
      select: {
        id: true,
        name: true,
        initialCutPrice: true,
        sizes: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return successResponse(paperCutting, '创建分切尺寸成功');
  }
); 
export const POST = withInternalAuth(handler);