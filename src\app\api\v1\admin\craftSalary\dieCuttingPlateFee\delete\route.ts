import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const deleteDieCuttingPlateFeeSchema = z.object({
  id: z.coerce.number().positive('ID必须是正整数')
});

export const POST = withValidation(
  deleteDieCuttingPlateFeeSchema,
  async (request: NextRequest, validatedQuery: any) => {
    const data = validatedQuery;

    // 检查刀版费是否存在
    const existingPlateFee = await prisma.dieCuttingPlateFee.findFirst({
      where: {
        id: data.id,
        isDel: false,
      },
    });

    assertExists(existingPlateFee, ErrorCode.RESOURCE_NOT_FOUND, '刀版费不存在');

    // 软删除刀版费
    const result = await prisma.dieCuttingPlateFee.update({
      where: { id: data.id },
      data: {
        isDel: true,
      },
    });

    return successResponse(
      { id: result.id },
      '删除刀版费成功'
    );
  }
);
