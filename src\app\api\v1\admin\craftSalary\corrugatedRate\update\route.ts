import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { UpdateCorrugatedRateData, updateCorrugatedRateSchema } from '@/lib/validations/admin/corrugatedProcess';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation(
  updateCorrugatedRateSchema,
  async (request: AuthenticatedRequest, validatedData: UpdateCorrugatedRateData) => {
    const { id, ...data } = validatedData;

    // 检查瓦楞率配置是否存在
    const existingCorrugatedRate = await prisma.corrugatedRate.findFirst({
      where: {
        id: id,
        isDel: false,
      },
    });

    assert(!!existingCorrugatedRate, ErrorCode.NOT_FOUND, '瓦楞率配置不存在');

    // 检查楞形是否与其他记录重复
    const duplicateCorrugatedRate = await prisma.corrugatedRate.findFirst({
      where: {
        fluteType: data.fluteType,
        id: { not: id },
        isDel: false,
      },
    });

    assert(!duplicateCorrugatedRate, ErrorCode.DUPLICATE_ENTRY, '该楞形已配置瓦楞率');

    // 更新瓦楞率配置
    const corrugatedRate = await prisma.corrugatedRate.update({
      where: { id },
      data: {
        fluteType: data.fluteType,
        rate: data.rate,
      },
    });

    return successResponse(corrugatedRate, '更新瓦楞率配置成功');
  }
); 
export const POST = withInternalAuth(handler);