'use client';

import React, { useState, useEffect } from 'react';
import {
  Table, Button, Space, Modal, Form, Input,
  Select, InputNumber, Popconfirm, Card, Typography,
  message, Row, Col, Switch, Tag
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined,
  SearchOutlined, ReloadOutlined
} from '@ant-design/icons';
import { specialMaterialApi } from '@/services/adminApi';
import { SpecialMaterial } from '@/types/material';
import { createSpecialMaterialSchema, updateSpecialMaterialSchema, validateStockSize } from '@/lib/validations/admin/specialMaterial';
import { useAsyncError } from '@/lib/hooks/useErrorHandler';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;

// 特殊材料管理页面
export default function SpecialMaterialManagementPage() {
  // 使用错误处理Hook
  const { execute: executeSpecialMaterial, loading: specialMaterialLoading } = useAsyncError();

  // 状态定义
  const [materialList, setMaterialList] = useState<SpecialMaterial[]>([]);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [keyword, setKeyword] = useState('');
  const [category, setCategory] = useState('');
  const [categoryList, setCategoryList] = useState<string[]>([]);

  // 模态框相关状态
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [form] = Form.useForm();

  // 初始加载数据
  useEffect(() => {
    fetchList();
    fetchCategoryList();
  }, []);

  // 获取特殊材料列表
  const fetchList = async (
    page = current,
    ps = pageSize,
    kw = keyword,
    cat = category
  ) => {
    const result = await executeSpecialMaterial(async () => {
      return await specialMaterialApi.getList({
        page,
        pageSize: ps,
        keyword: kw,
        category: cat
      });
    }, '获取特殊材料列表');

    if (result) {
      setMaterialList(result.list);
      setTotal(result.pagination.total);
    } else {
      setMaterialList([]);
      setTotal(0);
    }
  };

  // 获取材料品类列表
  const fetchCategoryList = async () => {
    const result = await executeSpecialMaterial(async () => {
      return await specialMaterialApi.getCategoryList();
    }, '获取材料品类列表');

    if (result) {
      setCategoryList(result);
    } else {
      setCategoryList([]);
    }
  };

  // 处理分页变化
  const handleTableChange = (pagination: any) => {
    setCurrent(pagination.current);
    setPageSize(pagination.pageSize);
    fetchList(pagination.current, pagination.pageSize);
  };

  // 打开添加特殊材料模态框
  const showAddModal = () => {
    setModalTitle('添加特殊材料');
    setEditingRecord(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 打开编辑特殊材料模态框
  const showEditModal = (record: SpecialMaterial) => {
    setModalTitle('编辑特殊材料');
    setEditingRecord(record);
    form.setFieldsValue({
      name: record.name,
      price: record.price,
      unit: record.unit,
      thickness: record.thickness,
      density: record.density,
      isStockSize: record.isStockSize,
      stockLength: record.stockLength,
      stockWidth: record.stockWidth,
      category: record.category,
      remark: record.remark
    });
    setModalVisible(true);
  };

  // 处理表单提交
  const handleFormSubmit = async () => {
    try {
      const values = await form.validateFields();

      // 处理品类数据类型问题 - mode="tags"会返回数组，需要转换为字符串
      if (Array.isArray(values.category) && values.category.length > 0) {
        values.category = values.category[0];
      }

      // 使用通用校验组件进行验证
      const schema = editingRecord ? updateSpecialMaterialSchema : createSpecialMaterialSchema;
      const validationResult = schema.safeParse({
        ...values,
        ...(editingRecord ? { id: editingRecord.id } : {})
      });

      if (!validationResult.success) {
        const errors = validationResult.error.format();
        const firstError = Object.values(errors)[0];
        if (firstError && typeof firstError === 'object' && '_errors' in firstError) {
          message.error(firstError._errors[0] || '表单验证失败');
        } else {
          message.error('表单验证失败');
        }
        return;
      }

      // 验证现货尺寸条件
      const stockSizeValidation = validateStockSize(validationResult.data);
      if (!stockSizeValidation.success) {
        message.error(stockSizeValidation.error);
        return;
      }

      if (editingRecord) {
        // 更新特殊材料
        const result = await executeSpecialMaterial(async () => {
          return await specialMaterialApi.update({
            id: editingRecord.id,
            ...values
          });
        }, '更新特殊材料');
        
        if (result) {
          message.success('特殊材料更新成功');
          setModalVisible(false);
          fetchList();
          fetchCategoryList();
        }
      } else {
        // 创建特殊材料
        const result = await executeSpecialMaterial(async () => {
          return await specialMaterialApi.create(values);
        }, '创建特殊材料');
        
        if (result) {
          message.success('特殊材料添加成功');
          setModalVisible(false);
          fetchList();
          fetchCategoryList();
        }
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || '操作失败';
      message.error(errorMessage);
      console.error('表单提交失败:', error);
    }
  };

  // 处理删除
  const handleDelete = async (id: number) => {
    const result = await executeSpecialMaterial(async () => {
      return await specialMaterialApi.delete(id);
    }, '删除特殊材料');

    if (result) {
      message.success('删除成功');
      fetchList();
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      align: 'center' as const,
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      width: 100,
      align: 'center' as const,
      render: (price: number) => `¥${price}`,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      width: 80,
      align: 'center' as const,
    },
    {
      title: '厚度',
      dataIndex: 'thickness',
      key: 'thickness',
      width: 80,
      align: 'center' as const,
    },
    {
      title: '密度',
      dataIndex: 'density',
      key: 'density',
      width: 80,
      align: 'center' as const,
    },
    {
      title: '按现货尺寸',
      dataIndex: 'isStockSize',
      key: 'isStockSize',
      width: 80,
      align: 'center' as const,
      render: (isStockSize: boolean) => (
        isStockSize ? 
          <Tag color="blue">是</Tag> : 
          <Tag color="default">否</Tag>
      ),
    },
    {
      title: '现货尺寸',
      key: 'stockSize',
      width: 100,
      align: 'center' as const,
      render: (_: any, record: SpecialMaterial) => {
        if (!record.isStockSize) return <Tag color="default">-</Tag>;
        return (
          <Tag color="purple">
            {record.stockLength || 0} × {record.stockWidth || 0}
          </Tag>
        );
      },
    },
    {
      title: '材料品类',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      align: 'center' as const,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      ellipsis: true,
      align: 'center' as const,
      width: 150,
      render: (remark: string) => remark || '-',
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      align: 'center' as const,
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      align: 'center' as const,
      render: (_: any, record: SpecialMaterial) => (
        <Space>
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => showEditModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              danger 
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>特殊材料数据库</Title>

      <Card>
        <div style={{ marginBottom: 16 }}>
          <Row gutter={16} align="middle">
            <Col span={4}>
              <Input
                placeholder="搜索名称"
                prefix={<SearchOutlined />}
                allowClear
                value={keyword}
                onChange={(e) => setKeyword(e.target.value)}
                onPressEnter={() => fetchList(1, pageSize, keyword, category)}
              />
            </Col>
            <Col span={4}>
              <Select
                placeholder="搜索材料品类"
                allowClear
                showSearch
                loading={specialMaterialLoading}
                style={{ width: '100%' }}
                value={category || undefined}
                onChange={(value) => setCategory(value || '')}
                optionFilterProp="children"
                filterOption={(input, option) =>
                  (option?.children as unknown as string).toLowerCase().includes(input.toLowerCase())
                }
              >
                {categoryList.map((cat) => (
                  <Option key={cat} value={cat}>
                    {cat}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col>
              <Space>
                <Button
                  type="primary"
                  icon={<SearchOutlined />}
                  onClick={() => fetchList(1, pageSize, keyword, category)}
                >
                  搜索
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => {
                    setKeyword('');
                    setCategory('');
                    fetchList(1, pageSize, '', '');
                  }}
                >
                  重置
                </Button>

              </Space>
            </Col>
            <Col flex="auto" style={{ textAlign: 'right' }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={showAddModal}

              >
                添加特殊材料
              </Button>
            </Col>
          </Row>
        </div>

        <Table
          columns={columns}
          dataSource={materialList}
          rowKey="id"
          loading={specialMaterialLoading}
          pagination={{
            current,
            pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
          }}
          onChange={handleTableChange}
          bordered
          size="middle"
          scroll={{ x: 1200 }}
          locale={{ emptyText: '暂无数据' }}
        />
      </Card>

      {/* 表单模态框 */}
      <Modal
        title={modalTitle}
        open={modalVisible}
        onOk={handleFormSubmit}
        onCancel={() => setModalVisible(false)}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="材料名称"
                rules={[{ required: true, message: '请输入材料名称' }]}
              >
                <Input placeholder="请输入材料名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="category"
                label="材料品类"
                rules={[{ required: true, message: '请选择或输入材料品类' }]}
              >
                <Select
                  mode="tags"
                  loading={specialMaterialLoading}
                  placeholder="请选择或输入材料品类"
                >
                  {categoryList.map(category => (
                    <Option key={category} value={category}>
                      {category}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="price"
                label="价格"
                rules={[{ required: true, message: '请输入价格' }]}
              >
                <InputNumber
                  min={0}
                  step={0.01}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="请输入价格"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="unit"
                label="单位"
                rules={[{ required: true, message: '请选择单位' }]}
              >
                <Select placeholder="请选择单位">
                  <Option value="元/平方">元/平方</Option>
                  <Option value="元/吨">元/吨</Option>
                  <Option value="元/张">元/张</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="thickness"
                label="厚度"
                rules={[{ required: true, message: '请输入厚度' }]}
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                  placeholder="请输入厚度"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="density"
                label="密度"
                rules={[{ required: true, message: '请输入密度' }]}
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                  placeholder="请输入密度"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="isStockSize"
                label="是否按现货尺寸"
                valuePropName="checked"
                initialValue={false}
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.isStockSize !== currentValues.isStockSize}
          >
            {({ getFieldValue }) =>
              getFieldValue('isStockSize') ? (
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="stockLength"
                      label="现货长度"
                      rules={[{ required: true, message: '请输入现货长度' }]}
                    >
                      <InputNumber
                        min={0}
                        style={{ width: '100%' }}
                        placeholder="请输入现货长度"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="stockWidth"
                      label="现货宽度"
                      rules={[{ required: true, message: '请输入现货宽度' }]}
                    >
                      <InputNumber
                        min={0}
                        style={{ width: '100%' }}
                        placeholder="请输入现货宽度"
                      />
                    </Form.Item>
                  </Col>
                </Row>
              ) : null
            }
          </Form.Item>

          <Form.Item
            name="remark"
            label="备注"
          >
            <Input.TextArea rows={3} placeholder="请输入备注信息（可选）" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
} 