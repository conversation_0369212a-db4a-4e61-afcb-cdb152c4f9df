'use client';

import React, { useState } from 'react';
import { 
  Typography, 
  Form, 
  Input, 
  Button, 
  Card, 
  Upload, 
  InputNumber, 
  Divider,
  List,
  Space
} from 'antd';
import { 
  UploadOutlined, 
  MinusCircleOutlined, 
  PlusOutlined 
} from '@ant-design/icons';

const { Title } = Typography;

export default function SettingsPage() {
  const [form] = Form.useForm();

  const onFinish = (values: any) => {
    console.log('Received values:', values);
  };

  return (
    <div>
      <Title level={2} style={{ marginBottom: 16 }}>系统设置</Title>
      
      <Card>
        <Form 
          form={form} 
          layout="vertical" 
          onFinish={onFinish}
          initialValues={{
            systemName: '盒型管理系统',
            pageSize: 10,
            partTypes: ['类型一', '类型二']
          }}
        >
          <Title level={4}>基本设置</Title>
          
          <Form.Item
            name="systemName"
            label="系统名称"
            rules={[{ required: true, message: '请输入系统名称' }]}
          >
            <Input />
          </Form.Item>
          
          <Form.Item
            name="logo"
            label="系统Logo"
          >
            <Upload
              name="logo"
              action="/api/upload"
              listType="picture"
              maxCount={1}
            >
              <Button icon={<UploadOutlined />}>上传图片</Button>
            </Upload>
          </Form.Item>
          
          <Form.Item
            name="pageSize"
            label="每页显示记录数"
            rules={[{ required: true, message: '请输入每页显示记录数' }]}
          >
            <InputNumber min={1} max={100} />
          </Form.Item>
          
          <Divider />
          
          <Title level={4}>部件类型设置</Title>
          
          <Form.List name="partTypes">
            {(fields, { add, remove }) => (
              <>
                {fields.map((field) => (
                  <Space key={field.key} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
                    <Form.Item
                      {...field}
                      validateTrigger={['onChange', 'onBlur']}
                      rules={[
                        {
                          required: true,
                          whitespace: true,
                          message: "请输入部件类型名称或删除此项",
                        },
                      ]}
                      noStyle
                    >
                      <Input placeholder="部件类型名称" style={{ width: '300px' }} />
                    </Form.Item>
                    <MinusCircleOutlined
                      onClick={() => {
                        remove(field.name);
                      }}
                    />
                  </Space>
                ))}
                <Form.Item>
                  <Button
                    type="dashed"
                    onClick={() => add()}
                    icon={<PlusOutlined />}
                  >
                    添加部件类型
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>
          
          <Form.Item style={{ marginTop: 24, textAlign: 'right' }}>
            <Button type="primary" htmlType="submit">
              保存设置
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
} 