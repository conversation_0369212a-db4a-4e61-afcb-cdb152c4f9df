import { requestResult } from '@/lib/utils/request';
import { 
  UserListItem,
  CreateUserRequest,
  UpdateUserRequest,
  QueryUserParams,
  UpdateUserStateRequest,
  DeleteUserRequest
} from '@/types/user';
import { ApiResponse, Result, PaginatedData } from '@/types/common';

/**
 * 获取用户列表
 */
export async function getUserList(params: QueryUserParams): Promise<Result<PaginatedData<UserListItem>>> {
  try {
    const response = await requestResult<ApiResponse<PaginatedData<UserListItem>>>('/api/v1/admin/users/getList', {
      method: 'POST',
      body: JSON.stringify(params),
    });

    if (response.success && response.data) {
      return {
        success: true,
        data: response.data.data
      };
    }

    return {
      success: false,
      error: {
        code: response.data?.code || 500,
        message: response.data?.message || '获取用户列表失败'
      }
    };
  } catch (error) {
    console.error('获取用户列表请求失败:', error);
    return {
      success: false,
      error: {
        code: 500,
        message: '网络错误，请稍后重试'
      }
    };
  }
}

/**
 * 创建用户
 */
export async function createUser(data: CreateUserRequest): Promise<Result<UserListItem>> {
  try {
    const response = await requestResult<ApiResponse<UserListItem>>('/api/v1/admin/users/create', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (response.success && response.data) {
      return {
        success: true,
        data: response.data.data
      };
    }

    return {
      success: false,
      error: {
        code: response.data?.code || 500,
        message: response.data?.message || '创建用户失败'
      }
    };
  } catch (error) {
    console.error('创建用户请求失败:', error);
    return {
      success: false,
      error: {
        code: 500,
        message: '网络错误，请稍后重试'
      }
    };
  }
}

/**
 * 更新用户
 */
export async function updateUser(data: UpdateUserRequest): Promise<Result<UserListItem>> {
  try {
    const response = await requestResult<ApiResponse<UserListItem>>('/api/v1/admin/users/update', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (response.success && response.data) {
      return {
        success: true,
        data: response.data.data
      };
    }

    return {
      success: false,
      error: {
        code: response.data?.code || 500,
        message: response.data?.message || '更新用户失败'
      }
    };
  } catch (error) {
    console.error('更新用户请求失败:', error);
    return {
      success: false,
      error: {
        code: 500,
        message: '网络错误，请稍后重试'
      }
    };
  }
}

/**
 * 删除用户
 */
export async function deleteUser(data: DeleteUserRequest): Promise<Result<null>> {
  try {
    const response = await requestResult<ApiResponse<null>>('/api/v1/admin/users/delete', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (response.success) {
      return {
        success: true,
        data: null
      };
    }

    return {
      success: false,
      error: {
        code: response.data?.code || 500,
        message: response.data?.message || '删除用户失败'
      }
    };
  } catch (error) {
    console.error('删除用户请求失败:', error);
    return {
      success: false,
      error: {
        code: 500,
        message: '网络错误，请稍后重试'
      }
    };
  }
}

/**
 * 更新用户状态
 */
export async function updateUserState(data: UpdateUserStateRequest): Promise<Result<UserListItem>> {
  try {
    const response = await requestResult<ApiResponse<UserListItem>>('/api/v1/admin/users/updateState', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (response.success && response.data) {
      return {
        success: true,
        data: response.data.data
      };
    }

    return {
      success: false,
      error: {
        code: response.data?.code || 500,
        message: response.data?.message || '更新用户状态失败'
      }
    };
  } catch (error) {
    console.error('更新用户状态请求失败:', error);
    return {
      success: false,
      error: {
        code: 500,
        message: '网络错误，请稍后重试'
      }
    };
  }
}

/**
 * 批量删除用户
 */
export async function batchDeleteUsers(ids: number[]): Promise<Result<null>> {
  try {
    const promises = ids.map(id => deleteUser({ id }));
    const results = await Promise.all(promises);
    
    const failedResults = results.filter(result => !result.success);
    
    if (failedResults.length === 0) {
      return {
        success: true,
        data: null
      };
    }

    return {
      success: false,
      error: {
        code: 500,
        message: `批量删除失败，${failedResults.length}个用户删除失败`
      }
    };
  } catch (error) {
    console.error('批量删除用户失败:', error);
    return {
      success: false,
      error: {
        code: 500,
        message: '批量删除用户失败，请稍后重试'
      }
    };
  }
}

/**
 * 批量更新用户状态
 */
export async function batchUpdateUserState(ids: number[], state: number): Promise<Result<null>> {
  try {
    const promises = ids.map(id => updateUserState({ id, state }));
    const results = await Promise.all(promises);
    
    const failedResults = results.filter(result => !result.success);
    
    if (failedResults.length === 0) {
      return {
        success: true,
        data: null
      };
    }

    return {
      success: false,
      error: {
        code: 500,
        message: `批量更新失败，${failedResults.length}个用户状态更新失败`
      }
    };
  } catch (error) {
    console.error('批量更新用户状态失败:', error);
    return {
      success: false,
      error: {
        code: 500,
        message: '批量更新用户状态失败，请稍后重试'
      }
    };
  }
}
