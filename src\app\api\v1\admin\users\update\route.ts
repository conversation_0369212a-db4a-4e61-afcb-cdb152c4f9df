import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { updateUserSchema, UpdateUserParams } from '@/lib/validations/user';
import { withValidation } from '@/lib/middleware/errorHandler';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { hashPassword } from '@/lib/auth/password';
import { UserRole } from '@/types/user';

const handler = withValidation<UpdateUserParams>(
  updateUserSchema,
  async (request: AuthenticatedRequest, validatedData: UpdateUserParams) => {
    const { id, name, phone, email, password, role, expiresAt, state } = validatedData;

    try {
      // 检查用户是否存在
      const existingUser = await prisma.user.findFirst({
        where: {
          id,
          isDel: false
        }
      });

      if (!existingUser) {
        return NextResponse.json(
          errorResponse(ErrorCode.USER_NOT_FOUND, '用户不存在'),
          { status: 404 }
        );
      }

      // 检查手机号是否被其他用户使用
      if (phone && phone !== existingUser.phone) {
        const phoneUser = await prisma.user.findFirst({
          where: {
            phone,
            id: { not: id },
            isDel: false
          }
        });

        if (phoneUser) {
          return NextResponse.json(
            errorResponse(ErrorCode.DUPLICATE_ENTRY, '手机号已被其他用户使用'),
            { status: 400 }
          );
        }
      }

      // 检查邮箱是否被其他用户使用
      if (email && email !== existingUser.email) {
        const emailUser = await prisma.user.findFirst({
          where: {
            email,
            id: { not: id },
            isDel: false
          }
        });

        if (emailUser) {
          return NextResponse.json(
            errorResponse(ErrorCode.DUPLICATE_ENTRY, '邮箱已被其他用户使用'),
            { status: 400 }
          );
        }
      }

      // 准备更新数据
      const updateData: any = {};

      if (name !== undefined) {
        updateData.name = name.trim();
      }

      if (phone !== undefined) {
        updateData.phone = phone.trim();
      }

      if (email !== undefined) {
        updateData.email = email && email.trim() ? email.trim() : null;
      }

      if (password !== undefined) {
        updateData.password = await hashPassword(password);
      }

      if (role !== undefined) {
        updateData.role = role;
        
        // 如果角色改为超级用户，需要设置到期时间
        if (role === UserRole.SUPER_USER && expiresAt) {
          updateData.expiresAt = new Date(expiresAt);
        } else if (role !== UserRole.SUPER_USER) {
          // 如果角色不是超级用户，清除到期时间
          updateData.expiresAt = null;
        }
      }

      if (expiresAt !== undefined) {
        if (expiresAt === null || expiresAt === '') {
          updateData.expiresAt = null;
        } else {
          updateData.expiresAt = new Date(expiresAt);
        }
      }

      if (state !== undefined) {
        updateData.state = state;
      }

      // 更新用户
      const updatedUser = await prisma.user.update({
        where: { id },
        data: updateData,
        select: {
          id: true,
          name: true,
          phone: true,
          email: true,
          role: true,
          expiresAt: true,
          state: true,
          createdAt: true,
          updatedAt: true
        }
      });

      return NextResponse.json(
        successResponse(updatedUser, '用户更新成功')
      );

    } catch (error) {
      console.error('更新用户失败:', error);
      
      // 处理数据库唯一约束错误
      if (error instanceof Error && error.message.includes('Unique constraint')) {
        if (error.message.includes('phone')) {
          return NextResponse.json(
            errorResponse(ErrorCode.DUPLICATE_ENTRY, '手机号已被其他用户使用'),
            { status: 400 }
          );
        }
        if (error.message.includes('email')) {
          return NextResponse.json(
            errorResponse(ErrorCode.DUPLICATE_ENTRY, '邮箱已被其他用户使用'),
            { status: 400 }
          );
        }
      }

      return NextResponse.json(
        errorResponse(ErrorCode.INTERNAL_ERROR, '更新用户失败，请稍后重试'),
        { status: 500 }
      );
    }
  }
);

export const POST = withInternalAuth(handler);
