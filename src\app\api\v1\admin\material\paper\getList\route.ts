import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { paperQuerySchema, PaperQueryParams } from '@/lib/validations/admin/paper';
import { withValidation } from '@/lib/middleware/errorHandler';
import { paginatedResponse } from '@/lib/utils/apiResponse';
import { Prisma } from '@prisma/client';


export const POST = withValidation<PaperQueryParams>(
  paperQuerySchema,
  async (request: NextRequest, validatedQuery: PaperQueryParams) => {
    const data = validatedQuery;
    const page = data.page || 1;
    const pageSize = data.pageSize || 10;
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const where: Prisma.PaperWhereInput = {
      isDel: false
    };

    if (data.keyword) {
      where.name = { contains: data.keyword };
    }

    if (data.category) {
      where.category = { contains: data.category };
    }

    // 查询总数和列表数据
    const [total, list] = await Promise.all([
      prisma.paper.count({ where }),
      prisma.paper.findMany({
        where,
        select: {
          id: true,
          name: true,
          category: true,
          thickness: true,
          weight: true,
          unit: true,
          price: true,
          regularPrice: true,
          largePrice: true,
          remark: true,
          createdAt: true,
          updatedAt: true,
        },
        skip,
        take: pageSize,
        orderBy: { createdAt: 'desc' },
      })
    ]);

    return paginatedResponse(
      list,
      {
        page,
        pageSize,
        total,
      },
      '获取纸张材料列表成功'
    );
  }
); 