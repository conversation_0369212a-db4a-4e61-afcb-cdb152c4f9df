import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const deleteSurfaceProcessSchema = z.object({
  id: z.number().int().positive(),
});

export const POST = withValidation(
  deleteSurfaceProcessSchema,
  async (request: NextRequest, validatedData: z.infer<typeof deleteSurfaceProcessSchema>) => {
    const { id } = validatedData;

    // 检查覆膜工艺是否存在
    const existingSurfaceProcess = await prisma.surfaceProcess.findFirst({
      where: {
        id: id,
        isDel: false,
      },
    });

    assert(!!existingSurfaceProcess, ErrorCode.NOT_FOUND, '覆膜工艺不存在');

    // 软删除覆膜工艺
    await prisma.surfaceProcess.update({
      where: { id },
      data: { isDel: true },
    });

    return successResponse({ id }, '删除覆膜工艺成功');
  }
); 