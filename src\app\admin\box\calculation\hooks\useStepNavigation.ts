import { useCallback } from 'react';
import { CalculationStep, StepNavigation } from '../types/calculation';

// 步骤顺序定义 - 分离工艺选择和配件选择为独立步骤
const STEP_ORDER = [
  CalculationStep.BASIC_INFO,
  CalculationStep.PACKAGING,
  CalculationStep.PROCESS,
  CalculationStep.ACCESSORY,
  CalculationStep.PROCESSING_FEE,
  CalculationStep.QUOTATION
];

// 步骤信息配置
const STEP_CONFIG = {
  [CalculationStep.BASIC_INFO]: {
    title: '基础信息',
    description: '填写盒子数量与基础属性，计算部件公式',
    icon: 'info'
  },
  [CalculationStep.PACKAGING]: {
    title: '部件合并与材料选择',
    description: '部件合并分析、材料选择和拼版计算',
    icon: 'layout'
  },
  [CalculationStep.PROCESS]: {
    title: '工艺选择',
    description: '选择印刷工艺和后道工艺',
    icon: 'process'
  },
  [CalculationStep.ACCESSORY]: {
    title: '配件选择',
    description: '选择普通配件和礼盒配件',
    icon: 'gift'
  },
  [CalculationStep.PROCESSING_FEE]: {
    title: '加工选择',
    description: '选择自定义加工费和固定选项加工费',
    icon: 'tool'
  },
  [CalculationStep.QUOTATION]: {
    title: '确认报价',
    description: '查看费用明细，确认最终报价',
    icon: 'quotation'
  }
};

interface UseStepNavigationProps {
  currentStep: CalculationStep;
  onStepChange: (step: CalculationStep) => void;
  getStepValidation: (step: CalculationStep) => boolean;
}

/**
 * 步骤导航Hook
 */
export function useStepNavigation({
  currentStep,
  onStepChange,
  getStepValidation
}: UseStepNavigationProps): StepNavigation {

  // 获取当前步骤索引
  const getCurrentStepIndex = useCallback(() => {
    return STEP_ORDER.indexOf(currentStep);
  }, [currentStep]);

  // 检查是否可以进入下一步
  const canGoNext = useCallback(() => {
    const currentIndex = getCurrentStepIndex();
    if (currentIndex >= STEP_ORDER.length - 1) {
      return false; // 已经是最后一步
    }
    
    // 检查当前步骤是否有效
    return getStepValidation(currentStep);
  }, [currentStep, getCurrentStepIndex, getStepValidation]);

  // 检查是否可以返回上一步
  const canGoPrevious = useCallback(() => {
    const currentIndex = getCurrentStepIndex();
    return currentIndex > 0;
  }, [getCurrentStepIndex]);

  // 下一步操作
  const nextStep = useCallback(() => {
    if (!canGoNext()) return;
    
    const currentIndex = getCurrentStepIndex();
    const nextStepIndex = currentIndex + 1;
    const nextStep = STEP_ORDER[nextStepIndex];
    
    if (nextStep) {
      onStepChange(nextStep);
    }
  }, [canGoNext, getCurrentStepIndex, onStepChange]);

  // 上一步操作
  const previousStep = useCallback(() => {
    if (!canGoPrevious()) return;
    
    const currentIndex = getCurrentStepIndex();
    const prevStepIndex = currentIndex - 1;
    const prevStep = STEP_ORDER[prevStepIndex];
    
    if (prevStep) {
      onStepChange(prevStep);
    }
  }, [canGoPrevious, getCurrentStepIndex, onStepChange]);

  // 跳转到指定步骤
  const goToStep = useCallback((step: CalculationStep) => {
    const targetIndex = STEP_ORDER.indexOf(step);
    const currentIndex = getCurrentStepIndex();
    
    if (targetIndex === -1) return; // 无效步骤
    
    if (targetIndex <= currentIndex) {
      // 向前跳转，允许
      onStepChange(step);
    } else {
      // 向后跳转，需要验证中间所有步骤
      let canJump = true;
      for (let i = currentIndex; i < targetIndex; i++) {
        if (!getStepValidation(STEP_ORDER[i])) {
          canJump = false;
          break;
        }
      }
      
      if (canJump) {
        onStepChange(step);
      }
    }
  }, [getCurrentStepIndex, onStepChange, getStepValidation]);

  return {
    canGoNext: canGoNext(),
    canGoPrevious: canGoPrevious(),
    nextStep,
    previousStep,
    goToStep
  };
}

// 导出步骤配置
export { STEP_ORDER, STEP_CONFIG }; 
