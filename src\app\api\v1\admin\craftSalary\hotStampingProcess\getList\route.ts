import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { hotStampingProcessListParamsSchema } from '@/lib/validations/admin/hotStampingProcess';
import { withValidation } from '@/lib/middleware/errorHandler';
import { paginatedResponse } from '@/lib/utils/apiResponse';
import { Prisma } from '@prisma/client';

export const POST = withValidation(
  hotStampingProcessListParamsSchema,
  async (request: NextRequest, validatedQuery: any) => {
    const data = validatedQuery;
    const page = data.page ?? 1;
    const pageSize = data.pageSize ?? 10;
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const where: Prisma.HotStampingProcessWhereInput = {
      isDel: false,
    };

    if (data.search) {
      where.OR = [
        { name: { contains: data.search } },
        { remark: { contains: data.search } }
      ];
    }

    // 查询数据和总数
    const [hotStampingProcesses, total] = await Promise.all([
      prisma.hotStampingProcess.findMany({
        where,
        skip,
        take: pageSize,
        orderBy: { createdAt: 'desc' },
      }),
      prisma.hotStampingProcess.count({ where }),
    ]);

    return paginatedResponse(
      hotStampingProcesses,
      {
        total,
        page,
        pageSize,
      },
      '查询烫金工艺列表成功'
    );
  }
); 