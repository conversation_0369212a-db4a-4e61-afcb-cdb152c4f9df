import { z } from 'zod';

// 基础字段验证规则
export const greyBoardBaseSchema = z.object({
  name: z.string()
    .min(1, '名称不能为空')
    .max(100, '名称长度不能超过100'),
  price: z.number()
    .positive('价格必须大于0')
    .refine(val => val <= 1000000, '价格不能超过1000000'),
  unit: z.string()
    .min(1, '单位不能为空')
    .max(20, '单位长度不能超过20')
    .refine(val => ['元/张', '元/吨'].includes(val), '单位必须是：元/张、元/吨'),
  weight: z.number()
    .min(0, '克重必须大于等于0')
    .refine(val => val <= 1000, '克重不能超过1000'),
  thickness: z.number()
    .min(0, '厚度必须大于等于0')
    .refine(val => val <= 10, '厚度不能超过10'),
  isRegular: z.boolean().default(false),
  isLarge: z.boolean().default(false),
  isStockSize: z.boolean().default(false),
  stockLength: z.number()
    .positive('现货长度必须大于0')
    .refine(val => val <= 10000, '现货长度不能超过10000')
    .optional()
    .nullable(),
  stockWidth: z.number()
    .positive('现货宽度必须大于0')
    .refine(val => val <= 10000, '现货宽度不能超过10000')
    .optional()
    .nullable(),
  category: z.string()
    .min(1, '品类不能为空')
    .max(50, '品类长度不能超过50'),
  remark: z.string()
    .max(1000, '备注长度不能超过1000')
    .optional()
    .nullable(),
});

// 创建灰板纸的验证schema
export const createGreyBoardSchema = greyBoardBaseSchema;

// 更新灰板纸的验证schema
export const updateGreyBoardSchema = greyBoardBaseSchema.extend({
  id: z.number().int().positive('ID必须是正整数'),
});

// 获取灰板纸详情的验证schema
export const getGreyBoardDetailSchema = z.object({
  id: z.number().int().positive('ID必须是正整数'),
});

// 获取灰板纸列表的验证schema
export const getGreyBoardListSchema = z.object({
  page: z.number().int().positive('页码必须是正整数'),
  pageSize: z.number().int().positive('每页条数必须是正整数'),
  search: z.string().optional().default(''),
});

// 删除灰板纸的验证schema
export const deleteGreyBoardSchema = z.object({
  id: z.number().int().positive('ID必须是正整数'),
});

// 现货尺寸条件验证函数
export const validateStockSize = (data: z.infer<typeof greyBoardBaseSchema>) => {
  if (data.isStockSize && (!data.stockLength || !data.stockWidth)) {
    return {
      success: false,
      error: '现货尺寸选项启用时，必须提供现货长度和宽度'
    };
  }
  return { success: true };
};

// 定义创建灰板纸分切尺寸的请求体验证schema
export const createGreyBoardCuttingSchema = z.object({
  name: z.string().min(1, '名称不能为空').max(100, '名称长度不能超过100'),
  initialCutPrice: z.number().nonnegative('分切起步金额不能为负数'),
  sizes: z.array(z.number()).min(1, '至少需要一个分切尺寸'),
});

// 定义删除灰板纸分切尺寸的请求体验证schema
export const deleteGreyBoardCuttingSchema = z.object({
  id: z.number().int().positive('ID必须是正整数'),
});

// 获取灰板纸分切尺寸详情的验证schema
export const getGreyBoardCuttingDetailSchema = z.object({
  id: z.number().int().positive('ID必须是正整数'),
});

// 获取灰板纸分切尺寸列表的验证schema
export const getGreyBoardCuttingListSchema = z.object({
  page: z.number().int().positive('页码必须是正整数'),
  pageSize: z.number().int().positive('每页条数必须是正整数'),
});

// 更新灰板纸分切尺寸的验证schema
export const updateGreyBoardCuttingSchema = z.object({
  id: z.number().int().positive('ID必须是正整数'),
  name: z.string().min(1, '名称不能为空').max(100, '名称长度不能超过100'),
  initialCutPrice: z.number().nonnegative('分切起步金额不能为负数'),
  sizes: z.array(z.number()).min(1, '至少需要一个分切尺寸'),
});

// 导出类型
export type GreyBoardBase = z.infer<typeof greyBoardBaseSchema>;
export type CreateGreyBoardParams = z.infer<typeof createGreyBoardSchema>;
export type UpdateGreyBoardParams = z.infer<typeof updateGreyBoardSchema>;
export type GetGreyBoardDetailParams = z.infer<typeof getGreyBoardDetailSchema>;
export type GetGreyBoardListParams = z.infer<typeof getGreyBoardListSchema>;
export type DeleteGreyBoardParams = z.infer<typeof deleteGreyBoardSchema>; 
export type CreateGreyBoardCuttingParams = z.infer<typeof createGreyBoardCuttingSchema>;
export type DeleteGreyBoardCuttingParams = z.infer<typeof deleteGreyBoardCuttingSchema>;
export type GetGreyBoardCuttingDetailParams = z.infer<typeof getGreyBoardCuttingDetailSchema>;
export type GetGreyBoardCuttingListParams = z.infer<typeof getGreyBoardCuttingListSchema>;
export type UpdateGreyBoardCuttingParams = z.infer<typeof updateGreyBoardCuttingSchema>;