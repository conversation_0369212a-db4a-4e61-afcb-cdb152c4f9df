/**
 * 验证工具函数
 * 提供统一的数据验证功能
 */

import { PartMaterialConfig } from '../types/calculation';

/**
 * 验证结果接口
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

/**
 * 尺寸验证参数接口
 */
export interface DimensionValidationParams {
  length?: number;
  width?: number;
  height?: number;
  minValue?: number;
  maxValue?: number;
}

/**
 * 价格验证参数接口
 */
export interface PriceValidationParams {
  unitPrice?: number;
  quantity?: number;
  totalPrice?: number;
  minPrice?: number;
  maxPrice?: number;
}

/**
 * 验证材料配置是否完整
 * @param config 材料配置
 * @returns 验证结果
 */
export const validateMaterialConfig = (config: PartMaterialConfig): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 必填字段验证
  if (!config.materialId) {
    errors.push('材料ID不能为空');
  }

  if (!config.materialName || config.materialName.trim() === '') {
    errors.push('材料名称不能为空');
  }

  if (!config.materialCategory) {
    warnings.push('材料分类未指定，将使用默认分类');
  }

  // 规格验证
  if (!config.materialSpec) {
    warnings.push('材料规格未指定');
  }

  // 尺寸验证
  if (config.materialSize) {
    const sizeValidation = validateDimensions({
      width: config.materialSize.width,
      height: config.materialSize.height,
      minValue: 1,
      maxValue: 10000
    });
    
    if (!sizeValidation.isValid) {
      errors.push(...sizeValidation.errors.map(err => `材料尺寸: ${err}`));
    }
  }

  // 瓦楞材料特殊验证
  if (config.materialCategory === 'corrugated') {
    if (!config.facePaper) {
      errors.push('瓦楞材料必须指定面纸');
    }
    if (!config.linerPaper) {
      errors.push('瓦楞材料必须指定里纸');
    }
    if (!config.structure) {
      errors.push('瓦楞材料必须指定结构');
    }
    if (!config.structurePrice || config.structurePrice <= 0) {
      errors.push('瓦楞材料必须指定有效的结构价格');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * 验证工艺参数
 * @param parameters 工艺参数
 * @param requiredParams 必需参数列表
 * @returns 验证结果
 */
export const validateProcessParameters = (
  parameters: Record<string, any>,
  requiredParams: string[] = []
): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 检查必需参数
  requiredParams.forEach(param => {
    if (!(param in parameters) || parameters[param] === null || parameters[param] === undefined) {
      errors.push(`缺少必需参数: ${param}`);
    }
  });

  // 验证数值参数
  Object.entries(parameters).forEach(([key, value]) => {
    if (typeof value === 'number') {
      if (isNaN(value)) {
        errors.push(`参数 ${key} 不是有效数字`);
      } else if (value < 0) {
        warnings.push(`参数 ${key} 为负数，请确认是否正确`);
      }
    }
  });

  // 特殊参数验证
  if ('length' in parameters && 'width' in parameters) {
    const dimensionValidation = validateDimensions({
      length: parameters.length,
      width: parameters.width,
      minValue: 0.1,
      maxValue: 10000
    });
    
    if (!dimensionValidation.isValid) {
      errors.push(...dimensionValidation.errors);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * 验证尺寸参数
 * @param params 尺寸验证参数
 * @returns 验证结果
 */
export const validateDimensions = (params: DimensionValidationParams): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  const { minValue = 0, maxValue = Number.MAX_SAFE_INTEGER } = params;

  // 验证长度
  if (params.length !== undefined) {
    if (isNaN(params.length)) {
      errors.push('长度必须是有效数字');
    } else if (params.length < minValue) {
      errors.push(`长度不能小于 ${minValue}`);
    } else if (params.length > maxValue) {
      errors.push(`长度不能大于 ${maxValue}`);
    }
  }

  // 验证宽度
  if (params.width !== undefined) {
    if (isNaN(params.width)) {
      errors.push('宽度必须是有效数字');
    } else if (params.width < minValue) {
      errors.push(`宽度不能小于 ${minValue}`);
    } else if (params.width > maxValue) {
      errors.push(`宽度不能大于 ${maxValue}`);
    }
  }

  // 验证高度
  if (params.height !== undefined) {
    if (isNaN(params.height)) {
      errors.push('高度必须是有效数字');
    } else if (params.height < minValue) {
      errors.push(`高度不能小于 ${minValue}`);
    } else if (params.height > maxValue) {
      errors.push(`高度不能大于 ${maxValue}`);
    }
  }

  // 逻辑验证
  if (params.length !== undefined && params.width !== undefined) {
    if (params.length === params.width) {
      warnings.push('长度和宽度相等，请确认是否为正方形');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * 验证价格参数
 * @param params 价格验证参数
 * @returns 验证结果
 */
export const validatePriceParameters = (params: PriceValidationParams): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  const { minPrice = 0, maxPrice = Number.MAX_SAFE_INTEGER } = params;

  // 验证单价
  if (params.unitPrice !== undefined) {
    if (isNaN(params.unitPrice)) {
      errors.push('单价必须是有效数字');
    } else if (params.unitPrice < minPrice) {
      errors.push(`单价不能小于 ${minPrice}`);
    } else if (params.unitPrice > maxPrice) {
      errors.push(`单价不能大于 ${maxPrice}`);
    } else if (params.unitPrice === 0) {
      warnings.push('单价为0，请确认是否正确');
    }
  }

  // 验证数量
  if (params.quantity !== undefined) {
    if (isNaN(params.quantity)) {
      errors.push('数量必须是有效数字');
    } else if (params.quantity < 0) {
      errors.push('数量不能为负数');
    } else if (params.quantity === 0) {
      warnings.push('数量为0，请确认是否正确');
    }
  }

  // 验证总价
  if (params.totalPrice !== undefined) {
    if (isNaN(params.totalPrice)) {
      errors.push('总价必须是有效数字');
    } else if (params.totalPrice < 0) {
      errors.push('总价不能为负数');
    }
  }

  // 逻辑验证
  if (params.unitPrice !== undefined && params.quantity !== undefined && params.totalPrice !== undefined) {
    const calculatedTotal = params.unitPrice * params.quantity;
    const tolerance = 0.01; // 允许的误差范围
    
    if (Math.abs(calculatedTotal - params.totalPrice) > tolerance) {
      warnings.push('总价与单价×数量的计算结果不一致，请检查');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * 验证邮箱格式
 * @param email 邮箱地址
 * @returns 是否有效
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * 验证手机号格式
 * @param phone 手机号
 * @returns 是否有效
 */
export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};

/**
 * 验证必填字段
 * @param value 字段值
 * @param fieldName 字段名称
 * @returns 验证结果
 */
export const validateRequired = (value: any, fieldName: string): ValidationResult => {
  const errors: string[] = [];

  if (value === null || value === undefined || value === '') {
    errors.push(`${fieldName}不能为空`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * 批量验证
 * @param validations 验证函数数组
 * @returns 合并的验证结果
 */
export const batchValidate = (validations: ValidationResult[]): ValidationResult => {
  const allErrors: string[] = [];
  const allWarnings: string[] = [];

  validations.forEach(validation => {
    allErrors.push(...validation.errors);
    if (validation.warnings) {
      allWarnings.push(...validation.warnings);
    }
  });

  return {
    isValid: allErrors.length === 0,
    errors: allErrors,
    warnings: allWarnings.length > 0 ? allWarnings : undefined
  };
};
