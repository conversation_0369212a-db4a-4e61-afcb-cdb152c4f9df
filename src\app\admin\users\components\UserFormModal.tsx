'use client';

import React, { useState, useEffect } from 'react';
import { 
  Modal, 
  Form, 
  Input, 
  Select, 
  DatePicker, 
  Switch, 
  message,
  Row,
  Col,
  Typography
} from 'antd';
import { EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';
import dayjs from 'dayjs';
import { 
  UserListItem, 
  CreateUserRequest, 
  UpdateUserRequest,
  UserRole, 
  UserState,
  USER_ROLE_OPTIONS,
  USER_STATE_OPTIONS
} from '@/types/user';
import { createUser, updateUser } from '@/services/user';

const { Text } = Typography;

interface UserFormModalProps {
  visible: boolean;
  user?: UserListItem | null;
  onSuccess: () => void;
  onCancel: () => void;
}

interface FormValues {
  name: string;
  phone: string;
  email?: string;
  password?: string;
  role: UserRole;
  expiresAt?: dayjs.Dayjs;
  state: UserState;
}

export default function UserFormModal({ visible, user, onSuccess, onCancel }: UserFormModalProps) {
  const [form] = Form.useForm<FormValues>();
  const [loading, setLoading] = useState(false);
  const [selectedRole, setSelectedRole] = useState<UserRole>(UserRole.USER);

  const isEdit = !!user;

  // 重置表单
  const resetForm = () => {
    if (user) {
      form.setFieldsValue({
        name: user.name,
        phone: user.phone,
        email: user.email || '',
        role: user.role,
        expiresAt: user.expiresAt ? dayjs(user.expiresAt) : undefined,
        state: user.state
      });
      setSelectedRole(user.role);
    } else {
      form.resetFields();
      setSelectedRole(UserRole.USER);
    }
  };

  // 监听弹窗显示状态
  useEffect(() => {
    if (visible) {
      resetForm();
    }
  }, [visible, user]);

  // 处理角色变化
  const handleRoleChange = (role: UserRole) => {
    setSelectedRole(role);
    // 如果不是超级用户，清除到期时间
    if (role !== UserRole.SUPER_USER) {
      form.setFieldValue('expiresAt', undefined);
    }
  };

  // 表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const formData = {
        name: values.name.trim(),
        phone: values.phone.trim(),
        email: values.email?.trim() || undefined,
        role: values.role,
        expiresAt: values.expiresAt?.toISOString(),
        state: values.state
      };

      let result;
      if (isEdit && user) {
        // 更新用户
        const updateData: UpdateUserRequest = {
          id: user.id,
          ...formData
        };
        
        // 如果有密码，添加到更新数据中
        if (values.password?.trim()) {
          updateData.password = values.password.trim();
        }
        
        result = await updateUser(updateData);
      } else {
        // 创建用户
        if (!values.password?.trim()) {
          message.error('密码不能为空');
          return;
        }
        
        const createData: CreateUserRequest = {
          ...formData,
          password: values.password.trim()
        };
        
        result = await createUser(createData);
      }

      if (result.success) {
        message.success(isEdit ? '用户更新成功' : '用户创建成功');
        onSuccess();
      } else {
        message.error(result.error?.message || (isEdit ? '用户更新失败' : '用户创建失败'));
      }
    } catch (error) {
      console.error('表单提交失败:', error);
      message.error('操作失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={isEdit ? '编辑用户' : '新建用户'}
      open={visible}
      onOk={handleSubmit}
      onCancel={onCancel}
      confirmLoading={loading}
      width={600}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          role: UserRole.USER,
          state: UserState.ENABLED
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="name"
              label="姓名"
              rules={[
                { required: true, message: '请输入姓名' },
                { max: 100, message: '姓名长度不能超过100个字符' }
              ]}
            >
              <Input placeholder="请输入姓名" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="phone"
              label="手机号"
              rules={[
                { required: true, message: '请输入手机号' },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }
              ]}
            >
              <Input placeholder="请输入手机号" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="email"
          label="邮箱"
          rules={[
            { type: 'email', message: '请输入有效的邮箱地址' },
            { max: 100, message: '邮箱长度不能超过100个字符' }
          ]}
        >
          <Input placeholder="请输入邮箱（可选）" />
        </Form.Item>

        <Form.Item
          name="password"
          label={isEdit ? '密码（留空则不修改）' : '密码'}
          rules={isEdit ? [
            { min: 8, message: '密码至少8位' },
            { pattern: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/, message: '密码必须包含字母和数字' }
          ] : [
            { required: true, message: '请输入密码' },
            { min: 8, message: '密码至少8位' },
            { pattern: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/, message: '密码必须包含字母和数字' }
          ]}
        >
          <Input.Password
            placeholder={isEdit ? '留空则不修改密码' : '请输入密码'}
            iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
          />
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="role"
              label="用户角色"
              rules={[{ required: true, message: '请选择用户角色' }]}
            >
              <Select
                placeholder="请选择用户角色"
                onChange={handleRoleChange}
              >
                {USER_ROLE_OPTIONS.map(option => (
                  <Select.Option key={option.value} value={option.value}>
                    <div>
                      <div>{option.label}</div>
                      {option.description && (
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {option.description}
                        </Text>
                      )}
                    </div>
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="state"
              label="用户状态"
              rules={[{ required: true, message: '请选择用户状态' }]}
            >
              <Select placeholder="请选择用户状态">
                {USER_STATE_OPTIONS.map(option => (
                  <Select.Option key={option.value} value={option.value}>
                    {option.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        {selectedRole === UserRole.SUPER_USER && (
          <Form.Item
            name="expiresAt"
            label="到期时间"
            rules={[
              { required: true, message: '超级用户必须设置到期时间' }
            ]}
          >
            <DatePicker
              placeholder="请选择到期时间"
              style={{ width: '100%' }}
              disabledDate={(current) => current && current < dayjs().startOf('day')}
              showTime
              format="YYYY-MM-DD HH:mm:ss"
            />
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
}
