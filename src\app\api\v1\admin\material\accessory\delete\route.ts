import { NextRequest } from 'next/server';
import { deleteAccessorySchema, DeleteAccessoryParams } from '@/lib/validations/admin/accessory';
import { prisma } from '@/lib/prisma';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation<DeleteAccessoryParams>(
  deleteAccessorySchema,
  async (request: NextRequest, validatedData: DeleteAccessoryParams) => {
    const { id } = validatedData;

    // 检查配件是否存在
    const existingAccessory = await prisma.accessory.findFirst({
      where: {
        id,
        isDel: false,
      },
    });

    assertExists(existingAccessory, ErrorCode.MATERIAL_NOT_FOUND, '配件不存在');

    // 软删除配件
    await prisma.accessory.update({
      where: { id },
      data: {
        isDel: true,
      },
    });

    return successResponse(true, '删除配件成功');
  }
); 