import { NextRequest } from 'next/server';
import { giftBoxAccessoryQuerySchema, GiftBoxAccessoryQueryParams } from '@/lib/validations/admin/giftBoxAccessory';
import { prisma } from '@/lib/prisma';
import { withValidation } from '@/lib/middleware/errorHandler';
import { paginatedResponse } from '@/lib/utils/apiResponse';

export const POST = withValidation<GiftBoxAccessoryQueryParams>(
  giftBoxAccessoryQuerySchema,
  async (request: NextRequest, validatedQuery: GiftBoxAccessoryQueryParams) => {
    const { page = 1, pageSize = 10, keyword = '' } = validatedQuery;

    // 构建查询条件
    const where: any = {
      isDel: false,
    };

    if (keyword) {
      where.name = {
        contains: keyword,
      };
    }

    // 查询总数和数据
    const [total, list] = await Promise.all([
      prisma.giftBoxAccessory.count({ where }),
      prisma.giftBoxAccessory.findMany({
        where,
        skip: (page - 1) * pageSize,
        take: pageSize,
        orderBy: {
          createdAt: 'desc',
        },
      }),
    ]);

    const pagination = {
      total,
      page,
      pageSize,
    };

    return paginatedResponse(list, pagination, '获取礼盒配件列表成功');
  }
); 