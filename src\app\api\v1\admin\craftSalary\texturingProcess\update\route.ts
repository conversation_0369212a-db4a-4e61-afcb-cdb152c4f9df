import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { updateTexturingProcessSchema } from '@/lib/validations/admin/embossingProcess';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation(
  updateTexturingProcessSchema,
  async (request: NextRequest, validatedData: any) => {
    const { id, ...data } = validatedData;

    // 检查压纹工艺是否存在
    const existingTexturingProcess = await prisma.texturingProcess.findFirst({
      where: {
        id,
        isDel: false,
      },
    });

    assert(!!existingTexturingProcess, ErrorCode.NOT_FOUND, '压纹工艺不存在');

    // 检查名称是否重复（排除自己）
    const duplicateTexturingProcess = await prisma.texturingProcess.findFirst({
      where: {
        name: data.name,
        isDel: false,
        id: { not: id },
      },
    });

    assert(!duplicateTexturingProcess, ErrorCode.DUPLICATE_ENTRY, '压纹工艺名称已存在');

    // 更新压纹工艺
    const texturingProcess = await prisma.texturingProcess.update({
      where: { id },
      data: {
        name: data.name,
        textureVersion: data.textureVersion,
        unit: data.unit,
        priceBelow1000: data.priceBelow1000,
        price1000_1999: data.price1000_1999,
        price2000_3999: data.price2000_3999,
        price4000Plus: data.price4000Plus,
        remark: data.remark || null,
      },
    });

    return successResponse(
      texturingProcess,
      '更新压纹工艺成功'
    );
  }
); 