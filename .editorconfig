# 顶级配置
root = true

# 所有文件默认规则
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# Markdown、YAML 文件保留空格（不清除尾部空格）
[*.md]
trim_trailing_whitespace = true

[*.yml]
trim_trailing_whitespace = true

# JSON 文件使用 2 个空格缩进
[*.json]
indent_style = space
indent_size = 2

# Makefile 保持 tab 缩进
[Makefile]
indent_style = tab

# Shell 脚本也使用 LF
[*.sh]
end_of_line = lf
