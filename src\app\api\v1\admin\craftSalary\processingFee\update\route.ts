import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { updateProcessingFeeSchema } from '@/lib/validations/admin/processingFee';
import { withValidation, assert, assertExists } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation(
  updateProcessingFeeSchema,
  async (request: NextRequest, validatedData: any) => {
    // 检查记录是否存在
    const existingFee = await prisma.processingFee.findFirst({
      where: {
        id: validatedData.id,
        isDel: false
      }
    });

    assertExists(existingFee, ErrorCode.RESOURCE_NOT_FOUND, '加工费记录不存在');

    // 检查名称是否已被其他记录使用
    const duplicateFee = await prisma.processingFee.findFirst({
      where: {
        name: validatedData.name,
        isDel: false,
        id: { not: validatedData.id }
      }
    });

    assert(!duplicateFee, ErrorCode.DUPLICATE_ENTRY, '该加工费名称已存在');

    // 更新加工费记录
    const updatedFee = await prisma.processingFee.update({
      where: { id: validatedData.id },
      data: {
        name: validatedData.name,
        unitPrice: validatedData.unitPrice,
        unit: validatedData.unit,
        basePrice: validatedData.basePrice,
        remark: validatedData.remark || ''
      }
    });

    return successResponse(updatedFee, '更新加工费成功');
  }
); 