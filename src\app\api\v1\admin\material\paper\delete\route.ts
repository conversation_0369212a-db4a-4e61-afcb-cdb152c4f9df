import { NextRequest } from 'next/server';
import { deletePaperSchema, DeletePaperParams } from '@/lib/validations/admin/paper';
import { prisma } from '@/lib/prisma';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation<DeletePaperParams>(
  deletePaperSchema,
  async (request: AuthenticatedRequest, validatedData: DeletePaperParams) => {
    // 检查纸张是否存在且未删除
    const existingPaper = await prisma.paper.findFirst({
      where: { 
        id: validatedData.id,
        isDel: false 
      }
    });

    assertExists(
      existingPaper,
      ErrorCode.RESOURCE_NOT_FOUND,
      '纸张材料不存在或已被删除'
    );

    // 软删除纸张材料
    await prisma.paper.update({
      where: { id: validatedData.id },
      data: {
        isDel: true,
      },
    });

    return successResponse(
      { id: validatedData.id },
      '删除纸张材料成功'
    );
  }
); 
export const POST = withInternalAuth(handler);