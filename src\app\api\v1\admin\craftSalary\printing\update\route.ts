import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { UpdatePrintingData, updatePrintingSchema } from '@/lib/validations/admin/printing';
import { withValidation, assert, assertExists } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation(
  updatePrintingSchema,
  async (request: AuthenticatedRequest, validatedData: UpdatePrintingData) => {
    const data = validatedData;

    // 检查印刷数据是否存在
    const existingPrinting = await prisma.printing.findFirst({
      where: {
        id: data.id,
        isDel: false,
      },
    });

    assertExists(existingPrinting, ErrorCode.RESOURCE_NOT_FOUND, '印刷数据不存在');

    // 如果修改了机型名称，检查名称是否重复
    if (data.machineModel && data.machineModel !== existingPrinting!.machineModel) {
      const duplicatePrinting = await prisma.printing.findFirst({
        where: {
          machineModel: data.machineModel,
          id: { not: data.id },
          isDel: false,
        },
      });

      assert(!duplicatePrinting, ErrorCode.DUPLICATE_ENTRY, '印刷机型已存在');
    }

    // 移除ID字段，避免更新时包含ID
    const { id: _, ...updateData } = data;

    // 更新印刷数据
    const result = await prisma.printing.update({
      where: { id: data.id },
      data: updateData,
    });

    return successResponse(result, '更新印刷配置成功');
  }
); 
export const POST = withInternalAuth(handler);