import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';
import { DeleteHotStampingProcessData, deleteHotStampingProcessSchema } from '@/lib/validations/admin/hotStampingProcess';

const handler = withValidation(
  deleteHotStampingProcessSchema,
  async (request: AuthenticatedRequest, validatedData: DeleteHotStampingProcessData) => {
    const { id } = validatedData;

    // 检查烫金工艺是否存在
    const existingHotStampingProcess = await prisma.hotStampingProcess.findFirst({
      where: {
        id: id,
        isDel: false,
      },
    });

    assert(!!existingHotStampingProcess, ErrorCode.NOT_FOUND, '烫金工艺不存在');

    // 软删除烫金工艺
    const deletedHotStampingProcess = await prisma.hotStampingProcess.update({
      where: { id: id },
      data: { isDel: true },
    });

    return successResponse(
      deletedHotStampingProcess,
      '删除烫金工艺成功'
    );
  }
); 
export const POST = withInternalAuth(handler);