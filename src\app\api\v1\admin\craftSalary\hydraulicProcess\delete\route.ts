import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';
import { z } from 'zod';

const deleteSchema = z.object({
  id: z.number().positive('ID必须是正整数'),
});

export const POST = withValidation(
  deleteSchema,
  async (request: NextRequest, validatedData: any) => {
    const { id } = validatedData;

    // 检查液压工艺是否存在
    const existingHydraulicProcess = await prisma.hydraulicProcess.findFirst({
      where: {
        id,
        isDel: false,
      },
    });

    assert(!!existingHydraulicProcess, ErrorCode.NOT_FOUND, '液压工艺不存在');

    // 软删除液压工艺
    await prisma.hydraulicProcess.update({
      where: { id },
      data: { isDel: true },
    });

    return successResponse(
      { id },
      '删除液压工艺成功'
    );
  }
);