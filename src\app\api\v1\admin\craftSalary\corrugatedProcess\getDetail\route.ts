import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';
import { GetCorrugatedProcessDetailData, getCorrugatedProcessDetailSchema } from '@/lib/validations/admin/corrugatedProcess';



export const POST = withValidation(
  getCorrugatedProcessDetailSchema,
  async (request: NextRequest, validatedData: GetCorrugatedProcessDetailData) => {
      const { id } = validatedData;
      
      // 查询瓦楞工艺详情
      const corrugatedProcess = await prisma.corrugatedProcess.findFirst({
        where: {
          id: id,
          isDel: false,
        },
      });
      
      assert(!!corrugatedProcess, ErrorCode.NOT_FOUND, '瓦楞工艺不存在');

      return successResponse(corrugatedProcess, '获取瓦楞工艺详情成功');
  }
); 