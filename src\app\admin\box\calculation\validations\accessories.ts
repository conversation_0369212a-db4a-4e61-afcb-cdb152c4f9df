import { z } from 'zod';

// 配件选择校验规则
export const accessoryItemSchema = z.object({
  id: z.number(),
  name: z.string().min(1, '配件名称不能为空'),
  quantity: z.number().min(0, '数量不能为负数'),
  unit: z.string().min(1, '单位不能为空'),
  unitPrice: z.number().min(0, '单价不能为负数'),
  totalPrice: z.number().min(0, '总价不能为负数'),
  parameters: z.record(z.any()).optional(),
});

// 配件配置校验规则
export const accessoryConfigSchema = z.object({
  accessories: z.array(accessoryItemSchema).optional(),
  giftBoxAccessories: z.array(accessoryItemSchema).optional(),
  accessoryCost: z.number().min(0).optional(),
});

export type AccessoryItemForm = z.infer<typeof accessoryItemSchema>;
export type AccessoryConfigForm = z.infer<typeof accessoryConfigSchema>; 