/**
 * 参数处理工具函数
 */

/**
 * 清理请求参数，移除无效值
 * @param params 原始参数对象
 * @returns 清理后的参数对象
 */
export function cleanRequestParams(params: Record<string, any>): Record<string, any> {
  const cleaned: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(params)) {
    // 跳过无效值
    if (isValidParamValue(value)) {
      cleaned[key] = value;
    }
  }
  
  return cleaned;
}

/**
 * 检查参数值是否有效
 * @param value 参数值
 * @returns 是否有效
 */
export function isValidParamValue(value: any): boolean {
  return (
    value !== undefined && 
    value !== null && 
    value !== '' && 
    value !== 'undefined' && 
    value !== 'null' &&
    !(typeof value === 'number' && isNaN(value))
  );
}

/**
 * 构建分页参数
 * @param page 页码
 * @param pageSize 每页大小
 * @param filters 其他筛选参数
 * @returns 清理后的参数对象
 */
export function buildPageParams(
  page: number, 
  pageSize: number, 
  filters: Record<string, any> = {}
): Record<string, any> {
  const params = {
    page,
    pageSize,
    ...filters
  };
  
  return cleanRequestParams(params);
}

/**
 * 构建搜索参数
 * @param searchText 搜索文本
 * @param filters 其他筛选参数
 * @returns 清理后的参数对象
 */
export function buildSearchParams(
  searchText: string,
  filters: Record<string, any> = {}
): Record<string, any> {
  const params: Record<string, any> = { ...filters };
  
  if (searchText) {
    params.keyword = searchText;
  }
  
  return cleanRequestParams(params);
} 