'use client';

import React, { useState, useEffect } from 'react';
import { Typography, Button, Input, Select, Table, Space, Card, DatePicker, message, Popconfirm, Tag, Badge, Segmented, Alert, Row, Col } from 'antd';
import { PlusOutlined, SearchOutlined, FunctionOutlined, EditOutlined, DeleteOutlined, ReloadOutlined } from '@ant-design/icons';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { CustomFormula, CustomFormulaListParams, FormulaStatus } from '@/types/customFormula';
import { customFormulaApi } from '@/services/adminApi';
import { useErrorHandler, useAsyncError } from '@/lib/hooks/useErrorHandler';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

export default function CustomFormulaPage() {
  const router = useRouter();
  const { clearError, errorState } = useErrorHandler();
  const { execute, loading: asyncLoading } = useAsyncError();

  const [viewMode] = useState<string | number>('list');
  const [searchText, setSearchText] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs | null, dayjs.Dayjs | null] | null>(null);
  const [formulaList, setFormulaList] = useState<CustomFormula[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  // 获取自定义公式列表
  const fetchFormulaList = async () => {
    const result = await execute(async () => {
      // 构建请求参数，只包含有效值
      const requestParams: any = {
        page: pagination.current,
        pageSize: pagination.pageSize,
      };

      if (searchText) {
        requestParams.name = searchText;
      }

      if (statusFilter !== '') {
        requestParams.status = parseInt(statusFilter);
      }

      if (dateRange?.[0] && dateRange?.[1]) {
        requestParams.startTime = dateRange[0].format('YYYY-MM-DD HH:mm:ss');
        requestParams.endTime = dateRange[1].format('YYYY-MM-DD HH:mm:ss');
      }

      return await customFormulaApi.getList(requestParams);
    }, '获取自定义公式列表');

    if (result && result.list) {
      setFormulaList(result.list || []);
      setPagination(prev => ({
        ...prev,
        total: result.pagination?.total || 0
      }));
    } else {
      setFormulaList([]);
      setPagination(prev => ({ ...prev, total: 0 }));
    }

    setLoading(false);
  };

  // 首次加载时获取数据
  useEffect(() => {
    fetchFormulaList();
  }, []);

  // 重置筛选条件
  const handleReset = () => {
    setSearchText('');
    setStatusFilter('');
    setDateRange(null);
    clearError();
    // 重置后立即查询
    fetchFormulaList();
  };

  // 处理分页变化
  const handleTableChange = (newPagination: any) => {
    setPagination(prev => ({
      ...prev,
      current: newPagination.current,
      pageSize: newPagination.pageSize,
    }));
  };

  // 当分页状态改变时重新获取数据
  useEffect(() => {
    fetchFormulaList();
  }, [pagination.current, pagination.pageSize]);

  // 处理新建按钮点击
  const handleCreate = () => {
    router.push('/admin/customFormula/create');
  };

  // 处理删除
  const handleDelete = async (id: number) => {
    const result = await execute(async () => {
      return await customFormulaApi.delete(id);
    }, '删除自定义公式');

    if (result) {
      message.success('删除成功');
      fetchFormulaList(); // 重新加载列表
    }
  };

  // 重试操作
  const handleRetry = () => {
    clearError();
    fetchFormulaList();
  };

  // 表格列定义
  const columns = [
    {
      title: '公式名称',
      dataIndex: 'name',
      key: 'name',
      width: 180,
      align: 'center' as const,
    },
    {
      title: '起步金额',
      dataIndex: 'initialAmount',
      key: 'initialAmount',
      width: 120,
      align: 'center' as const,
      render: (initialAmount: number) => `¥${initialAmount.toFixed(2)}`,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      align: 'center' as const,
      render: (status: number) => (
        <Badge
          status={status === FormulaStatus.ENABLED ? 'success' : 'default'}
          text={status === FormulaStatus.ENABLED ? '已发布' : '草稿'}
        />
      ),
    },
    {
      title: '参数数量',
      key: 'attributeCount',
      width: 120,
      align: 'center' as const,
      render: (_: any, record: CustomFormula) => (
        <Tag color="blue">参数: {record._count?.attributes || 0}</Tag>
      ),
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 170,
      align: 'center' as const,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      align: 'center' as const,
      render: (_: any, record: CustomFormula) => (
        <Space size="middle">
          <Link href={`/admin/customFormula/edit?id=${record.id}`}>
            <Button type="primary" size="small" icon={<EditOutlined />}>编辑</Button>
          </Link>
          <Popconfirm
            title="确定要删除这个自定义公式吗?"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button danger size="small" icon={<DeleteOutlined />}>删除</Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 卡片视图
  const renderCardView = () => {
    return (
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))', gap: '16px', textAlign: 'center' }}>
        {formulaList.map(item => {
          return (
            <Card
              key={item.id}
              hoverable
              cover={
                <div style={{
                  height: 120,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: '#f5f5f5'
                }}>
                  <FunctionOutlined style={{ fontSize: 48, color: '#1890ff' }} />
                </div>
              }
              actions={[
                <Link key="edit" href={`/admin/customFormula/edit?id=${item.id}`}>
                  <Button type="primary" size="small" icon={<EditOutlined />}>编辑</Button>
                </Link>,
                <Popconfirm
                  key="delete"
                  title="确定要删除这个自定义公式吗?"
                  onConfirm={() => handleDelete(item.id)}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button type="primary" danger size="small" icon={<DeleteOutlined />}>删除</Button>
                </Popconfirm>
              ]}
            >
              <Card.Meta
                title={
                  <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    <span>{item.name}</span>
                  </div>
                }
                description={
                  <Space direction="vertical" size={2} style={{ width: '100%', textAlign: 'center' }}>
                    <div>ID: {item.id}</div>
                    <div>起步金额: ¥{item.initialAmount.toFixed(2)}</div>
                    <div>
                      <Badge
                        status={item.status === FormulaStatus.ENABLED ? 'success' : 'default'}
                        text={item.status === FormulaStatus.ENABLED ? '已发布' : '草稿'}
                      />
                    </div>
                    <div style={{ fontSize: '12px', color: '#999' }}>
                      更新时间: {dayjs(item.updatedAt).format('YYYY-MM-DD HH:mm:ss')}
                    </div>
                  </Space>
                }
              />
            </Card>
          );
        })}
      </div>
    );
  };

  return (
    <div>
      <Title level={2}>自定义计算公式</Title>

      {/* 错误状态显示 */}
      {errorState.hasError && (
        <Alert
          message="操作失败"
          description={errorState.error?.message}
          type="error"
          showIcon
          closable
          onClose={clearError}
          action={
            <Button size="small" icon={<ReloadOutlined />} onClick={handleRetry}>
              重试
            </Button>
          }
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 搜索和筛选 */}
      <Card>
        <Row gutter={16} align="middle" style={{ marginBottom: 16 }}>
          <Col span={4}>
            <Input
              placeholder="搜索公式名称..."
              prefix={<SearchOutlined />}
              allowClear
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
            />
          </Col>
          <Col>
            <Select
              value={statusFilter}
              onChange={setStatusFilter}
              style={{ width: 120 }}
              placeholder="状态筛选"
              allowClear
            >
              <Option value="">所有状态</Option>
              <Option value="1">已发布</Option>
              <Option value="0">草稿</Option>
            </Select>
          </Col>
          <Col>
            <Button onClick={handleReset}>
              重置
            </Button>
          </Col>
          <Col>
            <Button type="primary" icon={<SearchOutlined />} onClick={() => fetchFormulaList()}>
              筛选
            </Button>
          </Col>
          <Col flex="auto" style={{ textAlign: 'right' }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreate}
            >
              新建计算公式
            </Button>
          </Col>
        </Row>


        {/* 公式列表 */}
        {viewMode === 'list' ? (
          <Table
            columns={columns}
            dataSource={formulaList}
            rowKey="id"
            loading={loading || asyncLoading}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showTotal: (total) => `共 ${total} 条`,
            }}
            onChange={handleTableChange}
            locale={{ emptyText: '暂无数据' }}
            scroll={{ x: 'max-content' }}
            bordered
            size="middle"
          />
        ) : (
          renderCardView()
        )}
      </Card>
    </div>
  );
} 