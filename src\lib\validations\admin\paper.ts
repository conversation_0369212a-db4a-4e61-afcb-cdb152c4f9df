import { z } from 'zod';

// 纸类单位枚举
const paperUnitEnum = ['元/吨', '元/张', '元/平方'] as const;

// 纸类基础验证 schema
const paperBaseSchema = {
  name: z.string({
    required_error: '请输入纸张名称',
    invalid_type_error: '纸张名称必须是字符串',
  }).min(1, '纸张名称不能为空').max(100, '纸张名称不能超过100个字符'),
  
  price: z.number({
    required_error: '请输入价格',
    invalid_type_error: '价格必须是数字',
  }).min(0, '价格不能小于0'),
  
  unit: z.enum(paperUnitEnum, {
    required_error: '请选择单位',
    invalid_type_error: '无效的单位类型',
  }),
  
  weight: z.number({
    required_error: '请输入克重',
    invalid_type_error: '克重必须是数字',
  }).min(0, '克重不能小于0'),
  
  thickness: z.number({
    required_error: '请输入厚度',
    invalid_type_error: '厚度必须是数字',
  }).min(0, '厚度不能小于0'),
  
  regularPrice: z.number({
    invalid_type_error: '正度价格必须是数字',
  }).min(0, '正度价格不能小于0').optional(),
  
  largePrice: z.number({
    invalid_type_error: '大度价格必须是数字',
  }).min(0, '大度价格不能小于0').optional(),
  
  category: z.string({
    required_error: '请选择或输入材料品类',
    invalid_type_error: '材料品类必须是字符串',
  }).min(1, '材料品类不能为空').max(50, '材料品类不能超过50个字符'),
  
  remark: z.string().max(1000, '备注不能超过1000个字符').optional(),
};

// 创建纸类验证 schema
export const createPaperSchema = z.object({
  ...paperBaseSchema,
});

// 更新纸类验证 schema
export const updatePaperSchema = z.object({
  id: z.number({
    required_error: '请提供纸张ID',
    invalid_type_error: '纸张ID必须是数字',
  }).positive('纸张ID必须大于0'),
  ...paperBaseSchema,
});

// 删除纸类验证 schema
export const deletePaperSchema = z.object({
  id: z.number({
    required_error: '请提供纸张ID',
    invalid_type_error: '纸张ID必须是数字',
  }).positive('纸张ID必须大于0'),
});

// 纸类分切尺寸基础验证 schema
const paperCuttingBaseSchema = {
  name: z.string({
    required_error: '请输入名称',
    invalid_type_error: '名称必须是字符串',
  }).min(1, '名称不能为空').max(100, '名称不能超过100个字符'),
  
  initialCutPrice: z.number({
    required_error: '请输入分切起步金额',
    invalid_type_error: '分切起步金额必须是数字',
  }).min(0, '分切起步金额不能小于0'),
  
  sizes: z.array(
    z.number({
      required_error: '请输入分切尺寸',
      invalid_type_error: '分切尺寸必须是数字',
    }).min(0, '分切尺寸不能小于0')
  ).min(1, '至少需要一个分切尺寸'),
};

// 创建分切尺寸验证 schema
export const createPaperCuttingSchema = z.object({
  ...paperCuttingBaseSchema,
});

// 更新分切尺寸验证 schema
export const updatePaperCuttingSchema = z.object({
  id: z.number({
    required_error: '请提供分切尺寸ID',
    invalid_type_error: '分切尺寸ID必须是数字',
  }).positive('分切尺寸ID必须大于0'),
  ...paperCuttingBaseSchema,
});

// 删除分切尺寸验证 schema
export const deletePaperCuttingSchema = z.object({
  id: z.number({
    required_error: '请提供分切尺寸ID',
    invalid_type_error: '分切尺寸ID必须是数字',
  }).positive('分切尺寸ID必须大于0'),
});

// 查询参数验证 schema
export const paperQuerySchema = z.object({
  page: z.number().positive('页码必须大于0').optional(),
  pageSize: z.number().positive('每页条数必须大于0').optional(),
  keyword: z.string().optional(),
  category: z.string().optional(),
});

// 类型定义
export type CreatePaperParams = z.infer<typeof createPaperSchema>;
export type UpdatePaperParams = z.infer<typeof updatePaperSchema>;
export type DeletePaperParams = z.infer<typeof deletePaperSchema>;
export type CreatePaperCuttingParams = z.infer<typeof createPaperCuttingSchema>;
export type UpdatePaperCuttingParams = z.infer<typeof updatePaperCuttingSchema>;
export type DeletePaperCuttingParams = z.infer<typeof deletePaperCuttingSchema>;
export type PaperQueryParams = z.infer<typeof paperQuerySchema>; 