import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { printingListParamsSchema } from '@/lib/validations/admin/printing';
import { withValidation } from '@/lib/middleware/errorHandler';
import { paginatedResponse } from '@/lib/utils/apiResponse';
import { Prisma } from '@prisma/client';

export const POST = withValidation(
  printingListParamsSchema,
  async (request: NextRequest, validatedQuery: any) => {
    const data = validatedQuery;
    const page = data.page ?? 1;
    const pageSize = data.pageSize ?? 10;
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const where: Prisma.PrintingWhereInput = {
      isDel: false
    };

    if (data.search) {
      where.OR = [
        { machineModel: { contains: data.search } },
        { remark: { contains: data.search } }
      ];
    }

    if (data.unit) {
      where.unit = data.unit;
    }

    // 构建排序条件
    const sortBy = data.sortBy ?? 'createdAt';
    const sortOrder = data.sortOrder ?? 'desc';
    const orderBy: Prisma.PrintingOrderByWithRelationInput = {
      [sortBy]: sortOrder
    } as Prisma.PrintingOrderByWithRelationInput;

    // 查询总数和列表数据
    const [total, list] = await Promise.all([
      prisma.printing.count({ where }),
      prisma.printing.findMany({
        where,
        orderBy,
        skip,
        take: pageSize,
      })
    ]);

    return paginatedResponse(
      list,
      {
        page,
        pageSize,
        total,
      },
      '获取印刷数据列表成功'
    );
  }
); 