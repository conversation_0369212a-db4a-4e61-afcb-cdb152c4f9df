import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { updatePaperCuttingSchema, UpdatePaperCuttingParams } from '@/lib/validations/admin/paperCutting';
import { withValidation, assert, assertExists } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation<UpdatePaperCuttingParams>(
  updatePaperCuttingSchema,
  async (request: AuthenticatedRequest, validatedData: UpdatePaperCuttingParams) => {
    const data = validatedData;

    // 检查分切尺寸是否存在
    const existingPaperCutting = await prisma.paperCutting.findFirst({
      where: {
        id: data.id,
        isDel: false,
      },
    });

    assertExists(existingPaperCutting, ErrorCode.MATERIAL_NOT_FOUND, '分切尺寸不存在');

    // 如果修改了名称，检查名称是否重复
    if (data.name && data.name !== existingPaperCutting!.name) {
      const duplicatePaperCutting = await prisma.paperCutting.findFirst({
        where: {
          name: data.name,
          id: { not: data.id },
          isDel: false,
        },
      });

      assert(!duplicatePaperCutting, ErrorCode.MATERIAL_NAME_EXISTS, '分切尺寸名称已存在');
    }

    // 更新分切尺寸
    const paperCutting = await prisma.paperCutting.update({
      where: { id: data.id },
      data: {
        name: data.name,
        initialCutPrice: data.initialCutPrice,
        sizes: data.sizes,
      },
      select: {
        id: true,
        name: true,
        initialCutPrice: true,
        sizes: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return successResponse(paperCutting, '更新分切尺寸成功');
  }
); 
export const POST = withInternalAuth(handler);