---
description: 
globs: 
alwaysApply: true
---
阅读开发相关文档
doc\前端开发指南.md
doc\API接口文档.md

项目情况:
doc 为项目文档目录
src 为项目代码根目录
src/app/admin 为管理后台页面
src/app/admin/box 为盒型页面
src/app/admin/customFormula 为自定义公式页面
src/app/api/v1/ 为后端接口
src/app/api/v1/admin 为管理后台后端接口
lib 为公用组件目录
lib/util 为工具目录
lib/validations 为通用 zod 校验组件
services 为前端调用后端通用接口目录
types 为前后端公用类型目录，汇总所有类型

项目注意事项:
前端:
页面需要保持相同的设计，组件尽量复用
保持细节统一，参数进行 zod 校验，注意类型
后端:
接口风格保持一致，参数都需要进行校验
类型详细且完善
综合：
类型尽量不使用 any
前后端参数对其，类型对齐
时刻注意代码风格和目录
构建操作文档，目录为 doc
构建现代化项目