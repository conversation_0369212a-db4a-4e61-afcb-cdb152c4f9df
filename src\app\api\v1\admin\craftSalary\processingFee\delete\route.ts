import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { deleteProcessingFeeSchema } from '@/lib/validations/admin/processingFee';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation(
  deleteProcessingFeeSchema,
  async (request: AuthenticatedRequest, validatedData: any) => {
    // 检查记录是否存在
    const existingFee = await prisma.processingFee.findFirst({
      where: {
        id: validatedData.id,
        isDel: false
      }
    });

    assertExists(existingFee, ErrorCode.RESOURCE_NOT_FOUND, '加工费记录不存在');

    // 软删除加工费记录
    await prisma.processingFee.update({
      where: { id: validatedData.id },
      data: { isDel: true }
    });

    return successResponse(null, '删除加工费成功');
  }
); 
export const POST = withInternalAuth(handler);