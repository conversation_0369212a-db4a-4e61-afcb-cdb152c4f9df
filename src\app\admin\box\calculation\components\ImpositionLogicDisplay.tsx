/**
 * 拼版逻辑展示组件
 * 给用户展示当前拼版的详细逻辑和计算过程
 */

import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Card, Descriptions, Tag, Alert, Collapse, Space, Typography, Progress, Divider, Spin, Button } from 'antd';
import { InfoCircleOutlined, CalculatorOutlined, PrinterOutlined, FileTextOutlined, DollarOutlined, ReloadOutlined } from '@ant-design/icons';
import { ImpositionResult, PartGroup } from '../types/packaging';
import { PartMaterialConfig } from '../types/calculation';
import { paperApi, specialPaperApi, greyBoardApi } from '@/services/adminApi';
import { perfLog } from '@/lib/utils/perfLog';
import { getChineseUnit, getSpecDisplayName } from '../util';

const { Text } = Typography;

interface ImpositionLogicDisplayProps {
  impositionResults: ImpositionResult[];
  showDetails?: boolean;
  facePartGroups?: PartGroup[];
  greyPartGroups?: PartGroup[];
  bleed?: number;
  gripper?: number;
  bite?: number;
  partMaterialConfigs?: Record<string, PartMaterialConfig>; // 部件材料配置
  boxQuantity?: number; // 盒子数量，用于材料费用计算
  // 损耗率参数
  paperWasteRate?: number; // 面纸损耗率（%）
  specialPaperWasteRate?: number; // 特种纸损耗率（%）
  greyBoardWasteRate?: number; // 灰板损耗率（%）
  corrugatedWasteRate?: number; // 瓦楞材料损耗率（%）
  onMaterialCostChange?: (cost: number, details?: MaterialCostDetail[]) => void; // 材料费用变化回调，包含详细信息
}

// 材料价格信息接口
interface MaterialPriceInfo {
  id: number;
  name: string;
  price: number;
  unit: string;
  category?: string;
  weight?: number;  // 克重，用于按吨计价的计算
  thickness?: number; // 厚度
}

// 材料费用详情接口
interface MaterialCostDetail {
  materialType: string;
  materialName: string;
  specification: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  totalCost: number;
  sheetsNeeded: number;
  partGroups: string[];
}

export const ImpositionLogicDisplay: React.FC<ImpositionLogicDisplayProps> = ({
  impositionResults,
  showDetails = true,
  facePartGroups = [],
  greyPartGroups = [],
  bleed = 3,
  gripper = 10,
  bite = 15,
  partMaterialConfigs,
  // 损耗率参数，提供默认值
  paperWasteRate = 10,
  specialPaperWasteRate = 5,
  greyBoardWasteRate = 8,
  corrugatedWasteRate = 7,
  onMaterialCostChange
}) => {
  // 材料价格状态
  const [materialPrices, setMaterialPrices] = useState<Record<string, MaterialPriceInfo>>({});
  const [materialCostLoading, setMaterialCostLoading] = useState(false);

  // 防止无限循环的状态
  const lastNotifiedCostRef = useRef<number>(0);
  const notificationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 使用工具函数进行单位转换

  // 获取材料价格信息
  const fetchMaterialPrices = async (materialIds: { paperId?: number; specialPaperId?: number; greyBoardId?: number }[]) => {
    try {
      setMaterialCostLoading(true);
      const priceMap: Record<string, MaterialPriceInfo> = {};

      // 获取纸张价格
      const paperIds = materialIds.filter(m => m.paperId).map(m => m.paperId!);
      if (paperIds.length > 0) {
        for (const id of paperIds) {
          try {
            const result = await paperApi.getDetail(id);
            if (result.success && result.data) {
              priceMap[`paper_${id}`] = {
                id: result.data.id,
                name: result.data.name,
                price: result.data.price,
                unit: result.data.unit,
                category: result.data.category,
                weight: result.data.weight,
                thickness: result.data.thickness
              };
            }
          } catch (error) {
            perfLog.error(`获取纸张价格失败 (ID: ${id}):`, error);
          }
        }
      }

      // 获取特种纸价格
      const specialPaperIds = materialIds.filter(m => m.specialPaperId).map(m => m.specialPaperId!);
      if (specialPaperIds.length > 0) {
        for (const id of specialPaperIds) {
          try {
            const result = await specialPaperApi.getDetail(id);
            if (result.success && result.data) {
              priceMap[`specialPaper_${id}`] = {
                id: result.data.id,
                name: result.data.name,
                price: result.data.price,
                unit: result.data.unit,
                category: result.data.category,
                weight: result.data.weight,
                thickness: result.data.thickness
              };
            }
          } catch (error) {
            perfLog.error(`获取特种纸价格失败 (ID: ${id}):`, error);
          }
        }
      }

      // 获取灰板价格
      const greyBoardIds = materialIds.filter(m => m.greyBoardId).map(m => m.greyBoardId!);
      if (greyBoardIds.length > 0) {
        for (const id of greyBoardIds) {
          try {
            const result = await greyBoardApi.getDetail(id);
            if (result.success && result.data) {
              priceMap[`greyBoard_${id}`] = {
                id: result.data.id,
                name: result.data.name,
                price: result.data.price,
                unit: result.data.unit,
                category: result.data.category,
                weight: result.data.weight,
                thickness: result.data.thickness
              };
            }
          } catch (error) {
            perfLog.error(`获取灰板价格失败 (ID: ${id}):`, error);
          }
        }
      }

      setMaterialPrices(priceMap);
    } catch (error) {
      perfLog.error('获取材料价格失败:', error);
    } finally {
      setMaterialCostLoading(false);
    }
  };

  // 计算材料费用
  const calculateMaterialCosts = useMemo(() => {
    if (!partMaterialConfigs || Object.keys(materialPrices).length === 0) {
      return { details: [], total: 0 };
    }

    const details: MaterialCostDetail[] = [];
    let total = 0;

    // 按材料分组统计
    const materialGroups: Record<string, {
      materialType: string;
      materialName: string;
      specification: string;
      materialId: number;
      materialCategory: string;
      totalSheets: number;
      totalMaterialArea: number; // 总材料面积(mm²)
      partGroups: string[];
      impositionResults: ImpositionResult[]; // 存储相关的拼版结果
    }> = {};

    // 收集所有材料信息
    Object.entries(partMaterialConfigs).forEach(([partGroupId, config]) => {
      perfLog.debug(`检查部件组材料配置: ${partGroupId}`, {
        hasConfig: !!config,
        materialId: config?.materialId,
        materialName: config?.materialName,
        materialCategory: config?.materialCategory,
        isComplete: !!(config?.materialId && config?.materialName)
      });

      if (!config.materialId || !config.materialName) {
        perfLog.debug(`跳过部件组 ${partGroupId}：材料配置不完整`);
        return;
      }

      // 确定材料类型和分类
      let materialType: string;
      let materialCategory: string;

      if (config.materialCategory === 'paper') {
        materialType = '纸张';
        materialCategory = 'paper';
      } else if (config.materialCategory === 'specialPaper') {
        materialType = '特种纸';
        materialCategory = 'specialPaper';
      } else if (config.materialCategory === 'greyBoard') {
        materialType = '灰板';
        materialCategory = 'greyBoard';
      } else if (config.materialCategory === 'corrugated') {
        materialType = '瓦楞';
        materialCategory = 'corrugated';
      } else {
        // 对于没有明确分类的材料，根据部件组类型判断
        const partGroup = [...facePartGroups, ...greyPartGroups].find(g => g.id === partGroupId);
        if (partGroup && greyPartGroups.some(g => g.id === partGroup.id)) {
          materialType = '灰板';
          materialCategory = 'greyBoard';
        } else {
          // 默认为纸张
          materialType = '纸张';
          materialCategory = 'paper';
        }
      }

      const key = `${materialCategory}_${config.materialId}`;

      if (!materialGroups[key]) {
        materialGroups[key] = {
          materialType,
          materialName: config.materialName,
          specification: config.materialSpec || '未指定',
          materialId: config.materialId,
          materialCategory,
          totalSheets: 0,
          totalMaterialArea: 0,
          partGroups: [],
          impositionResults: []
        };
      }

      // 查找对应的拼版结果
      const impositionResult = impositionResults.find(r => r.partGroup.id === partGroupId);
      if (impositionResult) {
        materialGroups[key].totalSheets += impositionResult.sheetsNeeded;
        materialGroups[key].totalMaterialArea += impositionResult.materialArea * impositionResult.sheetsNeeded;
        materialGroups[key].impositionResults.push(impositionResult);
      }

      // 添加部件组名称
      const partGroup = [...facePartGroups, ...greyPartGroups].find(g => g.id === partGroupId);
      if (partGroup && !materialGroups[key].partGroups.includes(partGroup.name)) {
        materialGroups[key].partGroups.push(partGroup.name);
      }
    });

    // 计算每种材料的费用
    Object.values(materialGroups).forEach(group => {
      const priceKey = `${group.materialCategory}_${group.materialId}`;
      const priceInfo = materialPrices[priceKey];

      // 瓦楞材料使用配置中的结构价格
      if (group.materialCategory === 'corrugated') {
        // 从配置中获取结构价格，使用面纸/里纸组合匹配
        const config = Object.values(partMaterialConfigs).find(c =>
          c.materialCategory === 'corrugated' &&
          c.facePaper && c.linerPaper && c.structure &&
          group.materialName.includes(c.facePaper) &&
          group.materialName.includes(c.linerPaper)
        );

        if (config && config.structurePrice && group.totalSheets > 0) {
          const totalAreaM2 = group.totalMaterialArea / 1000000; // 转换mm²到m²
          const quantity = totalAreaM2;
          const unitPrice = config.structurePrice;
          const unit = 'm²';

          // 应用瓦楞材料损耗率
          const wasteRate = corrugatedWasteRate / 100;
          const totalCost = quantity * unitPrice * (1 + wasteRate);

          details.push({
            materialType: group.materialType,
            materialName: group.materialName,
            specification: group.specification,
            quantity,
            unit,
            unitPrice,
            totalCost,
            sheetsNeeded: group.totalSheets,
            partGroups: group.partGroups
          });

          total += totalCost;

          perfLog.debug(`瓦楞材料费用计算完成: ${group.materialName}`, {
            unit: '元/平方米',
            quantity,
            unitPrice,
            totalCost,
            totalSheets: group.totalSheets,
            totalMaterialArea: group.totalMaterialArea
          });
        }
      } else if (priceInfo && group.totalSheets > 0) {
        let quantity: number;
        let unitPrice: number;
        let unit: string;
        let totalCost: number;

        // 根据材料类型获取对应的损耗率
        const getWasteRate = (materialCategory: string): number => {
          switch (materialCategory) {
            case 'paper':
              return paperWasteRate / 100; // 转换百分比为小数
            case 'specialPaper':
              return specialPaperWasteRate / 100;
            case 'greyBoard':
              return greyBoardWasteRate / 100;
            case 'corrugated':
              return corrugatedWasteRate / 100; // 瓦楞材料使用专门的损耗率
            default:
              return paperWasteRate / 100; // 默认使用面纸损耗率
          }
        };

        const wasteRate = getWasteRate(group.materialCategory);

        // 根据单位类型进行不同的计算
        switch (priceInfo.unit) {
          case '元/张':
            // 元/张：简单的张数乘以单价，应用损耗率
            quantity = group.totalSheets;
            unitPrice = priceInfo.price;
            unit = '张';
            totalCost = quantity * unitPrice * (1 + wasteRate);
            break;

          case '元/平方':
          case '元/平方米':
          case '元/m²':
            // 元/平方：材料面积(m²) × 张数 × 价格，应用损耗率
            const totalAreaM2 = group.totalMaterialArea / 1000000; // 转换mm²到m²
            quantity = totalAreaM2;
            unitPrice = priceInfo.price;
            unit = 'm²';
            totalCost = quantity * unitPrice * (1 + wasteRate);
            break;

          case '元/吨':
            // 元/吨：材料面积(m²) × 克重(g/m²) × 张数 × 价格(元/吨) ÷ 1,000,000，应用损耗率
            if (priceInfo.weight) {
              const totalAreaM2 = group.totalMaterialArea / 1000000; // 转换mm²到m²
              const totalWeightGrams = totalAreaM2 * priceInfo.weight; // 总重量(克)
              const totalWeightTons = totalWeightGrams / 1000000; // 转换克到吨
              quantity = totalWeightTons;
              unitPrice = priceInfo.price;
              unit = '吨';
              totalCost = quantity * unitPrice * (1 + wasteRate);
            } else {
              // 如果没有克重信息，回退到按张计算
              perfLog.warn(`材料 ${group.materialName} 缺少克重信息，按张计算`);
              quantity = group.totalSheets;
              unitPrice = priceInfo.price;
              unit = '张(缺少克重)';
              totalCost = quantity * unitPrice * (1 + wasteRate);
            }
            break;

          default:
            // 未知单位，按张计算，应用损耗率
            perfLog.warn(`未知的材料单位: ${priceInfo.unit}，按张计算`);
            quantity = group.totalSheets;
            unitPrice = priceInfo.price;
            unit = '张';
            totalCost = quantity * unitPrice * (1 + wasteRate);
            break;
        }

        details.push({
          materialType: group.materialType,
          materialName: group.materialName,
          specification: group.specification,
          quantity,
          unit,
          unitPrice,
          totalCost,
          sheetsNeeded: group.totalSheets,
          partGroups: group.partGroups
        });

        total += totalCost;

        // 添加其他材料的费用计算日志
        perfLog.debug(`${group.materialType}费用计算完成: ${group.materialName}`, {
          unit: priceInfo.unit,
          quantity,
          unitPrice,
          totalCost,
          totalSheets: group.totalSheets,
          totalMaterialArea: group.totalMaterialArea
        });
      }
    });

    return { details, total };
  }, [partMaterialConfigs, materialPrices, impositionResults, paperWasteRate, specialPaperWasteRate, greyBoardWasteRate, corrugatedWasteRate]); // 添加损耗率参数到依赖项

  // 当材料费用计算结果变化时，通知父组件（防抖处理）
  useEffect(() => {
    if (!onMaterialCostChange || calculateMaterialCosts.total <= 0) return;

    // 检查是否与上次通知的值相同，避免重复调用
    const currentCost = Math.round(calculateMaterialCosts.total * 100) / 100; // 保留两位小数
    const lastCost = Math.round(lastNotifiedCostRef.current * 100) / 100;

    if (currentCost === lastCost) {
      return; // 值没有变化，不需要通知
    }

    // 清除之前的定时器
    if (notificationTimeoutRef.current) {
      clearTimeout(notificationTimeoutRef.current);
    }

    // 使用防抖延迟通知，避免频繁调用
    notificationTimeoutRef.current = setTimeout(() => {
      onMaterialCostChange(currentCost, calculateMaterialCosts.details);
      lastNotifiedCostRef.current = currentCost;
      perfLog.debug('材料费用计算结果已通知父组件:', { cost: currentCost, detailsCount: calculateMaterialCosts.details.length });
    }, 500); // 500ms防抖延迟

    // 清理函数
    return () => {
      if (notificationTimeoutRef.current) {
        clearTimeout(notificationTimeoutRef.current);
        notificationTimeoutRef.current = null;
      }
    };
  }, [calculateMaterialCosts.total, onMaterialCostChange]);

  // 当材料配置变化时，获取价格信息
  useEffect(() => {
    if (!partMaterialConfigs) return;

    const materialIds: { paperId?: number; specialPaperId?: number; greyBoardId?: number }[] = [];

    Object.values(partMaterialConfigs).forEach(config => {
      if (config.materialId) {
        const materialRef: any = {};
        if (config.materialCategory === 'paper') {
          materialRef.paperId = config.materialId;
        } else if (config.materialCategory === 'specialPaper') {
          materialRef.specialPaperId = config.materialId;
        } else if (config.materialCategory === 'corrugated') {
          // 瓦楞材料不需要从API获取价格，使用配置中的结构价格
          // 这里不添加到materialIds中，因为价格已经在配置中
        } else if (config.materialCategory === 'greyBoard') {
          materialRef.greyBoardId = config.materialId;
        } else {
          // 默认作为纸类材料处理
          materialRef.paperId = config.materialId;
        }
        materialIds.push(materialRef);
      }
    });

    if (materialIds.length > 0) {
      fetchMaterialPrices(materialIds);
    }
  }, [partMaterialConfigs]); // 移除 facePartGroups 和 greyPartGroups，使用 materialCategory 判断材料类型

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (notificationTimeoutRef.current) {
        clearTimeout(notificationTimeoutRef.current);
        notificationTimeoutRef.current = null;
      }
    };
  }, []);

  // 计算总体统计
  const totalStats = React.useMemo(() => {
    const totalSheets = impositionResults.reduce((sum, result) => sum + result.sheetsNeeded, 0);
    const totalArea = impositionResults.reduce((sum, result) => sum + result.materialArea, 0);
    const totalWaste = impositionResults.reduce((sum, result) => sum + result.wasteArea, 0);
    const avgEfficiency = impositionResults.length > 0
      ? impositionResults.reduce((sum, result) => sum + result.efficiency, 0) / impositionResults.length
      : 0;

    return {
      totalSheets,
      totalArea,
      totalWaste,
      avgEfficiency,
      wasteRate: totalArea > 0 ? (totalWaste / totalArea) * 100 : 0
    };
  }, [impositionResults]);

  // 根据选定材料计算分组统计
  const materialStats = React.useMemo(() => {
    if (!partMaterialConfigs) return null;

    const materialGroups: Record<string, {
      materialName: string;
      materialSpec: string;
      totalSheets: number;
      results: ImpositionResult[];
    }> = {};

    impositionResults.forEach(result => {
      const partGroupId = result.partGroup.id;
      const config = partMaterialConfigs[partGroupId];

      if (config && config.materialName && config.materialSpec) {
        const key = `${config.materialName}_${config.materialSpec}`;

        if (!materialGroups[key]) {
          materialGroups[key] = {
            materialName: config.materialName,
            materialSpec: config.materialSpec,
            totalSheets: 0,
            results: []
          };
        }

        materialGroups[key].totalSheets += result.sheetsNeeded;
        materialGroups[key].results.push(result);
      }
    });

    return Object.values(materialGroups);
  }, [impositionResults, partMaterialConfigs]);

  // 使用工具函数进行规格转换

  // 按部件组渲染拼版结果
  const renderResultsByPartGroup = () => {
    if (!showDetails || impositionResults.length === 0) return null;

    // 按材料类型分组
    const faceResults = impositionResults.filter(r => r.materialType === 'face');
    const greyResults = impositionResults.filter(r => r.materialType === 'grey');

    return (
      <div>
        {/* 面纸部件组结果 */}
        {faceResults.length > 0 && (
          <Card
            title={
              <Space>
                <PrinterOutlined style={{ color: '#1890ff' }} />
                <span>面纸拼版结果</span>
              </Space>
            }
            size="small"
            style={{ marginBottom: 16 }}
          >
            {faceResults.map((result, index) => {
              return (
                <Card
                  key={index}
                  type="inner"
                  title={
                    <Space>
                      <Tag color="blue">面纸组 {index + 1}</Tag>
                      <span>{result.partGroup.name}</span>
                    </Space>
                  }
                  style={{ marginBottom: 12 }}
                >
                  <Descriptions column={2} size="small">
                    <Descriptions.Item label="拼版方式">
                      {result.impositionX} × {result.impositionY} = {result.totalImposition}个/张
                      {result.isRotated && <Tag style={{ marginLeft: 8 }}>已旋转</Tag>}
                    </Descriptions.Item>
                    <Descriptions.Item label="利用率">
                      <Space>
                        <Progress
                          percent={result.efficiency}
                          size="small"
                          style={{ width: 100 }}
                          status={result.efficiency >= 70 ? 'success' : result.efficiency >= 50 ? 'normal' : 'exception'}
                        />
                        <Text>{result.efficiency.toFixed(1)}%</Text>
                      </Space>
                    </Descriptions.Item>
                    <Descriptions.Item label="成品尺寸(含出血)">
                      {result.productWidth?.toFixed(1)} × {result.productHeight?.toFixed(1)} mm
                    </Descriptions.Item>
                    <Descriptions.Item label="拼版尺寸">
                      {result.materialWidth.toFixed(1)} × {result.materialLength.toFixed(1)} mm
                    </Descriptions.Item>
                    <Descriptions.Item label="拼版参数">
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        出血: {bleed}mm | 咬口: {bite}mm | 拉规: {gripper}mm
                      </Text>
                    </Descriptions.Item>
                    <Descriptions.Item label="排列详情">
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {typeof result.arrangementDetails === 'string'
                          ? result.arrangementDetails
                          : result.arrangementDetails?.pattern || '标准拼版'
                        }
                      </Text>
                    </Descriptions.Item>
                    <Descriptions.Item label="实际需要张数">
                      <Text strong style={{ color: '#1890ff' }}>{result.sheetsNeeded}</Text> 张
                    </Descriptions.Item>
                    <Descriptions.Item label="浪费面积">
                      {(result.wasteArea / 1000000).toFixed(3)} m²
                    </Descriptions.Item>
                  </Descriptions>
                  {result.warnings && result.warnings.length > 0 && (
                    <Alert
                      message="注意事项"
                      description={
                        <ul style={{ margin: 0, paddingLeft: 16 }}>
                          {result.warnings.map((warning, wIndex) => (
                            <li key={wIndex}>{warning}</li>
                          ))}
                        </ul>
                      }
                      type="warning"
                      showIcon
                      style={{ marginTop: 12 }}
                    />
                  )}
                </Card>
              );
            })}
          </Card>
        )}

        {/* 灰板纸部件组结果 */}
        {greyResults.length > 0 && (
          <Card
            title={
              <Space>
                <FileTextOutlined style={{ color: '#fa8c16' }} />
                <span>灰板纸拼版结果</span>
              </Space>
            }
            size="small"
            style={{ marginBottom: 16 }}
          >
            {greyResults.map((result, index) => {
              return (
                <Card
                  key={index}
                  type="inner"
                  title={
                    <Space>
                      <Tag color="orange">灰板纸组 {index + 1}</Tag>
                      <span>{result.partGroup.name}</span>
                    </Space>
                  }
                  style={{ marginBottom: 12 }}
                >

                  <Descriptions column={2} size="small">
                    <Descriptions.Item label="拼版方式">
                      {result.impositionX} × {result.impositionY} = {result.totalImposition}个/张
                      {result.isRotated && <Tag style={{ marginLeft: 8 }}>已旋转</Tag>}
                    </Descriptions.Item>
                    <Descriptions.Item label="利用率">
                      <Space>
                        <Progress
                          percent={result.efficiency}
                          size="small"
                          style={{ width: 100 }}
                          status={result.efficiency >= 70 ? 'success' : result.efficiency >= 50 ? 'normal' : 'exception'}
                        />
                        <Text>{result.efficiency.toFixed(1)}%</Text>
                      </Space>
                    </Descriptions.Item>
                    <Descriptions.Item label="成品尺寸(含出血)">
                      {result.productWidth?.toFixed(1)} × {result.productHeight?.toFixed(1)} mm
                    </Descriptions.Item>
                    <Descriptions.Item label="拼版尺寸">
                      {result.materialWidth.toFixed(1)} × {result.materialLength.toFixed(1)} mm
                    </Descriptions.Item>
                    <Descriptions.Item label="拼版参数">
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        出血: {bleed}mm | 咬口: {bite}mm | 拉规: {gripper}mm
                      </Text>
                    </Descriptions.Item>
                    <Descriptions.Item label="排列详情">
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {typeof result.arrangementDetails === 'string'
                          ? result.arrangementDetails
                          : result.arrangementDetails?.pattern || '标准拼版'
                        }
                      </Text>
                    </Descriptions.Item>
                    <Descriptions.Item label="实际需要张数">
                      <Text strong style={{ color: '#1890ff' }}>{result.sheetsNeeded}</Text> 张
                    </Descriptions.Item>
                    <Descriptions.Item label="浪费面积">
                      {(result.wasteArea / 1000000).toFixed(3)} m²
                    </Descriptions.Item>
                  </Descriptions>

                  {result.warnings && result.warnings.length > 0 && (
                    <Alert
                      message="注意事项"
                      description={
                        <ul style={{ margin: 0, paddingLeft: 16 }}>
                          {result.warnings.map((warning, wIndex) => (
                            <li key={wIndex}>{warning}</li>
                          ))}
                        </ul>
                      }
                      type="warning"
                      showIcon
                      style={{ marginTop: 12 }}
                    />
                  )}
                </Card>
              );
            })}
          </Card>
        )}
      </div>
    );
  };

  // 渲染总体统计
  const renderOverallStats = () => (
    <Card
      title={
        <Space>
          <CalculatorOutlined />
          <span>拼版汇总</span>
        </Space>
      }
      size="small"
      style={{ marginBottom: 16 }}
    >
      {/* 按材料分组显示总张数和费用 */}
      {materialStats && materialStats.length > 0 && (
        <>
          <div style={{ marginBottom: 16 }}>
            <Space style={{ marginBottom: 8 }}>
              <Text strong style={{ fontSize: '14px' }}>
                材料需求与费用汇总：
              </Text>
              <Button
                type="text"
                size="small"
                icon={<ReloadOutlined />}
                loading={materialCostLoading}
                onClick={() => {
                  const materialIds: { paperId?: number; specialPaperId?: number; greyBoardId?: number }[] = [];
                  if (partMaterialConfigs) {
                    Object.values(partMaterialConfigs).forEach(config => {
                      if (config.materialId) {
                        const materialRef: any = {};
                        if (config.materialCategory === 'paper') {
                          materialRef.paperId = config.materialId;
                        } else if (config.materialCategory === 'specialPaper') {
                          materialRef.specialPaperId = config.materialId;
                        } else if (config.materialCategory === 'corrugated') {
                          // 瓦楞材料不需要从API获取价格
                        } else if (config.materialCategory === 'greyBoard') {
                          materialRef.greyBoardId = config.materialId;
                        } else {
                          // 默认作为纸类材料处理
                          materialRef.paperId = config.materialId;
                        }
                        materialIds.push(materialRef);
                      }
                    });
                    if (materialIds.length > 0) {
                      fetchMaterialPrices(materialIds);
                    }
                  }
                }}
              >
                刷新价格
              </Button>
            </Space>

            <Spin spinning={materialCostLoading}>
              {materialStats.map((group, index) => (
                <div key={index} style={{ marginBottom: 8 }}>
                  <Space>
                    <Tag color="blue">{group.materialName}</Tag>
                    <Tag color="green">{getSpecDisplayName(group.materialSpec)}</Tag>
                    <Text strong style={{ color: '#1890ff' }}>
                      {group.totalSheets} 张
                    </Text>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      ({group.results.length} 个部件组)
                    </Text>
                  </Space>
                </div>
              ))}

              {/* 材料费用详情 */}
              {calculateMaterialCosts.details.length > 0 && (
                <div style={{ marginTop: 12, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 6 }}>
                  <Text strong style={{ fontSize: '13px', marginBottom: 8, display: 'block' }}>
                    <DollarOutlined style={{ marginRight: 4 }} />
                    材料费用明细：
                  </Text>
                  {calculateMaterialCosts.details.map((detail, index) => (
                    <div key={index} style={{ marginBottom: 6 }}>
                      <Space size="small">
                        <Tag color="blue">{detail.materialType}</Tag>
                        <Text style={{ fontSize: '12px' }}>{detail.materialName}</Text>
                        <Text type="secondary" style={{ fontSize: '11px' }}>
                          {detail.unit === 'm²' ? `${detail.quantity.toFixed(2)} ${getChineseUnit(detail.unit)}` :
                           detail.unit === '吨' ? `${detail.quantity.toFixed(4)} ${getChineseUnit(detail.unit)}` :
                           detail.unit.includes('缺少') ? `${detail.quantity.toFixed(0)} ${getChineseUnit(detail.unit)}` :
                           `${detail.quantity.toFixed(0)} ${getChineseUnit(detail.unit)}`}
                        </Text>
                        <Text type="secondary" style={{ fontSize: '11px' }}>
                          ¥{detail.unitPrice.toFixed(2)}/{getChineseUnit(detail.unit)}
                        </Text>
                        <Text type="success" strong style={{ fontSize: '12px' }}>
                          ¥{detail.totalCost.toFixed(2)}
                        </Text>
                      </Space>
                    </div>
                  ))}
                  <Divider style={{ margin: '8px 0' }} />
                  <div style={{ textAlign: 'right' }}>
                    <Text strong style={{ fontSize: '14px' }}>
                      材料总费用: <Text type="success">¥{calculateMaterialCosts.total.toFixed(2)}</Text>
                    </Text>
                  </div>
                </div>
              )}
            </Spin>
          </div>
          <Divider style={{ margin: '12px 0' }} />
        </>
      )}

      <Descriptions column={4} size="small">
        <Descriptions.Item label="总材料张数">
          <Text strong>{totalStats.totalSheets}</Text> 张
        </Descriptions.Item>
        <Descriptions.Item label="总材料面积">
          <Text strong>{(totalStats.totalArea / 1000000).toFixed(2)}</Text> m²
        </Descriptions.Item>
        <Descriptions.Item label="平均利用率">
          <Space>
            <Progress
              percent={totalStats.avgEfficiency}
              size="small"
              style={{ width: 80 }}
              status={totalStats.avgEfficiency >= 70 ? 'success' : totalStats.avgEfficiency >= 50 ? 'normal' : 'exception'}
            />
            <Text>{totalStats.avgEfficiency.toFixed(1)}%</Text>
          </Space>
        </Descriptions.Item>
        <Descriptions.Item label="浪费率">
          <Space>
            <Progress
              percent={totalStats.wasteRate}
              size="small"
              style={{ width: 80 }}
              status={totalStats.wasteRate <= 30 ? 'success' : totalStats.wasteRate <= 50 ? 'normal' : 'exception'}
            />
            <Text>{totalStats.wasteRate.toFixed(1)}%</Text>
          </Space>
        </Descriptions.Item>
      </Descriptions>
    </Card>
  );

  // 渲染详细拼版结果
  const renderDetailedResults = () => {
    if (!showDetails || impositionResults.length === 0) return null;

    return (
      <Card
        title={
          <Space>
            <InfoCircleOutlined />
            <span>详细拼版结果</span>
          </Space>
        }
        size="small"
        style={{ marginBottom: 16 }}
      >
        <Collapse
          size="small"
          ghost
          items={impositionResults.map((result, index) => ({
            key: index.toString(),
            label: (
              <Space>
                <Tag color={result.materialType === 'face' ? 'blue' : 'orange'}>
                  {result.materialType === 'face' ? '面纸' : '灰板纸'}
                </Tag>
                <span>{result.partGroup.name}</span>
                <Text type="secondary">
                  {result.impositionX}×{result.impositionY} = {result.totalImposition}个/张
                </Text>
                <Text type="secondary">
                  利用率: {result.efficiency.toFixed(1)}%
                </Text>
              </Space>
            ),
            children: (
              <>
                <Descriptions column={2} size="small">
                  <Descriptions.Item label="拼版方式">
                    {result.impositionX} × {result.impositionY}
                    {result.isRotated && <Tag style={{ marginLeft: 8 }}>已旋转</Tag>}
                  </Descriptions.Item>
                  <Descriptions.Item label="单张数量">
                    {result.totalImposition} 个
                  </Descriptions.Item>
                  <Descriptions.Item label="成品尺寸(含出血)">
                    {result.productWidth?.toFixed(1)} × {result.productHeight?.toFixed(1)} mm
                  </Descriptions.Item>
                  <Descriptions.Item label="拼版尺寸">
                    {result.materialWidth.toFixed(1)} × {result.materialLength.toFixed(1)} mm
                  </Descriptions.Item>
                  <Descriptions.Item label="材料面积">
                    {(result.materialArea / 1000000).toFixed(3)} m²
                  </Descriptions.Item>
                  <Descriptions.Item label="浪费面积">
                    {(result.wasteArea / 1000000).toFixed(3)} m²
                  </Descriptions.Item>
                  <Descriptions.Item label="实际需要张数">
                    <Text strong style={{ color: '#1890ff' }}>{result.sheetsNeeded}</Text> 张
                  </Descriptions.Item>
                  <Descriptions.Item label="利用率">
                    <Progress
                      percent={result.efficiency}
                      size="small"
                      style={{ width: 120 }}
                      status={result.efficiency >= 70 ? 'success' : result.efficiency >= 50 ? 'normal' : 'exception'}
                    />
                  </Descriptions.Item>
                </Descriptions>

                {result.arrangementDetails && (
                  <>
                    <Divider style={{ margin: '12px 0' }} />
                    <Text strong>排列详情：</Text>
                    <div style={{ marginTop: 8 }}>
                      <Text>模式: {result.arrangementDetails.userSelectedMode}</Text>
                      <br />
                      <Text>实际: {result.arrangementDetails.actualMode}</Text>
                      <br />
                      <Text type="secondary">{result.arrangementDetails.decisionReason}</Text>
                    </div>
                  </>
                )}

                {result.warnings && result.warnings.length > 0 && (
                  <Alert
                    message="警告"
                    description={
                      <ul style={{ margin: 0, paddingLeft: 16 }}>
                        {result.warnings.map((warning, wIndex) => (
                          <li key={wIndex}>{warning}</li>
                        ))}
                      </ul>
                    }
                    type="warning"
                    showIcon
                    style={{ marginTop: 12 }}
                  />
                )}
              </>
            )
          }))}
        />
      </Card>
    );
  };

  return (
    <div>
      {renderOverallStats()}
      {renderResultsByPartGroup()}
    </div>
  );
};

export default ImpositionLogicDisplay;

