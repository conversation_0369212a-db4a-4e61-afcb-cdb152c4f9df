'use client';

import React, { useState, useEffect } from 'react';
import {
  Table, Button, Space, Modal, Form, Input,
  Select, InputNumber, Popconfirm, Card, Typography,
  message, Row, Col, Switch, Divider, Tag, Tabs
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined,
  SearchOutlined, ReloadOutlined
} from '@ant-design/icons';
import { greyBoardApi, greyBoardCuttingApi } from '@/services/adminApi';
import { GreyBoard, GreyBoardCutting } from '@/types/material';
import { createGreyBoardSchema, updateGreyBoardSchema, validateStockSize } from '@/lib/validations/admin/greyBoard';
import { useAsyncError } from '@/lib/hooks/useErrorHandler';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;

// 灰板纸密度板数据库管理页面
export default function GreyBoardManagementPage() {
  // 使用统一的错误处理Hook
  const { execute: executeGreyBoard, loading: greyBoardLoading } = useAsyncError();
  const { execute: executeGreyBoardCutting, loading: greyBoardCuttingLoading } = useAsyncError();

  // 灰板纸数据相关状态
  const [boardList, setBoardList] = useState<GreyBoard[]>([]);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [keyword, setKeyword] = useState('');
  const [category, setCategory] = useState('');
  const [categoryList, setCategoryList] = useState<string[]>([]);

  // 分切尺寸相关状态
  const [cuttingList, setCuttingList] = useState<GreyBoardCutting[]>([]);
  const [cuttingTotal, setCuttingTotal] = useState(0);
  const [cuttingCurrent, setCuttingCurrent] = useState(1);
  const [cuttingPageSize, setCuttingPageSize] = useState(10);

  // 模态框相关状态
  const [modalVisible, setModalVisible] = useState(false);
  const [cuttingModalVisible, setCuttingModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [form] = Form.useForm();
  const [cuttingForm] = Form.useForm();
  const [customSizes, setCustomSizes] = useState<number[]>([]);

  // 初始加载数据
  useEffect(() => {
    fetchList();
    fetchCategoryList();
    fetchCuttingList();
  }, []);

  // 获取灰板纸列表
  const fetchList = async (
    page = current,
    ps = pageSize,
    kw = keyword,
    cat = category
  ) => {
    const result = await executeGreyBoard(async () => {
      return await greyBoardApi.getList({
        page,
        pageSize: ps,
        keyword: kw,
        category: cat
      });
    }, '获取灰板纸列表');

    if (result) {
      setBoardList(result.list);
      setTotal(result.pagination.total);
      setCurrent(result.pagination.page);
      setPageSize(result.pagination.pageSize);
    } else {
      setBoardList([]);
      setTotal(0);
    }
  };

  // 获取材料品类列表
  const fetchCategoryList = async () => {
    const result = await executeGreyBoard(async () => {
      return await greyBoardApi.getCategoryList();
    }, '获取材料品类列表');

    if (result) {
      setCategoryList(result);
    } else {
      setCategoryList([]);
    }
  };

  // 获取分切尺寸列表
  const fetchCuttingList = async (page = cuttingCurrent, pageSize = cuttingPageSize) => {
    const result = await executeGreyBoardCutting(async () => {
      return await greyBoardCuttingApi.getList({
        page,
        pageSize
      });
    }, '获取分切尺寸列表');

    if (result) {
      setCuttingList(result.list);
      setCuttingTotal(result.pagination.total);
      setCuttingCurrent(result.pagination.page);
      setCuttingPageSize(result.pagination.pageSize);
    } else {
      setCuttingList([]);
      setCuttingTotal(0);
    }
  };

  // 处理灰板纸分页变化
  const handleTableChange = (pagination: any) => {
    setCurrent(pagination.current);
    setPageSize(pagination.pageSize);
    fetchList(pagination.current, pagination.pageSize);
  };

  // 处理分切尺寸分页变化
  const handleCuttingTableChange = (pagination: any) => {
    setCuttingCurrent(pagination.current);
    setCuttingPageSize(pagination.pageSize);
    fetchCuttingList(pagination.current, pagination.pageSize);
  };

  // 打开添加灰板纸模态框
  const showAddModal = () => {
    setModalTitle('添加灰板纸密度板');
    setEditingRecord(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 打开编辑灰板纸模态框
  const showEditModal = (record: GreyBoard) => {
    setModalTitle('编辑灰板纸密度板');
    setEditingRecord(record);
    form.setFieldsValue({
      name: record.name,
      price: record.price,
      unit: record.unit,
      weight: record.weight,
      thickness: record.thickness,
      isRegular: record.isRegular,
      isLarge: record.isLarge,
      isStockSize: record.isStockSize,
      stockLength: record.stockLength,
      stockWidth: record.stockWidth,
      category: record.category,
      remark: record.remark
    });
    setModalVisible(true);
  };

  // 打开添加分切尺寸模态框
  const showAddCuttingModal = () => {
    setModalTitle('添加分切尺寸');
    setEditingRecord(null);
    setCustomSizes([]);
    cuttingForm.resetFields();
    setCuttingModalVisible(true);
  };

  // 打开编辑分切尺寸模态框
  const showEditCuttingModal = (record: GreyBoardCutting) => {
    setModalTitle('编辑分切尺寸');
    setEditingRecord(record);
    setCustomSizes(record.sizes);
    cuttingForm.setFieldsValue({
      name: record.name,
      initialCutPrice: record.initialCutPrice
    });
    setCuttingModalVisible(true);
  };

  // 处理表单提交
  const handleFormSubmit = async () => {
    try {
      const values = await form.validateFields();

      // 处理品类数据类型问题 - mode="tags"会返回数组，需要转换为字符串
      if (Array.isArray(values.category) && values.category.length > 0) {
        values.category = values.category[0];
      }

      // 使用通用校验组件进行验证
      const schema = editingRecord ? updateGreyBoardSchema : createGreyBoardSchema;
      const validationResult = schema.safeParse({
        ...values,
        ...(editingRecord ? { id: editingRecord.id } : {})
      });

      if (!validationResult.success) {
        const errors = validationResult.error.format();
        const firstError = Object.values(errors)[0];
        if (firstError && typeof firstError === 'object' && '_errors' in firstError) {
          message.error(firstError._errors[0] || '表单验证失败');
        } else {
          message.error('表单验证失败');
        }
        return;
      }

      // 现货尺寸条件验证
      const stockSizeValidation = validateStockSize(values);
      if (!stockSizeValidation.success) {
        message.error(stockSizeValidation.error);
        return;
      }

      let result;
      if (editingRecord) {
        // 更新灰板纸
        result = await executeGreyBoard(async () => {
          return await greyBoardApi.update({
            id: editingRecord.id,
            ...values
          });
        }, '更新灰板纸');
      } else {
        // 创建灰板纸
        result = await executeGreyBoard(async () => {
          return await greyBoardApi.create(values);
        }, '创建灰板纸');
      }

      if (result) {
        message.success(editingRecord ? '灰板纸密度板更新成功' : '灰板纸密度板添加成功');
        setModalVisible(false);
        fetchList();
        fetchCategoryList();
      }
    } catch (error) {
      console.error('表单提交失败:', error);
      message.error('表单验证失败');
    }
  };

  // 修改添加分切尺寸的函数
  const handleAddSize = () => {
    const newSize = cuttingForm.getFieldValue('newSize');
    if (!newSize || newSize <= 0) {
      message.error('请输入有效的分切尺寸');
      return;
    }

    setCustomSizes(prev => [...prev, Number(newSize)]);
    cuttingForm.setFieldValue('newSize', null);
  };

  // 移除分切尺寸
  const handleRemoveSize = (size: number) => {
    setCustomSizes(prev => prev.filter(item => item !== size));
  };

  // 处理分切尺寸表单提交
  const handleCuttingFormSubmit = async () => {
    try {
      const values = await cuttingForm.validateFields();

      // 检查是否至少添加了一个尺寸
      if (customSizes.length === 0) {
        message.error('请至少添加一个分切尺寸');
        return;
      }

      const submitData = {
        ...values,
        sizes: customSizes
      };

      let result;
      if (editingRecord) {
        // 更新分切尺寸
        result = await executeGreyBoardCutting(async () => {
          return await greyBoardCuttingApi.update({
            id: editingRecord.id,
            ...submitData
          });
        }, '更新分切尺寸');
      } else {
        // 创建分切尺寸
        result = await executeGreyBoardCutting(async () => {
          return await greyBoardCuttingApi.create(submitData);
        }, '创建分切尺寸');
      }

      if (result) {
        message.success(editingRecord ? '分切尺寸更新成功' : '分切尺寸添加成功');
        setCuttingModalVisible(false);
        fetchCuttingList();
      }
    } catch (error) {
      console.error('分切尺寸表单提交失败:', error);
      message.error('表单验证失败');
    }
  };

  // 删除灰板纸
  const handleDelete = async (id: number) => {
    const result = await executeGreyBoard(async () => {
      return await greyBoardApi.delete(id);
    }, '删除灰板纸');

    if (result) {
      message.success('灰板纸密度板删除成功');
      fetchList();
    }
  };

  // 删除分切尺寸
  const handleCuttingDelete = async (id: number) => {
    const result = await executeGreyBoardCutting(async () => {
      return await greyBoardCuttingApi.delete(id);
    }, '删除分切尺寸');

    if (result) {
      message.success('分切尺寸删除成功');
      fetchCuttingList();
    }
  };

  // 灰板纸表格列定义
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center' as const,
      width: 150,
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      align: 'center' as const,
      width: 100,
      render: (text: number) => `${text} 元`,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      align: 'center' as const,
      width: 100,
    },
    {
      title: '克重',
      dataIndex: 'weight',
      key: 'weight',
      align: 'center' as const,
      width: 80,
    },
    {
      title: '厚度',
      dataIndex: 'thickness',
      key: 'thickness',
      align: 'center' as const,
      width: 80,
    },
    {
      title: '正度',
      dataIndex: 'isRegular',
      key: 'isRegular',
      align: 'center' as const,
      width: 80,
      render: (isRegular: boolean) => (
        isRegular ?
          <Tag color="blue">是</Tag> :
          <Tag color="default">否</Tag>
      ),
    },
    {
      title: '大度',
      dataIndex: 'isLarge',
      key: 'isLarge',
      align: 'center' as const,
      width: 80,
      render: (isLarge: boolean) => (
        isLarge ?
          <Tag color="green">是</Tag> :
          <Tag color="default">否</Tag>
      ),
    },
    {
      title: '按现货尺寸',
      dataIndex: 'isStockSize',
      key: 'isStockSize',
      align: 'center' as const,
      width: 100,
      render: (isStockSize: boolean, record: GreyBoard) => (
        isStockSize ? (
          <Tag color="purple">
            {record.stockLength}×{record.stockWidth}
          </Tag>
        ) : (
          <Tag color="default">否</Tag>
        )
      ),
    },
    {
      title: '品类',
      dataIndex: 'category',
      key: 'category',
      align: 'center' as const,
      width: 100,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      align: 'center' as const,
      width: 150,
      ellipsis: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      align: 'center' as const,
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'action',
      align: 'center' as const,
      width: 150,
      render: (_: any, record: GreyBoard) => (
        <Space size="small">
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => showEditModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除此灰板纸密度板吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              danger
              size="small"
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 分切尺寸表格列定义
  const cuttingColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center' as const,
      width: 150,
    },
    {
      title: '分切起步金额',
      dataIndex: 'initialCutPrice',
      key: 'initialCutPrice',
      align: 'center' as const,
      width: 150,
      render: (text: number) => `${text} 元`,
    },
    {
      title: '分切尺寸',
      dataIndex: 'sizes',
      key: 'sizes',
      align: 'center' as const,
      width: 400,
      render: (sizes: number[]) => (
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '5px', justifyContent: 'center' }}>
          {sizes.map((size, index) => (
            <Tag key={index} color="blue" style={{ marginRight: 4, height: 'auto', lineHeight: '1.5' }}>{size}</Tag>
          ))}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      align: 'center' as const,
      fixed: 'right' as const,
      width: 150,
      render: (_: any, record: GreyBoardCutting) => (
        <Space size="small">
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => showEditCuttingModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除此分切尺寸吗？"
            onConfirm={() => handleCuttingDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              danger
              size="small"
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>灰板纸密度板数据库</Title>

      <Tabs
        defaultActiveKey="1"
        items={[
          {
            key: '1',
            label: '灰板纸密度板',
            children: (
              <Card>
                <div style={{ marginBottom: 16 }}>
                  <Row gutter={16} align="middle">
                    <Col span={4}>
                      <Input
                        placeholder="搜索名称"
                        prefix={<SearchOutlined />}
                        allowClear
                        value={keyword}
                        onChange={(e) => setKeyword(e.target.value)}
                        onPressEnter={() => fetchList(1, pageSize, keyword, category)}
                      />
                    </Col>
                    <Col span={4}>
                      <Select
                        placeholder="搜索材料品类"
                        allowClear
                        showSearch
                        loading={greyBoardLoading}
                        style={{ width: '100%' }}
                        value={category || undefined}
                        onChange={(value) => setCategory(value || '')}
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          (option?.children as unknown as string).toLowerCase().includes(input.toLowerCase())
                        }
                      >
                        {categoryList.map((cat) => (
                          <Option key={cat} value={cat}>
                            {cat}
                          </Option>
                        ))}
                      </Select>
                    </Col>
                    <Col>
                      <Button
                        type="primary"
                        icon={<SearchOutlined />}
                        onClick={() => fetchList(1, pageSize, keyword, category)}
                      >
                        搜索
                      </Button>
                    </Col>
                    <Col>
                      <Button
                        icon={<ReloadOutlined />}
                        onClick={() => {
                          setKeyword('');
                          setCategory('');
                          fetchList(1, pageSize, '', '');
                        }}
                      >
                        重置
                      </Button>
                    </Col>
                    <Col flex="auto" style={{ textAlign: 'right' }}>
                      <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={showAddModal}
                      >
                        添加灰板纸密度板
                      </Button>
                    </Col>
                  </Row>
                </div>

                <Table
                  columns={columns}
                  dataSource={boardList}
                  rowKey="id"
                  pagination={{
                    current,
                    pageSize,
                    total,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                  }}
                  loading={greyBoardLoading}
                  onChange={handleTableChange}
                  bordered
                  scroll={{ x: 1200 }}
                  locale={{ emptyText: '暂无数据' }}
                  size="middle"
                />
              </Card>
            )
          },
          // {
          //   key: '2',
          //   label: '卷筒材料分切尺寸',
          //   children: (
          //     <Card>
          //       <div style={{ marginBottom: 16 }}>
          //         <Row align="middle">
          //           <Col flex="auto" style={{ textAlign: 'right' }}>
          //             <Button
          //               type="primary"
          //               icon={<PlusOutlined />}
          //               onClick={showAddCuttingModal}
          //             >
          //               添加分切尺寸
          //             </Button>
          //           </Col>
          //         </Row>
          //       </div>

          //       <Table
          //         columns={cuttingColumns}
          //         dataSource={cuttingList}
          //         rowKey="id"
          //         pagination={{
          //           current: cuttingCurrent,
          //           pageSize: cuttingPageSize,
          //           total: cuttingTotal,
          //           showSizeChanger: true,
          //           showQuickJumper: true,
          //           showTotal: (total) => `共 ${total} 条`,
          //         }}
          //         loading={greyBoardCuttingLoading}
          //         onChange={handleCuttingTableChange}
          //         bordered
          //         scroll={{ x: 800 }}
          //         locale={{ emptyText: '暂无数据' }}
          //         size="middle"
          //       />
          //     </Card>
          //   )
          // },
        ]}
      />

      {/* 灰板纸密度板表单模态框 */}
      <Modal
        title={modalTitle}
        open={modalVisible}
        onOk={handleFormSubmit}
        onCancel={() => setModalVisible(false)}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="name"
                label="名称"
                rules={[{ required: true, message: '请输入灰板纸密度板名称' }]}
              >
                <Input placeholder="请输入灰板纸密度板名称" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="category"
                label="品类"
                rules={[{ required: true, message: '请选择或输入品类' }]}
              >
                <Select
                  placeholder="请选择品类"
                  loading={greyBoardLoading}
                  showSearch
                  allowClear
                  mode="tags"
                  onChange={(value) => {
                    // 如果是数组并且有多个值，只保留最后一个
                    if (Array.isArray(value) && value.length > 1) {
                      const lastValue = value[value.length - 1];
                      form.setFieldValue('category', [lastValue]);
                    }
                  }}
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                  }
                  dropdownRender={(menu) => (
                    <>
                      {menu}
                      {categoryList.length === 0 && !greyBoardLoading && (
                        <div style={{ padding: '8px', textAlign: 'center' }}>
                          暂无数据，请输入新品类
                        </div>
                      )}
                    </>
                  )}
                >
                  {categoryList.map((cat) => (
                    <Option key={cat} value={cat}>{cat}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="price"
                label="价格"
                rules={[{ required: true, message: '请输入价格' }]}
              >
                <InputNumber
                  placeholder="请输入价格"
                  min={0}
                  style={{ width: '100%' }}
                  addonAfter="元"
                  precision={2}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="unit"
                label="单位"
                rules={[{ required: true, message: '请选择单位' }]}
              >
                <Select placeholder="请选择单位">
                  <Option value="元/张">元/张</Option>
                  <Option value="元/吨">元/吨</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="weight"
                label="克重"
                rules={[{ required: true, message: '请输入克重' }]}
              >
                <InputNumber
                  placeholder="请输入克重"
                  min={0}
                  style={{ width: '100%' }}
                  precision={1}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="thickness"
                label="厚度"
                rules={[{ required: true, message: '请输入厚度' }]}
              >
                <InputNumber
                  placeholder="请输入厚度"
                  min={0}
                  style={{ width: '100%' }}
                  precision={1}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>

            <Col span={3}>
              <Form.Item
                name="isRegular"
                label="正度"
                valuePropName="checked"
                initialValue={false}
              >
                <Switch checkedChildren="是" unCheckedChildren="否" />
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item
                name="isLarge"
                label="大度"
                valuePropName="checked"
                initialValue={false}
              >
                <Switch checkedChildren="是" unCheckedChildren="否" />
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item
                name="isStockSize"
                label="现货尺寸"
                valuePropName="checked"
                initialValue={false}
              >
                <Switch checkedChildren="是" unCheckedChildren="否" />
              </Form.Item>
            </Col>
            <Col span={15}>
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) => prevValues.isStockSize !== currentValues.isStockSize}
              >
                {({ getFieldValue }) =>
                  getFieldValue('isStockSize') ? (
                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="stockLength"
                          label="现货长度"
                          dependencies={['isStockSize']}
                          rules={[
                            ({ getFieldValue }) => ({
                              required: getFieldValue('isStockSize'),
                              message: '按现货尺寸时，请输入现货长度'
                            })
                          ]}
                        >
                          <InputNumber
                            placeholder="请输入现货长度"
                            min={0}
                            style={{ width: '100%' }}
                            precision={0}
                            disabled={form.getFieldValue('isStockSize') === false}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="stockWidth"
                          label="现货宽度"
                          dependencies={['isStockSize']}
                          rules={[
                            ({ getFieldValue }) => ({
                              required: getFieldValue('isStockSize'),
                              message: '按现货尺寸时，请输入现货宽度'
                            })
                          ]}
                        >
                          <InputNumber
                            placeholder="请输入现货宽度"
                            min={0}
                            style={{ width: '100%' }}
                            precision={0}
                            disabled={form.getFieldValue('isStockSize') === false}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  ) : null
                }
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="remark"
            label="备注"
          >
            <Input.TextArea rows={4} placeholder="请输入备注信息" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 分切尺寸表单模态框 */}
      <Modal
        title={modalTitle}
        open={cuttingModalVisible}
        onOk={handleCuttingFormSubmit}
        onCancel={() => setCuttingModalVisible(false)}
        width={600}
      >
        <Form
          form={cuttingForm}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="名称"
                rules={[{ required: true, message: '请输入名称' }]}
              >
                <Input placeholder="请输入名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="initialCutPrice"
                label="分切起步金额"
                rules={[{ required: true, message: '请输入分切起步金额' }]}
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="请输入分切起步金额"
                  addonAfter="元"
                />
              </Form.Item>
            </Col>
          </Row>

          <Divider orientation="left">分切尺寸</Divider>

          <div style={{ marginBottom: 24 }}>
            <Row gutter={16}>
              <Col span={16}>
                <Form.Item
                  name="newSize"
                  label="添加尺寸"
                  noStyle
                  tooltip="请输入大于0的数值，单位为毫米"
                >
                  <InputNumber
                    placeholder="请输入分切尺寸（mm）"
                    min={0}
                    style={{ width: '100%' }}
                    precision={0}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Button
                  type="primary"
                  style={{ width: '100%' }}
                  onClick={handleAddSize}
                >
                  添加
                </Button>
              </Col>
            </Row>
          </div>

          <div style={{ marginBottom: 16, minHeight: 120, padding: '16px', border: '1px dashed #d9d9d9', borderRadius: '8px', backgroundColor: '#fafafa' }}>
            {customSizes.length > 0 ? (
              <div style={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: '8px'
              }}>
                {customSizes.map((size) => (
                  <Tag
                    key={size}
                    color="blue"
                    style={{
                      padding: '4px 8px',
                      fontSize: '14px',
                      margin: 0
                    }}
                    closable
                    onClose={(e) => {
                      e.preventDefault();
                      handleRemoveSize(size);
                    }}
                  >
                    {size}
                  </Tag>
                ))}
              </div>
            ) : (
              <div style={{ textAlign: 'center', color: '#999', paddingTop: '32px' }}>
                暂无分切尺寸，请添加
              </div>
            )}
          </div>
        </Form>
      </Modal>
    </div>
  );
} 