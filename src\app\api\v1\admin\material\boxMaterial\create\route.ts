import { NextRequest } from 'next/server';
import { createBoxMaterialSchema, CreateBoxMaterialData } from '@/lib/validations/admin/boxMaterial';
import { prisma } from '@/lib/prisma';
import { withValidation } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';

const handler = withValidation(
  createBoxMaterialSchema,
  async (request: AuthenticatedRequest, data: CreateBoxMaterialData) => {
    const {
      code,
      facePaper,
      linerPaper,
      threeLayerBE,
      threeLayerAC,
      fiveLayerABBC,
      fiveLayerEB,
      sevenLayerEBA,
      remark,
    } = data;

    // 创建纸箱材料
    const boxMaterial = await prisma.boxMaterial.create({
      data: {
        code,
        facePaper,
        linerPaper,
        threeLayerBE: threeLayerBE || null,
        threeLayerAC: threeLayerAC || null,
        fiveLayerABBC: fiveLayerABBC || null,
        fiveLayerEB: fiveLayerEB || null,
        sevenLayerEBA: sevenLayerEBA || null,
        remark: remark || null,
        isDel: false,
      },
    });

    return successResponse(boxMaterial, '创建纸箱材料成功');
  }); 
export const POST = withInternalAuth(handler);