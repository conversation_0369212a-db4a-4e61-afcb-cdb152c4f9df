import { z } from 'zod';

// 灰板分切尺寸查询参数验证 schema
export const greyBoardCuttingQuerySchema = z.object({
  page: z.number().positive('页码必须大于0').optional(),
  pageSize: z.number().positive('每页条数必须大于0').optional(),
  keyword: z.string().optional(),
});

// 灰板分切尺寸详情查询验证 schema
export const getGreyBoardCuttingDetailSchema = z.object({
  id: z.string().transform(Number).pipe(
    z.number().positive('分切尺寸ID必须大于0')
  ),
});

// 删除灰板分切尺寸验证 schema
export const deleteGreyBoardCuttingSchema = z.object({
  id: z.string().transform(Number).pipe(
    z.number().positive('分切尺寸ID必须大于0')
  ),
});

// 灰板分切尺寸基础验证 schema
const greyBoardCuttingBaseSchema = {
  name: z.string({
    required_error: '请输入名称',
    invalid_type_error: '名称必须是字符串',
  }).min(1, '名称不能为空').max(100, '名称不能超过100个字符'),
  
  initialCutPrice: z.number({
    required_error: '请输入分切起步金额',
    invalid_type_error: '分切起步金额必须是数字',
  }).min(0, '分切起步金额不能小于0'),
  
  sizes: z.array(
    z.number({
      required_error: '请输入分切尺寸',
      invalid_type_error: '分切尺寸必须是数字',
    }).min(0, '分切尺寸不能小于0')
  ).min(1, '至少需要一个分切尺寸'),
};

// 创建灰板分切尺寸验证 schema
export const createGreyBoardCuttingSchema = z.object({
  ...greyBoardCuttingBaseSchema,
});

// 更新灰板分切尺寸验证 schema
export const updateGreyBoardCuttingSchema = z.object({
  id: z.number({
    required_error: '请提供分切尺寸ID',
    invalid_type_error: '分切尺寸ID必须是数字',
  }).positive('分切尺寸ID必须大于0'),
  ...greyBoardCuttingBaseSchema,
});

// 类型定义
export type GreyBoardCuttingQueryParams = z.infer<typeof greyBoardCuttingQuerySchema>;
export type GetGreyBoardCuttingDetailParams = z.infer<typeof getGreyBoardCuttingDetailSchema>;
export type DeleteGreyBoardCuttingParams = z.infer<typeof deleteGreyBoardCuttingSchema>;
export type CreateGreyBoardCuttingParams = z.infer<typeof createGreyBoardCuttingSchema>;
export type UpdateGreyBoardCuttingParams = z.infer<typeof updateGreyBoardCuttingSchema>; 