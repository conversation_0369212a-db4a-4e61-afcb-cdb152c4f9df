import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { deletePaperCuttingSchema, DeletePaperCuttingParams } from '@/lib/validations/admin/paperCutting';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation<DeletePaperCuttingParams>(
  deletePaperCuttingSchema,
  async (request: NextRequest, validatedQuery: DeletePaperCuttingParams) => {
    const data = validatedQuery;

    // 检查分切尺寸是否存在
    const existingPaperCutting = await prisma.paperCutting.findFirst({
      where: {
        id: data.id,
        isDel: false,
      },
    });

    assertExists(existingPaperCutting, ErrorCode.MATERIAL_NOT_FOUND, '分切尺寸不存在');

    // 软删除分切尺寸
    await prisma.paperCutting.update({
      where: { id: data.id },
      data: {
        isDel: true,
      },
    });

    return successResponse({ id: data.id }, '删除分切尺寸成功');
  }
); 