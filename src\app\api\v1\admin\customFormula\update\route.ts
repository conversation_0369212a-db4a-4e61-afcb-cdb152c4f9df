import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { UpdateCustomFormulaParams, updateCustomFormulaSchema } from '@/lib/validations/admin/customFormula';
import { withValidation, assert, assertExists } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation<UpdateCustomFormulaParams>(
  updateCustomFormulaSchema,
  async (request: AuthenticatedRequest, validatedData: UpdateCustomFormulaParams) => {
    const data = validatedData;

    // 检查公式是否存在
    const existingFormula = await prisma.customFormula.findFirst({
      where: {
        id: data.id,
        isDel: false,
      },
    });

    assertExists(existingFormula, ErrorCode.FORMULA_NOT_FOUND, '自定义公式不存在');

    // 如果修改了名称，检查名称是否重复
    if (data.name && data.name !== existingFormula!.name) {
      const duplicateFormula = await prisma.customFormula.findFirst({
        where: {
          name: data.name,
          id: { not: data.id },
          isDel: false,
        },
      });

      assert(!duplicateFormula, ErrorCode.FORMULA_NAME_EXISTS, '公式名称已存在');
    }

    // 开始数据库事务
    const result = await prisma.$transaction(async (tx) => {
      // 更新自定义公式基本信息
      const formula = await tx.customFormula.update({
        where: { id: data.id },
        data: {
          name: data.name,
          initialAmount: data.initialAmount,
          expression: data.expression,
          status: data.status,
        },
      });

      // 更新属性 - 先删除旧的，再创建新的
      if (data.attributes !== undefined) {
        await tx.customFormulaAttribute.deleteMany({
          where: { formulaId: data.id },
        });

        if (data.attributes.length > 0) {
          await tx.customFormulaAttribute.createMany({
            data: data.attributes.map((attr) => ({
              formulaId: data.id,
              name: attr.name,
              value: attr.value,
            })),
          });
        }
      }

      return formula;
    });

    return successResponse(result, '更新自定义公式成功');
  }
); 
export const POST = withInternalAuth(handler);