import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { paperCuttingQuerySchema, PaperCuttingQueryParams } from '@/lib/validations/admin/paperCutting';
import { withValidation } from '@/lib/middleware/errorHandler';
import { paginatedResponse } from '@/lib/utils/apiResponse';
import { Prisma } from '@prisma/client';

export const POST = withValidation<PaperCuttingQueryParams>(
  paperCuttingQuerySchema,
  async (request: NextRequest, validatedQuery: PaperCuttingQueryParams) => {
      const data = validatedQuery;
      const page = data.page || 1;
      const pageSize = data.pageSize || 10;
      const skip = (page - 1) * pageSize;
      
      // 构建查询条件
      const where: Prisma.PaperCuttingWhereInput = {
        isDel: false
      };
      
      if (data.keyword) {
        where.name = { contains: data.keyword };
      }
      
      // 查询总数和列表数据
      const [total, list] = await Promise.all([
        prisma.paperCutting.count({ where }),
        prisma.paperCutting.findMany({
          where,
          select: {
            id: true,
            name: true,
            initialCutPrice: true,
            sizes: true,
            createdAt: true,
            updatedAt: true,
          },
          skip,
          take: pageSize,
          orderBy: { createdAt: 'desc' },
        })
      ]);
      
      // 处理sizes字段，确保前端接收到的是数组
      const formattedList = list.map(item => ({
        ...item,
        sizes: item.sizes ? (Array.isArray(item.sizes) ? item.sizes : JSON.parse(String(item.sizes))) : []
      }));
      
      return paginatedResponse(
        formattedList,
        {
          page,
          pageSize,
          total,
        },
        '获取分切尺寸列表成功'
      );
  }
); 