import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withE<PERSON>r<PERSON>and<PERSON> } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';

export const POST = withErrorHandler(
  async (request: AuthenticatedRequest) => {
    // 获取固定参数配置（应该只有一条）
    let params = await prisma.processingParams.findFirst();

    // 如果不存在配置，创建默认配置
    if (!params) {
      params = await prisma.processingParams.create({
        data: {
          pvcFilm: 0,
          slottingSalary: 0,
          slottingBasePrice: 0,
          blisterPlate: 0,
          blisterBasePrice: 0,
          highFrequencyPlate: 0,
          highFrequencyBasePrice: 0,
          sprayCodeFee: 0,
          sprayCodeBasePrice: 0,
          inspectionFee: 0,
          inspectionBasePrice: 0
        }
      });
    }

    return successResponse(params, '获取固定参数配置成功');
  }
); 