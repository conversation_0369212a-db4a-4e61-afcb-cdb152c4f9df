import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const getPrintingMachineDetailSchema = z.object({
  id: z.coerce.number().positive('ID必须是正整数')
});

const handler = withValidation(
  getPrintingMachineDetailSchema,
  async (request: AuthenticatedRequest, validatedQuery: any) => {
    const data = validatedQuery;

    // 查询印刷机数据详细信息
    const printingMachine = await prisma.printingMachine.findFirst({
      where: {
        id: data.id,
        isDel: false,
      },
    });

    assertExists(printingMachine, ErrorCode.RESOURCE_NOT_FOUND, '印刷机数据不存在');

    return successResponse(printingMachine, '获取印刷机详情成功');
  }
); 
export const POST = withInternalAuth(handler);