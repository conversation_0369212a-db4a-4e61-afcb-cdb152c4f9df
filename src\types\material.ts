// 材料尺寸配置类型
export interface MaterialSize {
  id: number;
  name: string; // 尺寸名称，如"正度长度"
  type: string; // 尺寸类型标识，如"regularLength"
  size: number; // 尺寸值，单位为mm
  description?: string; // 中文描述说明
  sortOrder: number; // 显示排序
  isDefault: boolean; // 是否为默认值
  createdAt: Date;
  updatedAt: Date;
}

// 材料尺寸类型枚举
export enum MaterialSizeType {
  REGULAR_LENGTH = 'regularLength',   // 正度长度
  REGULAR_WIDTH = 'regularWidth',     // 正度宽度
  LARGE_LENGTH = 'largeLength',       // 大度长度
  LARGE_WIDTH = 'largeWidth',         // 大度宽度
  SPECIAL_LENGTH = 'specialLength',   // 特规长度
  SPECIAL_WIDTH = 'specialWidth'      // 特规宽度
}

// 材料尺寸配置对象
export interface MaterialSizeConfig {
  regularLength: number;    // 正度长 1092
  regularWidth: number;     // 正度宽 787
  largeLength: number;      // 大度长 1194
  largeWidth: number;       // 大度宽 889
  specialLength: number;    // 特规长 787
  specialWidth: number;     // 特规宽 1092
}

// 材料尺寸更新参数
export interface MaterialSizeUpdateParams {
  config: MaterialSizeConfig;
}

// 材料单位枚举
export enum MaterialUnit {
  // 通用单位
  YUAN_PER_SQUARE = '元/平方', // 元/平方
  YUAN_PER_TON = '元/吨', // 元/吨
  YUAN_PER_SHEET = '元/张', // 元/张
  YUAN_PER_PAIR = '元/对', // 元/对
  YUAN_PER_METER = '元/米', // 元/米
  YUAN_PER_STRIP = '元/条', // 元/条
  YUAN_PER_CUBIC = '元/立方', // 元/立方
}

// 纸类材料单位类型
export type PaperUnit = MaterialUnit.YUAN_PER_SQUARE | MaterialUnit.YUAN_PER_TON | MaterialUnit.YUAN_PER_SHEET;

// 灰板纸单位类型
export type GreyBoardUnit = MaterialUnit.YUAN_PER_SHEET | MaterialUnit.YUAN_PER_TON;

// 不干胶单位类型
export type StickerUnit = MaterialUnit.YUAN_PER_SQUARE | MaterialUnit.YUAN_PER_SHEET;

// 配件单位类型
export type AccessoryUnit = MaterialUnit.YUAN_PER_PAIR | MaterialUnit.YUAN_PER_METER | MaterialUnit.YUAN_PER_STRIP | MaterialUnit.YUAN_PER_SQUARE;

// 礼盒配件单位类型
export type GiftBoxAccessoryUnit = MaterialUnit.YUAN_PER_CUBIC | MaterialUnit.YUAN_PER_SQUARE;

// 纸类材料类型
export interface Paper {
  id: number;
  name: string; // 纸张名称
  price: number; // 价格
  unit: PaperUnit; // 单位
  weight: number; // 克重
  thickness: number; // 厚度
  regularPrice?: number; // 正度价格
  largePrice?: number; // 大度价格
  category: string; // 材料品类（书写纸、双胶纸等）
  remark?: string; // 备注
  isDel: boolean; // 是否删除
  createdAt: Date;
  updatedAt: Date;
}

// 卷筒材料分切尺寸类型
export interface PaperCutting {
  id: number;
  name: string; // 名称
  initialCutPrice: number; // 分切起步金额
  sizes: number[]; // 分切尺寸数组
  isDel: boolean; // 是否删除
  createdAt: Date;
  updatedAt: Date;
}

import { 
  SpecialPaperBase,
  CreateSpecialPaperParams,
  UpdateSpecialPaperParams,
  GetSpecialPaperDetailParams,
  GetSpecialPaperListParams,
  DeleteSpecialPaperParams
} from '@/lib/validations/admin/specialPaper';

// 特种纸类型
export interface SpecialPaper extends SpecialPaperBase {
  id: number;
  isDel: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 特种纸创建参数
export type SpecialPaperCreateParams = CreateSpecialPaperParams;

// 特种纸更新参数
export type SpecialPaperUpdateParams = UpdateSpecialPaperParams;

// 特种纸详情参数
export type SpecialPaperDetailParams = GetSpecialPaperDetailParams;

// 特种纸列表参数
export type SpecialPaperListParams = GetSpecialPaperListParams;

// 特种纸删除参数
export type SpecialPaperDeleteParams = DeleteSpecialPaperParams;

// 灰板纸密度板类型
export interface GreyBoard {
  id: number;
  name: string; // 名称
  price: number; // 价格
  unit: GreyBoardUnit; // 单位
  weight: number; // 克重
  thickness: number; // 厚度
  isRegular: boolean; // 是否正度
  isLarge: boolean; // 是否大度
  isStockSize: boolean; // 是否按现货尺寸
  stockLength?: number; // 现货长度
  stockWidth?: number; // 现货宽度
  category: string; // 品类
  remark?: string; // 备注
  isDel: boolean; // 是否删除
  createdAt: Date;
  updatedAt: Date;
}

// 灰板材料分切尺寸类型
export interface GreyBoardCutting {
  id: number;
  name: string; // 名称
  initialCutPrice: number; // 分切起步金额
  sizes: number[]; // 分切尺寸数组
  isDel: boolean; // 是否删除
  createdAt: Date;
  updatedAt: Date;
}

// 不干胶类型
export interface Sticker {
  id: number;
  name: string; // 名称
  price: number; // 价格
  unit: StickerUnit; // 单位
  weight: number; // 克重
  category: string; // 材料品类
  remark?: string; // 备注
  isDel: boolean; // 是否删除
  createdAt: Date;
  updatedAt: Date;
}

import { 
  SpecialMaterialBase,
  CreateSpecialMaterialParams,
  UpdateSpecialMaterialParams,
  GetSpecialMaterialDetailParams,
  GetSpecialMaterialListParams,
  DeleteSpecialMaterialParams
} from '@/lib/validations/admin/specialMaterial';

// 特殊材料类型
export interface SpecialMaterial extends SpecialMaterialBase {
  id: number;
  isDel: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 特殊材料创建参数
export type SpecialMaterialCreateParams = CreateSpecialMaterialParams;

// 特殊材料更新参数
export type SpecialMaterialUpdateParams = UpdateSpecialMaterialParams;

// 特殊材料详情参数
export type SpecialMaterialDetailParams = GetSpecialMaterialDetailParams;

// 特殊材料列表参数
export type SpecialMaterialListParams = GetSpecialMaterialListParams;

// 特殊材料删除参数
export type SpecialMaterialDeleteParams = DeleteSpecialMaterialParams;

// 配件类型
export interface Accessory {
  id: number;
  name: string; // 名称
  price: number; // 价格
  initialPrice: number; // 起步价
  weight: number; // 重量
  unit: AccessoryUnit; // 单位
  remark?: string; // 备注
  isDel: boolean; // 是否删除
  createdAt: Date;
  updatedAt: Date;
}

// 礼盒配件类型
export interface GiftBoxAccessory {
  id: number;
  name: string; // 名称
  price: number; // 价格
  unit: GiftBoxAccessoryUnit; // 单位（元/立方、元/平方）
  isStockSize: boolean; // 是否按现货尺寸
  stockLength?: number; // 现货长度
  stockWidth?: number; // 现货宽度
  remark?: string; // 备注
  isDel: boolean; // 是否删除
  createdAt: Date;
  updatedAt: Date;
}

// 配件创建参数
export interface AccessoryCreateParams {
  name: string;
  price: number;
  initialPrice: number;
  weight: number;
  unit: AccessoryUnit;
  remark?: string;
}

// 配件更新参数
export interface AccessoryUpdateParams extends AccessoryCreateParams {
  id: number;
}

// 礼盒配件创建参数
export interface GiftBoxAccessoryCreateParams {
  name: string;
  price: number;
  unit: GiftBoxAccessoryUnit;
  isStockSize: boolean;
  stockLength?: number;
  stockWidth?: number;
  remark?: string;
}

// 礼盒配件更新参数
export interface GiftBoxAccessoryUpdateParams extends GiftBoxAccessoryCreateParams {
  id: number;
}

// 纸箱材料类型
export interface BoxMaterial {
  id: number;
  code: string; // 编号
  facePaper: string; // 面纸
  linerPaper: string; // 里纸
  threeLayerBE?: number; // 三层B/E
  threeLayerAC?: number; // 三层A/C
  fiveLayerABBC?: number; // 五层AB/BC
  fiveLayerEB?: number; // 五层EB
  sevenLayerEBA?: number; // 七层EBA
  remark?: string; // 备注
  isDel: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// API 请求参数类型
export interface MaterialListParams {
  page?: number;
  pageSize?: number;
  keyword?: string;
  category?: string;
}

// 纸类材料创建参数
export interface PaperCreateParams {
  name: string;
  price: number;
  unit: string;
  weight: number;
  thickness: number;
  regularPrice?: number;
  largePrice?: number;
  category: string;
  remark?: string;
}

// 纸类材料更新参数
export interface PaperUpdateParams extends PaperCreateParams {
  id: number;
}

// 纸类分切尺寸创建参数
export interface PaperCuttingCreateParams {
  name: string;
  initialCutPrice: number;
  sizes: number[];
}

// 纸类分切尺寸更新参数
export interface PaperCuttingUpdateParams extends PaperCuttingCreateParams {
  id: number;
}

// 灰板纸创建参数
export interface GreyBoardCreateParams {
  name: string;
  price: number;
  unit: string;
  weight: number;
  thickness: number;
  isRegular: boolean;
  isLarge: boolean;
  isStockSize: boolean;
  stockLength?: number;
  stockWidth?: number;
  category: string;
  remark?: string;
}

// 灰板纸更新参数
export interface GreyBoardUpdateParams extends GreyBoardCreateParams {
  id: number;
}

// 灰板分切创建参数
export interface GreyBoardCuttingCreateParams {
  name: string;
  initialCutPrice: number;
  sizes: number[];
}

// 灰板分切更新参数
export interface GreyBoardCuttingUpdateParams extends GreyBoardCuttingCreateParams {
  id: number;
}

// 不干胶创建参数
export interface StickerCreateParams {
  name: string;
  price: number;
  unit: string; // "元/平方" | "元/张"
  weight: number;
  category: string;
  remark?: string;
}

// 不干胶更新参数
export interface StickerUpdateParams extends StickerCreateParams {
  id: number;
}

// 纸箱材料创建参数
export interface BoxMaterialCreateParams {
  code: string;
  facePaper: string;
  linerPaper: string;
  threeLayerBE?: number;
  threeLayerAC?: number;
  fiveLayerABBC?: number;
  fiveLayerEB?: number;
  sevenLayerEBA?: number;
  remark?: string;
}

// 纸箱材料更新参数
export interface BoxMaterialUpdateParams extends BoxMaterialCreateParams {
  id: number;
}

// 纸箱材料列表查询参数
export interface BoxMaterialListParams extends MaterialListParams {
  code?: string;
} 