import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { CreateCorrugatedRateData, createCorrugatedRateSchema } from '@/lib/validations/admin/corrugatedProcess';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation(
  createCorrugatedRateSchema,
  async (request: AuthenticatedRequest, validatedData: CreateCorrugatedRateData) => {
    const data = validatedData;

    // 检查楞形是否已配置
    const existingCorrugatedRate = await prisma.corrugatedRate.findFirst({
      where: {
        fluteType: data.fluteType,
        isDel: false,
      },
    });

    assert(!existingCorrugatedRate, ErrorCode.DUPLICATE_ENTRY, '该楞形已配置瓦楞率');

    // 创建瓦楞率配置
    const corrugatedRate = await prisma.corrugatedRate.create({
      data: {
        fluteType: data.fluteType,
        rate: data.rate,
      },
    });

    return successResponse(corrugatedRate, '创建瓦楞率配置成功');
  }
); 
export const POST = withInternalAuth(handler);