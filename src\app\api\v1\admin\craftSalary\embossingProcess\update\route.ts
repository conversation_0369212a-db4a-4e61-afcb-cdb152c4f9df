import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { updateEmbossingProcessSchema } from '@/lib/validations/admin/embossingProcess';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation(
  updateEmbossingProcessSchema,
  async (request: NextRequest, validatedData: any) => {
    const { id, ...data } = validatedData;

    // 检查凹凸工艺是否存在
    const existingEmbossingProcess = await prisma.embossingProcess.findFirst({
      where: {
        id,
        isDel: false,
      },
    });

    assert(!!existingEmbossingProcess, ErrorCode.NOT_FOUND, '凹凸工艺不存在');

    // 检查名称是否重复（排除自己）
    const duplicateEmbossingProcess = await prisma.embossingProcess.findFirst({
      where: {
        name: data.name,
        isDel: false,
        id: { not: id },
      },
    });

    assert(!duplicateEmbossingProcess, ErrorCode.DUPLICATE_ENTRY, '凹凸工艺名称已存在');

    // 更新凹凸工艺
    const embossingProcess = await prisma.embossingProcess.update({
      where: { id },
      data: {
        name: data.name,
        price: data.price,
        unit: data.unit,
        basePrice: data.basePrice,
        salary: data.salary,
        salaryBasePrice: data.salaryBasePrice,
        remark: data.remark || null,
      },
    });

    return successResponse(
      embossingProcess,
      '更新凹凸工艺成功'
    );
  }
); 