'use client';

import React, { useState, useEffect } from 'react';
import {
  Table, Button, Space, Modal, Form, Input,
  Select, InputNumber, Popconfirm, Card, Typography,
  message, Row, Col, Tag, Tabs
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined,
  SearchOutlined, ReloadOutlined, SaveOutlined
} from '@ant-design/icons';
import { processingFeeApi, processingParamsApi } from '@/services/adminApi';
import {
  ProcessingFee,
  ProcessingParams,
  PROCESSING_FEE_UNITS,
  ProcessingFeeUnit
} from '@/types/craftSalary';
import { useAsyncError } from '@/lib/hooks/useErrorHandler';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

/**
 * 加工费管理页面
 */
export default function ProcessingFeeManagementPage() {
  // 错误处理Hook
  const { execute: executeFee, loading: feeLoading } = useAsyncError();
  const { execute: executeParams, loading: paramsLoading } = useAsyncError();

  // Tab状态
  const [activeTab, setActiveTab] = useState('fee');

  // 加工费数据相关状态
  const [feeList, setFeeList] = useState<ProcessingFee[]>([]);
  const [feeTotal, setFeeTotal] = useState(0);
  const [feeCurrent, setFeeCurrent] = useState(1);
  const [feePageSize, setFeePageSize] = useState(10);
  const [feeKeyword, setFeeKeyword] = useState('');
  const [unitFilter, setUnitFilter] = useState<ProcessingFeeUnit | undefined>();

  // 固定参数状态
  const [paramsData, setParamsData] = useState<ProcessingParams | null>(null);

  // 加工费模态框相关状态
  const [feeModalVisible, setFeeModalVisible] = useState(false);
  const [feeModalTitle, setFeeModalTitle] = useState('');
  const [editingFeeRecord, setEditingFeeRecord] = useState<any>(null);
  const [feeForm] = Form.useForm();

  // 固定参数表单
  const [paramsForm] = Form.useForm();

  // 初始加载数据
  useEffect(() => {
    fetchFeeList();
    if (activeTab === 'params') {
      fetchParamsData();
    }
  }, []);

  // Tab切换时加载对应数据
  useEffect(() => {
    if (activeTab === 'params' && !paramsData) {
      fetchParamsData();
    }
  }, [activeTab]);

  // 获取加工费数据列表
  const fetchFeeList = async (page = feeCurrent, pageSize_ = feePageSize, search = feeKeyword, unit = unitFilter) => {
    const requestParams: any = {
      page,
      pageSize: pageSize_,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    };

    if (search) {
      requestParams.search = search;
    }

    if (unit) {
      requestParams.unit = unit;
    }

    const result = await executeFee(async () => {
      return await processingFeeApi.getList(requestParams);
    }, '获取加工费列表');

    if (result) {
      setFeeList(result.list || []);
      setFeeTotal(result.pagination?.total || 0);
    } else {
      setFeeList([]);
      setFeeTotal(0);
    }
  };

  // 获取固定参数配置
  const fetchParamsData = async () => {
    const result = await executeParams(async () => {
      return await processingParamsApi.get();
    }, '获取固定参数配置');

    if (result) {
      setParamsData(result);
      // 填充表单
      paramsForm.setFieldsValue({
        pvcFilm: result.pvcFilm,
        slottingSalary: result.slottingSalary,
        slottingBasePrice: result.slottingBasePrice,
        blisterPlate: result.blisterPlate,
        blisterBasePrice: result.blisterBasePrice,
        highFrequencyPlate: result.highFrequencyPlate,
        highFrequencyBasePrice: result.highFrequencyBasePrice,
        sprayCodeFee: result.sprayCodeFee,
        sprayCodeBasePrice: result.sprayCodeBasePrice,
        inspectionFee: result.inspectionFee,
        inspectionBasePrice: result.inspectionBasePrice
      });
    }
  };

  // 处理加工费分页变化
  const handleFeeTableChange = (pagination: any) => {
    setFeeCurrent(pagination.current);
    setFeePageSize(pagination.pageSize);
    fetchFeeList(pagination.current, pagination.pageSize);
  };

  // 搜索处理
  const handleSearch = () => {
    setFeeCurrent(1);
    fetchFeeList(1, feePageSize, feeKeyword, unitFilter);
  };

  // 重置搜索
  const handleReset = () => {
    setFeeKeyword('');
    setUnitFilter(undefined);
    setFeeCurrent(1);
    fetchFeeList(1, feePageSize, '', undefined);
  };

  // 单位筛选处理
  const handleUnitFilter = (value: ProcessingFeeUnit | undefined) => {
    setUnitFilter(value);
    setFeeCurrent(1);
    fetchFeeList(1, feePageSize, feeKeyword, value);
  };

  // 打开添加加工费模态框
  const showAddFeeModal = () => {
    setFeeModalTitle('添加加工费');
    setEditingFeeRecord(null);
    feeForm.resetFields();
    feeForm.setFieldsValue({
      unitPrice: 0,
      basePrice: 0
    });
    setFeeModalVisible(true);
  };

  // 打开编辑加工费模态框
  const showEditFeeModal = (record: ProcessingFee) => {
    setFeeModalTitle('编辑加工费');
    setEditingFeeRecord(record);
    feeForm.setFieldsValue({
      name: record.name,
      unitPrice: record.unitPrice,
      unit: record.unit,
      basePrice: record.basePrice,
      remark: record.remark || ''
    });
    setFeeModalVisible(true);
  };

  // 处理加工费表单提交
  const handleFeeFormSubmit = async () => {
    try {
      const values = await feeForm.validateFields();

      const requestData = {
        name: values.name,
        unitPrice: Number(values.unitPrice),
        unit: values.unit,
        basePrice: Number(values.basePrice),
        remark: values.remark || undefined
      };

      const result = await executeFee(async () => {
        if (editingFeeRecord) {
          return await processingFeeApi.update({ ...requestData, id: editingFeeRecord.id });
        } else {
          return await processingFeeApi.create(requestData);
        }
      }, editingFeeRecord ? '更新加工费' : '创建加工费');

      if (result) {
        setFeeModalVisible(false);
        fetchFeeList();
        message.success(editingFeeRecord ? '更新成功' : '创建成功');
      }
    } catch (error) {
      console.error('表单提交失败:', error);
    }
  };

  // 删除加工费
  const handleFeeDelete = async (id: number) => {
    const result = await executeFee(async () => {
      return await processingFeeApi.delete(id);
    }, '删除加工费');

    if (result) {
      fetchFeeList();
      message.success('删除成功');
    }
  };

  // 保存固定参数配置
  const handleParamsSave = async () => {
    try {
      const values = await paramsForm.validateFields();

      const requestData = {
        pvcFilm: Number(values.pvcFilm),
        slottingSalary: Number(values.slottingSalary),
        slottingBasePrice: Number(values.slottingBasePrice),
        blisterPlate: Number(values.blisterPlate),
        blisterBasePrice: Number(values.blisterBasePrice),
        highFrequencyPlate: Number(values.highFrequencyPlate),
        highFrequencyBasePrice: Number(values.highFrequencyBasePrice),
        sprayCodeFee: Number(values.sprayCodeFee),
        sprayCodeBasePrice: Number(values.sprayCodeBasePrice),
        inspectionFee: Number(values.inspectionFee),
        inspectionBasePrice: Number(values.inspectionBasePrice)
      };

      const result = await executeParams(async () => {
        return await processingParamsApi.update(requestData);
      }, '保存固定参数配置');

      if (result) {
        fetchParamsData();
        message.success('保存成功');
      }
    } catch (error) {
      console.error('保存配置失败:', error);
    }
  };

  // 加工费表格列定义
  const feeColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      align: 'center' as const,
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      width: 120,
      render: (value: number) => `¥${value.toFixed(3)}`,
      align: 'center' as const,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      width: 120,
      render: (unit: ProcessingFeeUnit) => (
        <Tag color="blue">{unit}</Tag>
      ),
      align: 'center' as const,
    },
    {
      title: '起步价',
      dataIndex: 'basePrice',
      key: 'basePrice',
      width: 120,
      render: (value: number) => `¥${value.toFixed(2)}`,
      align: 'center' as const,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      ellipsis: true,
      width: 280,
      align: 'center' as const,
      render: (value: string) => value || '-',
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 180,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss'),
      align: 'center' as const,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      align: 'center' as const,
      fixed: 'right' as const,
      render: (_: any, record: ProcessingFee) => (
        <Space size="small">
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => showEditFeeModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除吗？"
            onConfirm={() => handleFeeDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // Tab内容配置
  const tabItems = [
    {
      key: 'fee',
      label: '加工费配置',
      children: (
        <Card>
          {/* 搜索和筛选 */}
          <Row gutter={16} align="middle" style={{ marginBottom: 16 }}>
            <Col span={4}>
              <Input
                placeholder="搜索名称或备注"
                prefix={<SearchOutlined />}
                allowClear
                value={feeKeyword}
                onChange={(e) => setFeeKeyword(e.target.value)}
                onPressEnter={handleSearch}
              />
            </Col>
            <Col>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={handleSearch}
              >
                搜索
              </Button>
            </Col>
            <Col>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleReset}
              >
                重置
              </Button>
            </Col>
            <Col flex="auto" style={{ textAlign: 'right' }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={showAddFeeModal}
              >
                新增加工费
              </Button>
            </Col>
          </Row>

          {/* 表格 */}
          <Table
            columns={feeColumns}
            dataSource={feeList}
            rowKey="id"
            loading={feeLoading}
            pagination={{
              current: feeCurrent,
              pageSize: feePageSize,
              total: feeTotal,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条`,
            }}
            onChange={handleFeeTableChange}
            bordered
            size="middle"
            scroll={{ x: 1200 }}
            locale={{ emptyText: '暂无数据' }}
          />
        </Card>
      )
    },
    {
      key: 'params',
      label: '固定参数配置',
      children: (
        <Card>
          <Form
            form={paramsForm}
            layout="vertical"
            onFinish={handleParamsSave}
          >
            {/* 第一行：PVC贴膜 + 开槽工资 */}
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={12}>
                <Card title="PVC贴膜" size="small">
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="pvcFilm"
                        label="价格 (元/吨)"
                        rules={[
                          { required: true, message: '请输入PVC贴膜价格' }
                        ]}
                      >
                        <InputNumber
                          placeholder="请输入价格"
                          min={0}
                          precision={2}
                          style={{ width: '100%' }}
                          addonAfter="元/吨"
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="开槽工资" size="small">
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="slottingSalary"
                        label="工资 (元/个)"
                        rules={[
                          { required: true, message: '请输入开槽工资' }
                        ]}
                      >
                        <InputNumber
                          placeholder="请输入工资"
                          min={0}
                          precision={2}
                          style={{ width: '100%' }}
                          addonAfter="元/个"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="slottingBasePrice"
                        label="起步价"
                        rules={[
                          { required: true, message: '请输入开槽起步价' }
                        ]}
                      >
                        <InputNumber
                          placeholder="请输入起步价"
                          min={0}
                          precision={2}
                          style={{ width: '100%' }}
                          addonAfter="元"
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>
              </Col>
            </Row>

            {/* 第二行：吸塑版 + 高频机版 */}
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={12}>
                <Card title="吸塑版" size="small">
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="blisterPlate"
                        label="价格 (元/平方)"
                        rules={[
                          { required: true, message: '请输入吸塑版价格' }
                        ]}
                      >
                        <InputNumber
                          placeholder="请输入价格"
                          min={0}
                          precision={2}
                          style={{ width: '100%' }}
                          addonAfter="元/平方"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="blisterBasePrice"
                        label="起步价"
                        rules={[
                          { required: true, message: '请输入吸塑起步价' }
                        ]}
                      >
                        <InputNumber
                          placeholder="请输入起步价"
                          min={0}
                          precision={2}
                          style={{ width: '100%' }}
                          addonAfter="元"
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="高频机版" size="small">
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="highFrequencyPlate"
                        label="价格 (元/平方)"
                        rules={[
                          { required: true, message: '请输入高频机版价格' }
                        ]}
                      >
                        <InputNumber
                          placeholder="请输入价格"
                          min={0}
                          precision={2}
                          style={{ width: '100%' }}
                          addonAfter="元/平方"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="highFrequencyBasePrice"
                        label="起步价"
                        rules={[
                          { required: true, message: '请输入高频机起步价' }
                        ]}
                      >
                        <InputNumber
                          placeholder="请输入起步价"
                          min={0}
                          precision={2}
                          style={{ width: '100%' }}
                          addonAfter="元"
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>
              </Col>
            </Row>

            {/* 第三行：喷码费用 + 检验费用 */}
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col span={12}>
                <Card title="喷码费用" size="small">
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="sprayCodeFee"
                        label="费用 (元/个)"
                        rules={[
                          { required: true, message: '请输入喷码费用' }
                        ]}
                      >
                        <InputNumber
                          placeholder="请输入费用"
                          min={0}
                          precision={2}
                          style={{ width: '100%' }}
                          addonAfter="元/个"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="sprayCodeBasePrice"
                        label="起步价"
                        rules={[
                          { required: true, message: '请输入喷码起步价' }
                        ]}
                      >
                        <InputNumber
                          placeholder="请输入起步价"
                          min={0}
                          precision={2}
                          style={{ width: '100%' }}
                          addonAfter="元"
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="检验费用" size="small">
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="inspectionFee"
                        label="费用 (元/个)"
                        rules={[
                          { required: true, message: '请输入检验费用' }
                        ]}
                      >
                        <InputNumber
                          placeholder="请输入费用"
                          min={0}
                          precision={2}
                          style={{ width: '100%' }}
                          addonAfter="元/个"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="inspectionBasePrice"
                        label="起步价"
                        rules={[
                          { required: true, message: '请输入检验起步价' }
                        ]}
                      >
                        <InputNumber
                          placeholder="请输入起步价"
                          min={0}
                          precision={2}
                          style={{ width: '100%' }}
                          addonAfter="元"
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>
              </Col>
            </Row>

            {/* 保存按钮 */}
            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={paramsLoading}
                icon={<SaveOutlined />}
                size="large"
              >
                保存配置
              </Button>
            </Form.Item>
          </Form>
        </Card>
      )
    }
  ];

  return (
    <div>
      <Title level={2}>加工费管理</Title>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
      />

      {/* 编辑模态框 */}
      <Modal
        title={feeModalTitle}
        open={feeModalVisible}
        onOk={handleFeeFormSubmit}
        onCancel={() => setFeeModalVisible(false)}
        confirmLoading={feeLoading}
        width={600}
        destroyOnHidden
      >
        <Form
          form={feeForm}
          layout="vertical"
          preserve={false}
        >
          <Form.Item
            name="name"
            label="名称"
            rules={[
              { required: true, message: '请输入名称' },
              { max: 100, message: '名称长度不能超过100个字符' }
            ]}
          >
            <Input placeholder="请输入加工费名称" />
          </Form.Item>

          <Form.Item
            name="unitPrice"
            label="单价"
            rules={[
              { required: true, message: '请输入单价' }
            ]}
          >
            <InputNumber
              placeholder="请输入单价"
              min={0}
              precision={3}
              style={{ width: '100%' }}
              addonAfter="元"
            />
          </Form.Item>

          <Form.Item
            name="unit"
            label="单位"
            rules={[
              { required: true, message: '请选择单位' }
            ]}
          >
            <Select placeholder="请选择单位">
              {PROCESSING_FEE_UNITS.map(unit => (
                <Option key={unit} value={unit}>{unit}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="basePrice"
            label="起步价"
            rules={[
              { required: true, message: '请输入起步价' }
            ]}
          >
            <InputNumber
              placeholder="请输入起步价"
              min={0}
              precision={2}
              style={{ width: '100%' }}
              addonAfter="元"
            />
          </Form.Item>

          <Form.Item
            name="remark"
            label="备注"
            rules={[
              { max: 500, message: '备注长度不能超过500个字符' }
            ]}
          >
            <TextArea
              placeholder="请输入备注信息"
              rows={3}
              maxLength={500}
              showCount
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
} 