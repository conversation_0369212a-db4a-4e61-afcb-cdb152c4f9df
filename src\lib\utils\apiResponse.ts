import { NextResponse } from 'next/server';
import { ErrorCode, ErrorMessages, ApiError } from '@/lib/constants/errorCodes';
import { ApiResponse, ErrorResponse, PaginatedData } from '@/types/common';

/**
 * 分页信息类型
 */
export interface Pagination {
  page: number;
  pageSize: number;
  total: number;
}

/**
 * 生成成功响应
 * @param data 响应数据
 * @param message 成功消息
 * @param statusCode HTTP状态码，默认200
 */
export function successResponse<T>(
  data: T,
  message: string = '操作成功',
  statusCode: number = 200
): NextResponse {
  const response: ApiResponse<T> = {
    code: ErrorCode.SUCCESS,
    message,
    data,
    timestamp: new Date().toISOString(),
  };

  return NextResponse.json(response, { status: statusCode });
}

export function successFileResponse(
  data: Buffer,
  headers: Record<string, string> = {}
): NextResponse {
  return new NextResponse(data, {
    headers: {
      'Content-Type': 'application/octet-stream',
      ...headers,
    },
  });
}

/**
 * 生成分页响应
 * @param list 数据列表
 * @param pagination 分页信息
 * @param message 成功消息
 */
export function paginatedResponse<T>(
  list: T[],
  pagination: Pagination,
  message: string = '获取成功'
): NextResponse {
  const data: PaginatedData<T> = {
    list,
    pagination,
  };

  return successResponse(data, message);
}

/**
 * 生成错误响应
 * @param code 错误码
 * @param message 错误消息（可选，默认使用错误码对应的消息）
 * @param errors 详细错误信息
 * @param statusCode HTTP状态码（可选，自动根据错误码推断）
 */
export function errorResponse(
  code: ErrorCode,
  message?: string,
  errors?: any,
  statusCode?: number
): NextResponse {
  const finalMessage = message || ErrorMessages[code] || ErrorMessages[ErrorCode.UNKNOWN_ERROR];
  const finalStatusCode = statusCode || 200;

  const response: ErrorResponse = {
    code,
    message: finalMessage,
    errors,
    timestamp: new Date().toISOString(),
  };

  return NextResponse.json(response, { status: finalStatusCode });
}

/**
 * 生成验证错误响应
 * @param errors Zod验证错误详情
 * @param message 自定义错误消息
 */
export function validationErrorResponse(
  errors: any,
  message: string = '参数验证失败'
): NextResponse {
  return errorResponse(ErrorCode.INVALID_PARAMETERS, message, errors);
}

/**
 * 生成业务错误响应
 * @param code 业务错误码
 * @param message 自定义错误消息
 * @param details 错误详情
 */
export function businessErrorResponse(
  code: ErrorCode,
  message?: string,
  details?: any
): NextResponse {
  return errorResponse(code, message, details);
}

/**
 * 生成系统错误响应
 * @param error 错误对象
 * @param message 自定义错误消息
 */
export function systemErrorResponse(
  error?: Error | unknown,
  message: string = '服务器内部错误'
): NextResponse {
  console.error('系统错误:', error);
  
  const details = error instanceof Error ? {
    name: error.name,
    message: error.message,
    stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
  } : undefined;

  return errorResponse(ErrorCode.INTERNAL_SERVER_ERROR, message, details);
}
