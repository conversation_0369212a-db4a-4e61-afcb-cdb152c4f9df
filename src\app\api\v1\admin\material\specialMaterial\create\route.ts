import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createSpecialMaterialSchema, validateStockSize, CreateSpecialMaterialParams } from '@/lib/validations/admin/specialMaterial';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation<CreateSpecialMaterialParams>(
  createSpecialMaterialSchema,
  async (request: NextRequest, validatedData: CreateSpecialMaterialParams) => {
    // 验证现货尺寸条件
    const stockSizeValidation = validateStockSize(validatedData);
    assert(stockSizeValidation.success, ErrorCode.INVALID_PARAMETERS, stockSizeValidation.error || '现货尺寸验证失败');

    // 检查特殊材料名称是否已存在
    const existingMaterial = await prisma.specialMaterial.findFirst({
      where: {
        name: validatedData.name,
        isDel: false,
      },
    });

    assert(!existingMaterial, ErrorCode.MATERIAL_NAME_EXISTS, '特殊材料名称已存在');

    // 创建特殊材料
    const specialMaterial = await prisma.specialMaterial.create({
      data: {
        name: validatedData.name,
        price: validatedData.price,
        unit: validatedData.unit,
        thickness: validatedData.thickness,
        density: validatedData.density,
        isStockSize: validatedData.isStockSize,
        stockLength: validatedData.isStockSize ? validatedData.stockLength : null,
        stockWidth: validatedData.isStockSize ? validatedData.stockWidth : null,
        category: validatedData.category,
        remark: validatedData.remark || null,
        isDel: false,
      },
    });

    return successResponse(specialMaterial, '创建特殊材料成功');
  }
); 