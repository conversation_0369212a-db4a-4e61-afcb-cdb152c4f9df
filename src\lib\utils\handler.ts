import { message } from 'antd';
import { ZodError } from 'zod';

interface ApiError {
  code: number;
  message: string;
  errors?: any;
}

/**
 * 统一处理API错误
 * @param error 错误对象
 * @param defaultMessage 默认错误信息
 */
export const handleApiError = (error: any, defaultMessage: string = '操作失败') => {
  console.error('API Error:', error);

  // 如果是API返回的错误
  if (error.response?.data) {
    const apiError: ApiError = error.response.data;   
    // 显示API返回的错误信息
    message.error(apiError.message || defaultMessage);
    return;
  }

  // 处理网络错误
  if (error.message === 'Network Error') {
    message.error('网络连接失败，请检查网络设置');
    return;
  }

  // 处理请求超时
  if (error.code === 'ECONNABORTED') {
    message.error('请求超时，请稍后重试');
    return;
  }

  // 其他错误
  message.error(error.message || defaultMessage);
};

/**
 * 统一处理表单验证错误
 * @param error Zod验证错误
 * @param defaultMessage 默认错误信息
 */
export const handleValidationError = (error: ZodError, defaultMessage: string = '表单验证失败') => {
  if (error.issues.length > 0) {
    const firstError = error.issues[0];
    message.error(firstError.message || defaultMessage);
    return;
  }
  message.error(defaultMessage);
};

/**
 * 统一处理错误
 * @param error 错误对象
 * @param defaultMessage 默认错误信息
 */
export const handleError = (error: any, defaultMessage: string = '操作失败') => {
  if (error instanceof ZodError) {
    handleValidationError(error, defaultMessage);
  } else {
    handleApiError(error, defaultMessage);
  }
};

/**
 * 统一处理成功消息
 * @param msg 成功消息
 */
export const handleSuccess = (msg: string) => {
  message.success(msg);
}; 