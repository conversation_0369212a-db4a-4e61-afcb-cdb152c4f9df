'use client';

import React, { useState, useEffect } from 'react';
import {
  Table, Button, Space, Modal, Form, Input,
  Select, InputNumber, Popconfirm, Card, Typography,
  message, Row, Col, Tag
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined,
  SearchOutlined, ReloadOutlined
} from '@ant-design/icons';
import { silkScreenProcessApi } from '@/services/adminApi';
import { SilkScreenProcess, SILK_SCREEN_PROCESS_UNITS, SilkScreenProcessUnit } from '@/types/craftSalary';
import { useAsyncError } from '@/lib/hooks/useErrorHandler';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

/**
 * 丝印工艺管理页面
 */
export default function SilkScreenProcessManagementPage() {
  // 错误处理Hook
  const { execute: executeSilkScreenProcess, loading: silkScreenProcessLoading } = useAsyncError();

  // 数据相关状态
  const [silkScreenProcessList, setSilkScreenProcessList] = useState<SilkScreenProcess[]>([]);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [keyword, setKeyword] = useState('');
  const [unit, setUnit] = useState<SilkScreenProcessUnit | ''>('');

  // 模态框相关状态
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [form] = Form.useForm();

  // 初始加载数据
  useEffect(() => {
    fetchSilkScreenProcessList();
  }, []);

  // 获取丝印工艺数据列表
  const fetchSilkScreenProcessList = async (page = current, pageSize_ = pageSize, search = keyword, unit_ = unit) => {
    const requestParams: any = {
      page,
      pageSize: pageSize_,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    };
    
    if (search) {
      requestParams.search = search;
    }
    
    if (unit_) {
      requestParams.unit = unit_;
    }
    
    const result = await executeSilkScreenProcess(async () => {
      return await silkScreenProcessApi.getList(requestParams);
    }, '获取丝印工艺列表');

    if (result) {
      setSilkScreenProcessList(result.list || []);
      setTotal(result.pagination?.total || 0);
    } else {
      setSilkScreenProcessList([]);
      setTotal(0);
    }
  };

  // 处理分页变化
  const handleTableChange = (pagination: any) => {
    setCurrent(pagination.current);
    setPageSize(pagination.pageSize);
    fetchSilkScreenProcessList(pagination.current, pagination.pageSize);
  };

  // 打开添加模态框
  const showAddModal = () => {
    setModalTitle('添加丝印工艺');
    setEditingRecord(null);
    form.resetFields();
    form.setFieldsValue({
      unitPrice: 0,
      basePrice: 0,
      materialFee: 0,
      unit: '元/个'
    });
    setModalVisible(true);
  };

  // 打开编辑模态框
  const showEditModal = (record: SilkScreenProcess) => {
    setModalTitle('编辑丝印工艺');
    setEditingRecord(record);
    form.setFieldsValue({
      name: record.name,
      unitPrice: record.unitPrice,
      unit: record.unit,
      basePrice: record.basePrice,
      materialFee: record.materialFee,
      remark: record.remark || ''
    });
    setModalVisible(true);
  };

  // 处理表单提交
  const handleFormSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      const requestData = {
        name: values.name,
        unitPrice: Number(values.unitPrice),
        unit: values.unit,
        basePrice: Number(values.basePrice),
        materialFee: Number(values.materialFee),
        remark: values.remark || ''
      };

      const result = await executeSilkScreenProcess(async () => {
        if (editingRecord) {
          return await silkScreenProcessApi.update({ ...requestData, id: editingRecord.id });
        } else {
          return await silkScreenProcessApi.create(requestData);
        }
      }, editingRecord ? '更新丝印工艺' : '创建丝印工艺');

      if (result) {
        message.success('保存成功');
        setModalVisible(false);
        fetchSilkScreenProcessList();
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 删除丝印工艺
  const handleDelete = async (id: number) => {
    const result = await executeSilkScreenProcess(async () => {
      return await silkScreenProcessApi.delete(id);
    }, '删除丝印工艺');

    if (result) {
      fetchSilkScreenProcessList();
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center' as const,
      width: 150,
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      align: 'center' as const,
      width: 100,
      render: (price: number) => price > 0 ? `¥${price.toFixed(2)}` : '-'
    },
    {
      title: '计价单位',
      dataIndex: 'unit',
      key: 'unit',
      align: 'center' as const,
      width: 100,
      render: (unit: string) => (
        <Tag color="green">{unit}</Tag>
      )
    },
    {
      title: '起步价',
      dataIndex: 'basePrice',
      key: 'basePrice',
      align: 'center' as const,
      width: 100,
      render: (price: number) => price > 0 ? `¥${price.toFixed(2)}` : '-'
    },
    {
      title: '材料费',
      dataIndex: 'materialFee',
      key: 'materialFee',
      align: 'center' as const,
      width: 100,
      render: (fee: number) => fee > 0 ? `¥${fee.toFixed(2)}` : '-'
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      align: 'center' as const,
      width: 200,
      ellipsis: true,
      render: (remark: string) => remark || '-'
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      align: 'center' as const,
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'action',
      align: 'center' as const,
      fixed: 'right' as const,
      width: 150,
      render: (_: any, record: SilkScreenProcess) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => showEditModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此丝印工艺吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="是"
            cancelText="否"
          >
            <Button
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>丝印工艺管理</Title>

      <Card>
        <div style={{ marginBottom: 16 }}>
          <Row gutter={16} align="middle">
            <Col span={4}>
              <Input
                placeholder="搜索名称或备注"
                prefix={<SearchOutlined />}
                allowClear
                value={keyword}
                onChange={(e) => setKeyword(e.target.value)}
                onPressEnter={() => fetchSilkScreenProcessList(1, pageSize, keyword, unit)}
              />
            </Col>
            <Col>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={() => fetchSilkScreenProcessList(1, pageSize, keyword, unit)}
              >
                搜索
              </Button>
            </Col>
            <Col>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => {
                  setKeyword('');
                  setUnit('');
                  fetchSilkScreenProcessList(1, pageSize, '', '');
                }}
              >
                重置
              </Button>
            </Col>
            <Col flex="auto" style={{ textAlign: 'right' }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={showAddModal}
              >
                添加丝印工艺
              </Button>
            </Col>
          </Row>
        </div>

        <Table
          columns={columns}
          dataSource={silkScreenProcessList}
          rowKey="id"
          pagination={{
            current: current,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
          }}
          loading={silkScreenProcessLoading}
          onChange={handleTableChange}
          bordered
          size="middle"
          scroll={{ x: 1000 }}
          locale={{ emptyText: '暂无数据' }}
        />
      </Card>

      {/* 表单模态框 */}
      <Modal
        title={modalTitle}
        open={modalVisible}
        onOk={handleFormSubmit}
        onCancel={() => setModalVisible(false)}
        width={600}
        okText="确定"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="名称"
            rules={[
              { required: true, message: '请输入丝印工艺名称' },
              { max: 100, message: '名称长度不能超过100字符' }
            ]}
          >
            <Input placeholder="请输入丝印工艺名称" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="unitPrice"
                label="单价"
                rules={[{ required: true, message: '请输入单价' }]}
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="0.00"
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="unit"
                label="计价单位"
                rules={[{ required: true, message: '请选择计价单位' }]}
              >
                <Select placeholder="请选择计价单位">
                  {SILK_SCREEN_PROCESS_UNITS.map(unit => (
                    <Option key={unit} value={unit}>{unit}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="basePrice"
                label="起步价"
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="0.00"
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="materialFee"
                label="材料费"
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="0.00"
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="remark"
            label="备注"
          >
            <TextArea
              placeholder="请输入备注信息（可选）"
              rows={3}
              showCount
              maxLength={500}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
} 