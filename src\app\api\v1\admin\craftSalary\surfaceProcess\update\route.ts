import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { UpdateSurfaceProcessData, updateSurfaceProcessSchema } from '@/lib/validations/admin/surfaceProcess';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation(
  updateSurfaceProcessSchema,
  async (request: NextRequest, validatedData: UpdateSurfaceProcessData) => {
    const { id, ...data } = validatedData;

    // 检查覆膜工艺是否存在
    const existingSurfaceProcess = await prisma.surfaceProcess.findFirst({
      where: {
        id: id,
        isDel: false,
      },
    });

    assert(!!existingSurfaceProcess, ErrorCode.NOT_FOUND, '覆膜工艺不存在');

    // 检查名称是否与其他记录重复
    const duplicateSurfaceProcess = await prisma.surfaceProcess.findFirst({
      where: {
        name: data.name,
        id: { not: id },
        isDel: false,
      },
    });

    assert(!duplicateSurfaceProcess, ErrorCode.DUPLICATE_ENTRY, '覆膜工艺名称已存在');

    // 更新覆膜工艺
    const surfaceProcess = await prisma.surfaceProcess.update({
      where: { id },
      data: {
        name: data.name,
        price: data.price,
        unit: data.unit,
        basePrice: data.basePrice,
        thickness: data.thickness,
        density: data.density,
        remark: data.remark,
      },
    });

    return successResponse(surfaceProcess, '更新覆膜工艺成功');
  }
); 