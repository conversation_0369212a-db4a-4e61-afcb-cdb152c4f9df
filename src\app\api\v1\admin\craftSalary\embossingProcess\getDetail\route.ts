import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';
import { z } from 'zod';

const getDetailSchema = z.object({
  id: z.number().positive('ID必须是正整数'),
});

export const POST = withValidation(
  getDetailSchema,
  async (request: NextRequest, validatedData: any) => {
    const { id } = validatedData;

    // 查询凹凸工艺详情
    const embossingProcess = await prisma.embossingProcess.findFirst({
      where: {
        id,
        isDel: false,
      },
    });

    assert(!!embossingProcess, ErrorCode.NOT_FOUND, '凹凸工艺不存在');

    return successResponse(
      embossingProcess,
      '获取凹凸工艺详情成功'
    );
  }
); 