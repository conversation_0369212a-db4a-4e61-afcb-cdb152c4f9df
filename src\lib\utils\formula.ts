import * as math from 'mathjs';
import { pinyin } from 'pinyin-pro';

const chineseRegex = /[\u4e00-\u9fa5]/g;

// 将中文变量转为拼音（驼峰式）
export function translateChineseToPinyin(expr: string) {
  return expr.replace(chineseRegex, (char) => {
    // 中文转拼音，去除声调，首字母大写（如 '长' -> 'Chang'）
    return pinyin(char, { toneType: 'none', type: 'array' })[0]
      .replace(/^./, match => match.toUpperCase()); // 首字母大写
  });
}

/**
 * 从公式表达式中提取属性名称
 * @param attributes 属性列表
 * @param expression 公式表达式
 * @returns 属性名称数组
 */
export function extractAttributeNames(expression: string): string[] {
  try {
    const pinyinExpr = translateChineseToPinyin(expression);
    const node = math.parse(pinyinExpr);
    const symbols = new Set<string>();

    // 遍历语法树，提取所有变量名
    node.traverse((node) => {
      if (node.type === 'SymbolNode' && 'name' in node) {
        symbols.add((node as { name: string }).name);
      }
    });

    return Array.from(symbols);
  } catch (error) {
    return [];
  }
}

/**
 * 验证公式中引用的属性是否都存在
 * @param expression 公式表达式
 * @param attributes 已定义的属性列表
 * @returns 验证结果对象
 */
export function validateFormulaAttributes(
  expression: string,
  attributes: { name: string, code: string }[]
): { isValid: boolean; missingAttributes: string[] } {
  const attributeNames = extractAttributeNames(expression);
  const definedNames = new Set(attributes.map(attr => attr.code));
  const missingAttributes = attributeNames.filter(name => !definedNames.has(name));
  return {
    isValid: missingAttributes.length === 0,
    missingAttributes
  };
}

/**
 * 验证属性名称是否重复
 * @param attributes 属性列表
 * @returns 验证结果对象
 */
export function validateAttributeNames(
  attributes: { name: string }[]
): { isValid: boolean; duplicateNames: string[] } {
  const nameCount = new Map<string, number>();
  const duplicateNames = new Set<string>();

  // 统计每个属性名出现的次数
  attributes.forEach(attr => {
    const count = nameCount.get(attr.name) || 0;
    if (count > 0) {
      duplicateNames.add(attr.name);
    }
    nameCount.set(attr.name, count + 1);
  });

  return {
    isValid: duplicateNames.size === 0,
    duplicateNames: Array.from(duplicateNames)
  };
} 