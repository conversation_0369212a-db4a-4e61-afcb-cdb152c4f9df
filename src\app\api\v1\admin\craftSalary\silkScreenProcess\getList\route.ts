import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { silkScreenProcessListParamsSchema } from '@/lib/validations/admin/silkScreenProcess';
import { withValidation } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { paginatedResponse } from '@/lib/utils/apiResponse';
import { Prisma } from '@prisma/client';

const handler = withValidation(
  silkScreenProcessListParamsSchema,
  async (request: AuthenticatedRequest, validatedQuery: any) => {
    const data = validatedQuery;
    const page = data.page ?? 1;
    const pageSize = data.pageSize ?? 10;
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const where: Prisma.SilkScreenProcessWhereInput = {
      isDel: false,
    };

    if (data.search) {
      where.OR = [
        { name: { contains: data.search } },
        { remark: { contains: data.search } },
      ];
    }

    if (data.unit) {
      where.unit = data.unit;
    }

    // 构建排序条件
    const sortBy = data.sortBy ?? 'createdAt';
    const sortOrder = data.sortOrder ?? 'desc';
    const orderBy: Prisma.SilkScreenProcessOrderByWithRelationInput = {
      [sortBy]: sortOrder
    } as Prisma.SilkScreenProcessOrderByWithRelationInput;

    // 查询总数和列表数据
    const [total, list] = await Promise.all([
      prisma.silkScreenProcess.count({ where }),
      prisma.silkScreenProcess.findMany({
        where,
        orderBy,
        skip,
        take: pageSize,
      })
    ]);

    return paginatedResponse(
      list,
      {
        page,
        pageSize,
        total,
      },
      '获取丝印工艺列表成功'
    );
  }
); 
export const POST = withInternalAuth(handler);