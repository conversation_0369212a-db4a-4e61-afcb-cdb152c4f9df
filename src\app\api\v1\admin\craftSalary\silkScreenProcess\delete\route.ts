import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const deleteSilkScreenProcessSchema = z.object({
  id: z.number().int().positive(),
});

export const POST = withValidation(
  deleteSilkScreenProcessSchema,
  async (request: NextRequest, validatedData: z.infer<typeof deleteSilkScreenProcessSchema>) => {
    const { id } = validatedData;

    // 检查丝印工艺是否存在
    const existingSilkScreenProcess = await prisma.silkScreenProcess.findFirst({
      where: {
        id: id,
        isDel: false,
      },
    });

    assert(!!existingSilkScreenProcess, ErrorCode.NOT_FOUND, '丝印工艺不存在');

    // 软删除丝印工艺
    await prisma.silkScreenProcess.update({
      where: { id },
      data: { isDel: true },
    });

    return successResponse({ id }, '删除丝印工艺成功');
  }
); 