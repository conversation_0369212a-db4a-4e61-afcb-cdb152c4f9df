import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { updateHotStampingPlateFeeSchema } from '@/lib/validations/admin/hotStampingProcess';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation(
  updateHotStampingPlateFeeSchema,
  async (request: NextRequest, validatedData: any) => {
    const { id, ...data } = validatedData;

    // 检查烫金版费是否存在
    const existingHotStampingPlateFee = await prisma.hotStampingPlateFee.findFirst({
      where: {
        id: id,
        isDel: false,
      },
    });

    assert(!!existingHotStampingPlateFee, ErrorCode.NOT_FOUND, '烫金版费不存在');

    // 检查名称是否重复（排除自己）
    const duplicateHotStampingPlateFee = await prisma.hotStampingPlateFee.findFirst({
      where: {
        name: data.name,
        isDel: false,
        id: { not: id },
      },
    });

    assert(!duplicateHotStampingPlateFee, ErrorCode.DUPLICATE_ENTRY, '烫金版费名称已存在');

    // 更新烫金版费
    const updatedHotStampingPlateFee = await prisma.hotStampingPlateFee.update({
      where: { id: id },
      data: {
        name: data.name,
        price: data.price,
        unit: data.unit,
        basePrice: data.basePrice,
        remark: data.remark || null,
      },
    });

    return successResponse(
      updatedHotStampingPlateFee,
      '更新烫金版费成功'
    );
  }
); 