import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { deleteBoxSchema, DeleteBoxParams } from '@/lib/validations/admin/box';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation<DeleteBoxParams>(
  deleteBoxSchema,
  async (request: NextRequest, validatedQuery: DeleteBoxParams) => {
    const data = validatedQuery;

    // 检查盒型是否存在
    const existingBox = await prisma.box.findFirst({
      where: {
        id: data.id,
        isDel: false,
      },
    });

    assertExists(existingBox, ErrorCode.BOX_NOT_FOUND, '盒型不存在');

    // 软删除盒型
    const result = await prisma.box.update({
      where: { id: data.id },
      data: {
        isDel: true,
      },
    });

    return successResponse(
      { id: result.id },
      '删除盒型成功'
    );
  }
); 