'use client';

import React, { useState, useEffect } from 'react';
import { 
  Form, 
  Input, 
  Button, 
  Checkbox, 
  Card, 
  Typography, 
  message,
  Row,
  Col,
  Divider,
  Space
} from 'antd';
import { 
  UserOutlined, 
  LockOutlined, 
  MobileOutlined, 
  MailOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { LoginRequest } from '@/types/user';
import { login, setAuthInfo, checkAuthStatus } from '@/services/auth';

const { Title, Text } = Typography;

interface LoginFormValues {
  username: string;
  password: string;
  rememberMe: boolean;
}

export default function LoginPage() {
  const [form] = Form.useForm<LoginFormValues>();
  const [loading, setLoading] = useState(false);
  const [loginType, setLoginType] = useState<'phone' | 'email' | 'username'>('phone');
  const router = useRouter();

  // 检查是否已登录
  useEffect(() => {
    const checkAuth = async () => {
      const isAuthenticated = await checkAuthStatus();
      if (isAuthenticated) {
        router.replace('/');
      }
    };
    checkAuth();
  }, [router]);

  // 处理登录
  const handleLogin = async (values: LoginFormValues) => {
    setLoading(true);
    try {
      const loginData: LoginRequest = {
        username: values.username.trim(),
        password: values.password,
        rememberMe: values.rememberMe
      };

      const result = await login(loginData);
      
      if (result.success && result.data) {
        message.success('登录成功');
        
        // 保存用户信息到本地存储
        setAuthInfo(result.data.user, result.data.token);
        
        // 跳转到首页或之前访问的页面
        const redirectUrl = new URLSearchParams(window.location.search).get('redirect') || '/';
        router.replace(redirectUrl);
      } else {
        message.error(result.error?.message || '登录失败');
      }
    } catch (error) {
      console.error('登录失败:', error);
      message.error('登录失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 获取用户名输入框的占位符和图标
  const getUsernameConfig = () => {
    switch (loginType) {
      case 'phone':
        return {
          placeholder: '请输入手机号',
          prefix: <MobileOutlined />,
          rules: [
            { required: true, message: '请输入手机号' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }
          ]
        };
      case 'email':
        return {
          placeholder: '请输入邮箱地址',
          prefix: <MailOutlined />,
          rules: [
            { required: true, message: '请输入邮箱地址' },
            { type: 'email' as const, message: '请输入有效的邮箱地址' }
          ]
        };
      default:
        return {
          placeholder: '请输入用户名',
          prefix: <UserOutlined />,
          rules: [
            { required: true, message: '请输入用户名' },
            { min: 2, message: '用户名至少2个字符' }
          ]
        };
    }
  };

  const usernameConfig = getUsernameConfig();

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <Card
        style={{
          width: '100%',
          maxWidth: '400px',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          borderRadius: '12px'
        }}
        styles={{ body: {padding: '40px'} }}
      >
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <Title level={2} style={{ color: '#1890ff', marginBottom: '8px' }}>
            用户登录
          </Title>
          <Text type="secondary">
            欢迎回来，请输入您的登录信息
          </Text>
        </div>

        <Form
          form={form}
          name="login"
          onFinish={handleLogin}
          autoComplete="off"
          size="large"
          initialValues={{
            rememberMe: true
          }}
        >
          {/* 登录方式切换 */}
          <div style={{ marginBottom: '24px' }}>
            <Space size="large" style={{ width: '100%', justifyContent: 'center' }}>
              <Button
                type={loginType === 'phone' ? 'primary' : 'text'}
                size="small"
                onClick={() => setLoginType('phone')}
              >
                手机号登录
              </Button>
              <Button
                type={loginType === 'email' ? 'primary' : 'text'}
                size="small"
                onClick={() => setLoginType('email')}
              >
                邮箱登录
              </Button>
              <Button
                type={loginType === 'username' ? 'primary' : 'text'}
                size="small"
                onClick={() => setLoginType('username')}
              >
                用户名登录
              </Button>
            </Space>
          </div>

          <Form.Item
            name="username"
            rules={usernameConfig.rules}
          >
            <Input
              prefix={usernameConfig.prefix}
              placeholder={usernameConfig.placeholder}
              autoComplete="username"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码至少6个字符' }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请输入密码"
              autoComplete="current-password"
              iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            />
          </Form.Item>

          <Form.Item>
            <Row justify="space-between" align="middle">
              <Col>
                <Form.Item name="rememberMe" valuePropName="checked" noStyle>
                  <Checkbox>记住我</Checkbox>
                </Form.Item>
              </Col>
              <Col>
                <Button type="link" size="small">
                  忘记密码？
                </Button>
              </Col>
            </Row>
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              block
              style={{
                height: '48px',
                fontSize: '16px',
                fontWeight: 'bold'
              }}
            >
              {loading ? '登录中...' : '登录'}
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
}
