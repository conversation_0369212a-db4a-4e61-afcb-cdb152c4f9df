import { z } from 'zod';
import { PRINTING_UNITS } from '@/types/craftSalary';

/**
 * 印刷模块数据验证模式
 */

// 印刷计价单位验证
export const printingUnitSchema = z.enum(PRINTING_UNITS, {
  errorMap: () => ({ message: '请选择有效的计价单位' })
});

// 价格验证模式 - 允许0但不允许负数
const priceSchema = z.number({
  required_error: '价格是必填项',
  invalid_type_error: '价格必须是数字'
}).min(0, '价格不能为负数');

// 尺寸验证模式 - 必须大于0
const sizeSchema = z.number({
  required_error: '尺寸是必填项',
  invalid_type_error: '尺寸必须是数字'
}).positive('尺寸必须大于0');

// 基础印刷数据验证模式
const basePrintingSchema = z.object({
  machineModel: z.string({
    required_error: '印刷机型是必填项'
  }).min(1, '印刷机型不能为空').max(100, '印刷机型长度不能超过100字符'),
  
  basePrice: priceSchema,
  price1000_1999: priceSchema,
  price2000_2999: priceSchema,
  price3000_3999: priceSchema,
  price4000_4999: priceSchema,
  price5000_5999: priceSchema,
  price6000_6999: priceSchema,
  price7000_7999: priceSchema,
  price8000_8999: priceSchema,
  price9000_9999: priceSchema,
  price10000Plus: z.number({
    required_error: '10000以上价格是必填项',
    invalid_type_error: '10000以上价格必须是数字'
  }).positive('10000以上价格必须大于0'),
  
  unit: printingUnitSchema,
  ctpPlateFee: priceSchema,
  spotColorFee: priceSchema,
  
  remark: z.string().max(500, '备注长度不能超过500字符').optional()
});

// 创建印刷数据验证模式
export const createPrintingSchema = basePrintingSchema.refine((data) => {
  // 验证至少有一个价格区间大于0（除了price10000Plus，它已经要求必须大于0）
  const priceFields = [
    data.basePrice,
    data.price1000_1999,
    data.price2000_2999,
    data.price3000_3999,
    data.price4000_4999,
    data.price5000_5999,
    data.price6000_6999,
    data.price7000_7999,
    data.price8000_8999,
    data.price9000_9999
  ];
  
  return priceFields.some(price => price > 0) || data.price10000Plus > 0;
}, {
  message: '至少需要设置一个价格区间的价格大于0',
  path: ['basePrice'] // 错误显示在基础价格字段
});

// 更新印刷数据验证模式
export const updatePrintingSchema = basePrintingSchema.partial().extend({
  id: z.number().positive('ID必须是正整数')
});

// 创建印刷机验证模式
export const createPrintingMachineSchema = z.object({
  machineName: z.string({
    required_error: '印刷机名称是必填项'
  }).min(1, '印刷机名称不能为空').max(100, '印刷机名称长度不能超过100字符'),
  
  maxLength: sizeSchema,
  maxWidth: sizeSchema,
  
  remark: z.string().max(500, '备注长度不能超过500字符').optional()
});

// 更新印刷机验证模式
export const updatePrintingMachineSchema = createPrintingMachineSchema.partial().extend({
  id: z.number().positive('ID必须是正整数')
});

// 查询参数验证模式
export const printingListParamsSchema = z.object({
  page: z.number().int().positive().optional(),
  pageSize: z.number().int().positive().max(100, '每页最多100条').optional(),
  search: z.string().max(100, '搜索关键词长度不能超过100字符').optional(),
  unit: printingUnitSchema.optional(),
  sortBy: z.enum(['createdAt', 'updatedAt', 'machineModel', 'basePrice']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
});

export const printingMachineListParamsSchema = z.object({
  page: z.number().int().positive().optional(),
  pageSize: z.number().int().positive().max(100, '每页最多100条').optional(),
  search: z.string().max(100, '搜索关键词长度不能超过100字符').optional(),
  sortBy: z.enum(['createdAt', 'updatedAt', 'machineName']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
});

// 价格计算参数验证模式
export const priceCalculationParamsSchema = z.object({
  quantity: z.number().int().positive('数量必须是正整数'),
  printingId: z.number().int().positive('印刷配置ID必须是正整数'),
  length: z.number().positive('长度必须大于0').optional(),
  width: z.number().positive('宽度必须大于0').optional()
});

// 表单数据验证模式（字符串转数字）
export const printingFormSchema = z.object({
  machineModel: z.string().min(1, '印刷机型不能为空').max(100, '印刷机型长度不能超过100字符'),
  basePrice: z.string().refine((val) => !isNaN(Number(val)) && Number(val) >= 0, {
    message: '起步价必须是大于等于0的数字'
  }),
  price1000_1999: z.string().refine((val) => !isNaN(Number(val)) && Number(val) >= 0, {
    message: '1000-1999价格必须是大于等于0的数字'
  }),
  price2000_2999: z.string().refine((val) => !isNaN(Number(val)) && Number(val) >= 0, {
    message: '2000-2999价格必须是大于等于0的数字'
  }),
  price3000_3999: z.string().refine((val) => !isNaN(Number(val)) && Number(val) >= 0, {
    message: '3000-3999价格必须是大于等于0的数字'
  }),
  price4000_4999: z.string().refine((val) => !isNaN(Number(val)) && Number(val) >= 0, {
    message: '4000-4999价格必须是大于等于0的数字'
  }),
  price5000_5999: z.string().refine((val) => !isNaN(Number(val)) && Number(val) >= 0, {
    message: '5000-5999价格必须是大于等于0的数字'
  }),
  price6000_6999: z.string().refine((val) => !isNaN(Number(val)) && Number(val) >= 0, {
    message: '6000-6999价格必须是大于等于0的数字'
  }),
  price7000_7999: z.string().refine((val) => !isNaN(Number(val)) && Number(val) >= 0, {
    message: '7000-7999价格必须是大于等于0的数字'
  }),
  price8000_8999: z.string().refine((val) => !isNaN(Number(val)) && Number(val) >= 0, {
    message: '8000-8999价格必须是大于等于0的数字'
  }),
  price9000_9999: z.string().refine((val) => !isNaN(Number(val)) && Number(val) >= 0, {
    message: '9000-9999价格必须是大于等于0的数字'
  }),
  price10000Plus: z.string().refine((val) => !isNaN(Number(val)) && Number(val) > 0, {
    message: '10000以上价格必须是大于0的数字'
  }),
  unit: printingUnitSchema,
  ctpPlateFee: z.string().refine((val) => !isNaN(Number(val)) && Number(val) >= 0, {
    message: 'CTP板费必须是大于等于0的数字'
  }),
  spotColorFee: z.string().refine((val) => !isNaN(Number(val)) && Number(val) >= 0, {
    message: '专色费必须是大于等于0的数字'
  }),
  remark: z.string().max(500, '备注长度不能超过500字符').optional()
});

export const printingMachineFormSchema = z.object({
  machineName: z.string().min(1, '印刷机名称不能为空').max(100, '印刷机名称长度不能超过100字符'),
  maxLength: z.string().refine((val) => !isNaN(Number(val)) && Number(val) > 0, {
    message: '最大长度必须是大于0的数字'
  }),
  maxWidth: z.string().refine((val) => !isNaN(Number(val)) && Number(val) > 0, {
    message: '最大宽度必须是大于0的数字'
  }),
  remark: z.string().max(500, '备注长度不能超过500字符').optional()
});

// 工具函数：将表单数据转换为API请求数据
export const transformPrintingFormData = (formData: z.infer<typeof printingFormSchema>) => {
  return {
    machineModel: formData.machineModel,
    basePrice: Number(formData.basePrice),
    price1000_1999: Number(formData.price1000_1999),
    price2000_2999: Number(formData.price2000_2999),
    price3000_3999: Number(formData.price3000_3999),
    price4000_4999: Number(formData.price4000_4999),
    price5000_5999: Number(formData.price5000_5999),
    price6000_6999: Number(formData.price6000_6999),
    price7000_7999: Number(formData.price7000_7999),
    price8000_8999: Number(formData.price8000_8999),
    price9000_9999: Number(formData.price9000_9999),
    price10000Plus: Number(formData.price10000Plus),
    unit: formData.unit,
    ctpPlateFee: Number(formData.ctpPlateFee),
    spotColorFee: Number(formData.spotColorFee),
    remark: formData.remark
  };
};

export const transformPrintingMachineFormData = (formData: z.infer<typeof printingMachineFormSchema>) => {
  return {
    machineName: formData.machineName,
    maxLength: Number(formData.maxLength),
    maxWidth: Number(formData.maxWidth),
    remark: formData.remark
  };
}; 

export type CreatePrintingData = z.infer<typeof createPrintingSchema>;
export type UpdatePrintingData = z.infer<typeof updatePrintingSchema>;

export type CreatePrintingMachineData = z.infer<typeof createPrintingMachineSchema>;
export type UpdatePrintingMachineData = z.infer<typeof updatePrintingMachineSchema>;

export type PrintingListParams = z.infer<typeof printingListParamsSchema>;
export type PrintingMachineListParams = z.infer<typeof printingMachineListParamsSchema>;

export type PriceCalculationParams = z.infer<typeof priceCalculationParamsSchema>;
