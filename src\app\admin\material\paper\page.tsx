'use client';

import React, { useState, useEffect } from 'react';
import {
  Table, Button, Space, Modal, Form, Input,
  Select, InputNumber, Popconfirm, Card, Typography,
  message, Tabs, Row, Col,
  Tag, Divider
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined,
  SearchOutlined, ReloadOutlined} from '@ant-design/icons';
import { paperApi, paperCuttingApi } from '@/services/adminApi';
import { Paper, PaperCutting } from '@/types/material';
import { useAsyncError } from '@/lib/hooks/useErrorHandler';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;

// 纸类数据库管理页面
export default function PaperManagementPage() {
  // 错误处理Hook
  const { execute: executePaper, loading: paperLoading, errorState: paperErrorState } = useAsyncError();
  const { execute: executeCutting, loading: cuttingLoading, errorState: cuttingErrorState } = useAsyncError();

  // 纸张数据相关状态
  const [paperList, setPaperList] = useState<Paper[]>([]);
  const [paperTotal, setPaperTotal] = useState(0);
  const [paperCurrent, setPaperCurrent] = useState(1);
  const [paperPageSize, setPaperPageSize] = useState(10);
  const [paperKeyword, setPaperKeyword] = useState('');
  const [paperCategory, setPaperCategory] = useState('');
  const [categoryList, setCategoryList] = useState<string[]>([]);
  const [categoryLoading, setCategoryLoading] = useState(false);

  // 分切尺寸相关状态
  const [cuttingList, setCuttingList] = useState<PaperCutting[]>([]);
  const [cuttingTotal, setCuttingTotal] = useState(0);
  const [cuttingCurrent, setCuttingCurrent] = useState(1);
  const [cuttingPageSize, setCuttingPageSize] = useState(10);

  // 模态框相关状态
  const [paperModalVisible, setPaperModalVisible] = useState(false);
  const [cuttingModalVisible, setCuttingModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [paperForm] = Form.useForm();
  const [cuttingForm] = Form.useForm();
  const [customSizes, setCustomSizes] = useState<number[]>([]);

  // 初始加载数据
  useEffect(() => {
    fetchPaperList();
    fetchCuttingList();
    fetchCategoryList();
  }, []);

  // 获取纸张列表
  const fetchPaperList = async (page = paperCurrent, pageSize = paperPageSize, keyword = paperKeyword, category = paperCategory) => {
    // 构建请求参数，只包含有效值
    const requestParams: any = {
      page,
      pageSize,
    };
    
    if (keyword) {
      requestParams.keyword = keyword;
    }
    
    if (category) {
      requestParams.category = category;
    }
    
    const result = await executePaper(async () => {
      return await paperApi.getList(requestParams);
    }, '获取纸张列表');

    if (result) {
      setPaperList(result.list || []);
      setPaperTotal(result.pagination.total || 0);
    } else {
      setPaperList([]);
      setPaperTotal(0);
    }
  };

  // 获取材料品类列表
  const fetchCategoryList = async () => {
    setCategoryLoading(true);
    
    const result = await executePaper(async () => {
      return await paperApi.getCategoryList();
    }, '获取材料品类列表');

    if (result) {
      setCategoryList(result);
    } else {
      setCategoryList([]);
    }
    
    setCategoryLoading(false);
  };

  // 获取分切尺寸列表
  const fetchCuttingList = async (page = cuttingCurrent, pageSize = cuttingPageSize) => {
    // 构建请求参数，只包含有效值
    const requestParams: any = {
      page,
      pageSize
    };
    
    const result = await executeCutting(async () => {
      return await paperCuttingApi.getList(requestParams);
    }, '获取分切尺寸列表');

    if (result) {
      setCuttingList(result.list || []);
      setCuttingTotal(result.pagination.total || 0);
    } else {
      setCuttingList([]);
      setCuttingTotal(0);
    }
  };

  // 处理纸张分页变化
  const handlePaperTableChange = (pagination: any) => {
    setPaperCurrent(pagination.current);
    setPaperPageSize(pagination.pageSize);
    fetchPaperList(pagination.current, pagination.pageSize);
  };

  // 处理分切尺寸分页变化
  const handleCuttingTableChange = (pagination: any) => {
    setCuttingCurrent(pagination.current);
    setCuttingPageSize(pagination.pageSize);
    fetchCuttingList(pagination.current, pagination.pageSize);
  };

  // 打开添加纸张模态框
  const showAddPaperModal = () => {
    setModalTitle('添加纸张');
    setEditingRecord(null);
    paperForm.resetFields();
    setPaperModalVisible(true);
  };

  // 打开编辑纸张模态框
  const showEditPaperModal = (record: Paper) => {
    setModalTitle('编辑纸张');
    setEditingRecord(record);
    paperForm.setFieldsValue({
      name: record.name,
      price: record.price,
      unit: record.unit,
      weight: record.weight,
      thickness: record.thickness,
      regularPrice: record.regularPrice,
      largePrice: record.largePrice,
      category: record.category,
      remark: record.remark
    });
    setPaperModalVisible(true);
  };

  // 打开添加分切尺寸模态框
  const showAddCuttingModal = () => {
    setModalTitle('添加分切尺寸');
    setEditingRecord(null);
    setCustomSizes([]);
    cuttingForm.resetFields();
    setCuttingModalVisible(true);
  };

  // 打开编辑分切尺寸模态框
  const showEditCuttingModal = (record: PaperCutting) => {
    setModalTitle('编辑分切尺寸');
    setEditingRecord(record);
    
    // 确保 sizes 是数组
    let sizes: number[] = [];
    try {
      sizes = Array.isArray(record.sizes) 
        ? record.sizes 
        : JSON.parse(String(record.sizes));
    } catch (e) {
      console.error('解析分切尺寸失败:', e);
    }
    
    // 为每个尺寸生成唯一ID
    const sizesWithIds: number[] = sizes.map(size => Number(size));
    
    setCustomSizes(sizesWithIds);
    cuttingForm.setFieldsValue({
      name: record.name,
      initialCutPrice: record.initialCutPrice
    });
    setCuttingModalVisible(true);
  };

  // 处理纸张表单提交
  const handlePaperFormSubmit = async () => {
    try {
      const values = await paperForm.validateFields();

      // 处理品类数据类型问题 - mode="tags"会返回数组，需要转换为字符串
      if (Array.isArray(values.category) && values.category.length > 0) {
        values.category = values.category[0];
      }

      if (editingRecord) {
        // 更新纸张
        const result = await executePaper(async () => {
          return await paperApi.update({
            id: editingRecord.id,
            ...values
          });
        }, '更新纸张');

        if (result) {
          setPaperModalVisible(false);
          fetchPaperList();
          fetchCategoryList(); // 重新获取品类列表，确保新添加的品类能被列出
        }
      } else {
        // 创建纸张
        const result = await executePaper(async () => {
          return await paperApi.create(values);
        }, '创建纸张');

        if (result) {
          setPaperModalVisible(false);
          fetchPaperList();
          fetchCategoryList(); // 重新获取品类列表，确保新添加的品类能被列出
        }
      }
    } catch (error) {
      console.error('表单提交失败:', error);
      message.error('表单验证失败');
    }
  };

  // 处理分切尺寸表单提交
  const handleCuttingFormSubmit = async () => {
    try {
      const values = await cuttingForm.validateFields();
      
      // 确保至少有一个尺寸
      if (customSizes.length === 0) {
        message.error('请至少添加一个分切尺寸');
        return;
      }

      const formData = {
        ...values,
        sizes: customSizes
      };

      if (editingRecord) {
        // 更新分切尺寸
        const result = await executeCutting(async () => {
          return await paperCuttingApi.update({
            id: editingRecord.id,
            ...formData
          });
        }, '更新分切尺寸');
        if (result) {
          setCuttingModalVisible(false);
          fetchCuttingList();
        }
      } else {
        // 创建分切尺寸
        const result = await executeCutting(async () => {
          return await paperCuttingApi.create(formData);
        }, '创建分切尺寸');
        if (result) {
          setCuttingModalVisible(false);
          fetchCuttingList();
        }
      }
    } catch (error) {
      message.error('表单验证失败');
    }
  };

  // 删除纸张
  const handleDeletePaper = async (id: number) => {
    const result = await executePaper(async () => {
      return await paperApi.delete(id);
    }, '删除纸张');
    if (result !== null) {
      fetchPaperList();
    }
  };

  // 删除分切尺寸
  const handleDeleteCutting = async (id: number) => {
    const result = await executeCutting(async () => {
      return await paperCuttingApi.delete(id);
    }, '删除分切尺寸');
    if (result !== null) {
      fetchCuttingList();
    }
  };

  // 修改添加分切尺寸的函数
  const handleAddSize = () => {
    const newSize = cuttingForm.getFieldValue('newSize');
    if (!newSize || newSize <= 0) {
      message.error('请输入有效的分切尺寸');
      return;
    }
    
    setCustomSizes(prev => [...prev, Number(newSize)]);
    
    cuttingForm.setFieldValue('newSize', null);
  };

  // 移除分切尺寸
  const handleRemoveSize = (size: number) => {
    setCustomSizes(prev => prev.filter(item => item !== size));
  };

  // 纸张表格列定义
  const paperColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center' as const,
      width: 150,
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      align: 'center' as const,
      width: 100,
      render: (text: number) => `${text} 元`,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      align: 'center' as const,
      width: 100,
    },
    {
      title: '克重',
      dataIndex: 'weight',
      key: 'weight',
      align: 'center' as const,
      width: 80,
    },
    {
      title: '厚度',
      dataIndex: 'thickness',
      key: 'thickness',
      align: 'center' as const,
      width: 80,
    },
    {
      title: '正度价格',
      dataIndex: 'regularPrice',
      key: 'regularPrice',
      align: 'center' as const,
      width: 120,
      render: (text: number) => text ? `${text} 元` : '-',
    },
    {
      title: '大度价格',
      dataIndex: 'largePrice',
      key: 'largePrice',
      align: 'center' as const,
      width: 120,
      render: (text: number) => text ? `${text} 元` : '-',
    },
    {
      title: '材料品类',
      dataIndex: 'category',
      key: 'category',
      align: 'center' as const,
      width: 120,
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      align: 'center' as const,
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'action',
      align: 'center' as const,
      fixed: 'right' as const,
      width: 150,
      render: (_: any, record: Paper) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => showEditPaperModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此纸张吗？"
            onConfirm={() => handleDeletePaper(record.id)}
            okText="是"
            cancelText="否"
          >
            <Button
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 分切尺寸表格列定义
  const cuttingColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center' as const,
      width: 150,
    },
    {
      title: '分切起步金额',
      dataIndex: 'initialCutPrice',
      key: 'initialCutPrice',
      align: 'center' as const,
      width: 150,
      render: (text: number) => `${text} 元`,
    },
    {
      title: '分切尺寸',
      dataIndex: 'sizes',
      key: 'sizes',
      align: 'center' as const,
      width: 400,
      render: (sizes: number[]) => (
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '5px', justifyContent: 'center' }}>
          {sizes.map((size, index) => (
            <Tag key={index} color="blue" style={{ marginRight: 4, height: 'auto', lineHeight: '1.5' }}>{size}</Tag>
          ))}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      align: 'center' as const,
      fixed: 'right' as const,
      width: 150,
      render: (_: any, record: PaperCutting) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => showEditCuttingModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此分切尺寸吗？"
            onConfirm={() => handleDeleteCutting(record.id)}
            okText="是"
            cancelText="否"
          >
            <Button
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>纸类数据库</Title>

      <Tabs
        defaultActiveKey="1"
        items={[
          {
            key: '1',
            label: '纸张数据库',
            children: (
              <Card>
                <div style={{ marginBottom: 16 }}>
                  <Row gutter={16} align="middle">
                    <Col span={4}>
                      <Input
                        placeholder="搜索名称"
                        prefix={<SearchOutlined />}
                        allowClear
                        value={paperKeyword}
                        onChange={(e) => setPaperKeyword(e.target.value)}
                        onPressEnter={() => fetchPaperList(1, paperPageSize, paperKeyword, paperCategory)}
                      />
                    </Col>
                    <Col span={4}>
                      <Select
                        placeholder="搜索材料品类"
                        allowClear
                        showSearch
                        loading={paperLoading}
                        style={{ width: '100%' }}
                        value={paperCategory || undefined}
                        onChange={(value) => setPaperCategory(value || '')}
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          (option?.children as unknown as string).toLowerCase().includes(input.toLowerCase())
                        }
                      >
                        {categoryList.map((cat) => (
                          <Option key={cat} value={cat}>
                            {cat}
                          </Option>
                        ))}
                      </Select>
                    </Col>
                    <Col>
                      <Button
                        type="primary"
                        icon={<SearchOutlined />}
                        onClick={() => fetchPaperList(1, paperPageSize, paperKeyword, paperCategory)}
                      >
                        搜索
                      </Button>
                    </Col>
                    <Col>
                      <Button
                        icon={<ReloadOutlined />}
                        onClick={() => {
                          setPaperKeyword('');
                          setPaperCategory('');
                          fetchPaperList(1, paperPageSize, '', '');
                        }}
                      >
                        重置
                      </Button>
                    </Col>
                    <Col flex="auto" style={{ textAlign: 'right' }}>
                      <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={showAddPaperModal}
                      >
                        添加纸张
                      </Button>
                    </Col>
                  </Row>
                </div>

                <Table
                  columns={paperColumns}
                  dataSource={paperList}
                  rowKey="id"
                  pagination={{
                    current: paperCurrent,
                    pageSize: paperPageSize,
                    total: paperTotal,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                  }}
                  loading={paperLoading}
                  onChange={handlePaperTableChange}
                  bordered
                  size="middle"
                  scroll={{ x: 1200 }}
                  locale={{ emptyText: '暂无数据' }}
                />
              </Card>
            )
          },
          // {
          //   key: '2',
          //   label: '卷筒材料分切尺寸',
          //   children: (
          //     <Card>
          //       <div style={{ marginBottom: 16, textAlign: 'right' }}>
          //         <Button
          //           type="primary"
          //           icon={<PlusOutlined />}
          //           onClick={showAddCuttingModal}
          //         >
          //           添加分切尺寸
          //         </Button>
          //       </div>

          //       <Table
          //         columns={cuttingColumns}
          //         dataSource={cuttingList}
          //         rowKey="id"
          //         pagination={{
          //           current: cuttingCurrent,
          //           pageSize: cuttingPageSize,
          //           total: cuttingTotal,
          //           showSizeChanger: true,
          //           showQuickJumper: true,
          //           showTotal: (total) => `共 ${total} 条`,
          //         }}
          //         loading={cuttingLoading}
          //         onChange={handleCuttingTableChange}
          //         bordered
          //         size="middle"
          //         scroll={{ x: 800 }}
          //         locale={{ emptyText: '暂无数据' }}
          //       />
          //     </Card>
          //   )
          // }
        ]}
      />

      {/* 纸张表单模态框 */}
      <Modal
        title={modalTitle}
        open={paperModalVisible}
        onOk={handlePaperFormSubmit}
        onCancel={() => setPaperModalVisible(false)}
        width={700}
      >
        <Form
          form={paperForm}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="纸张名称"
                rules={[{ required: true, message: '请输入纸张名称' }]}
              >
                <Input placeholder="请输入纸张名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="category"
                label="材料品类"
                rules={[{ required: true, message: '请选择或输入品类' }]}
              >
                <Select
                  placeholder="请选择品类"
                  loading={categoryLoading}
                  showSearch
                  allowClear
                  mode="tags"
                  onChange={(value) => {
                    // 如果是数组并且有多个值，只保留最后一个
                    if (Array.isArray(value) && value.length > 1) {
                      const lastValue = value[value.length - 1];
                      paperForm.setFieldValue('category', [lastValue]);
                    }
                  }}
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                  }
                  dropdownRender={(menu) => (
                    <>
                      {menu}
                      {categoryList.length === 0 && !categoryLoading && (
                        <div style={{ padding: '8px', textAlign: 'center' }}>
                          暂无数据，请输入新品类
                        </div>
                      )}
                    </>
                  )}
                >
                  {categoryList.map((cat) => (
                    <Option key={cat} value={cat}>{cat}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="price"
                label="价格"
                rules={[{ required: true, message: '请输入价格' }]}
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="请输入价格"
                  addonAfter="元"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="unit"
                label="单位"
                rules={[{ required: true, message: '请选择单位' }]}
              >
                <Select placeholder="请选择单位">
                  <Option value="元/吨">元/吨</Option>
                  <Option value="元/张">元/张</Option>
                  <Option value="元/平方">元/平方</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="weight"
                label="克重"
                rules={[{ required: true, message: '请输入克重' }]}
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                  placeholder="请输入克重"
                  addonAfter="g/m²"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="thickness"
                label="厚度"
                rules={[{ required: true, message: '请输入厚度' }]}
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                  placeholder="请输入厚度"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="regularPrice"
                label="正度价格"
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="正度价格（可选）"
                  addonAfter="元"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="largePrice"
                label="大度价格"
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="大度价格（可选）"
                  addonAfter="元"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="remark"
            label="备注"
          >
            <Input.TextArea rows={3} placeholder="请输入备注信息（可选）" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 分切尺寸表单模态框 */}
      <Modal
        title={modalTitle}
        open={cuttingModalVisible}
        onOk={handleCuttingFormSubmit}
        onCancel={() => setCuttingModalVisible(false)}
        width={600}
      >
        <Form
          form={cuttingForm}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="名称"
                rules={[{ required: true, message: '请输入名称' }]}
              >
                <Input placeholder="请输入名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="initialCutPrice"
                label="分切起步金额"
                rules={[{ required: true, message: '请输入分切起步金额' }]}
              >
                <InputNumber 
                  min={0} 
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="请输入分切起步金额"
                  addonAfter="元"
                />
              </Form.Item>
            </Col>
          </Row>

          <Divider orientation="left">分切尺寸</Divider>

          <div style={{ marginBottom: 24 }}>
            <Row gutter={16}>
              <Col span={16}>
                <Form.Item
                  name="newSize"
                  label="添加尺寸"
                  noStyle
                  tooltip="请输入大于0的数值，单位为毫米"
                >
                  <InputNumber 
                    placeholder="请输入分切尺寸（mm）" 
                    min={0} 
                    style={{ width: '100%' }}
                    precision={2}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Button 
                  type="primary" 
                  style={{ width: '100%' }}
                  onClick={handleAddSize}
                >
                  添加
                </Button>
              </Col>
            </Row>
          </div>
          
          <div style={{ marginBottom: 16, minHeight: 120, padding: '16px', border: '1px dashed #d9d9d9', borderRadius: '8px', backgroundColor: '#fafafa' }}>
            {customSizes.length > 0 ? (
              <div style={{ 
                display: 'flex', 
                flexWrap: 'wrap', 
                gap: '8px'
              }}>
                {customSizes.map((size) => (
                  <Tag
                    key={size}
                    color="blue"
                    style={{ 
                      padding: '4px 8px', 
                      fontSize: '14px',
                      margin: 0
                    }}
                    closable
                    onClose={(e) => {
                      e.preventDefault();
                      handleRemoveSize(size);
                    }}
                  >
                    {size}
                  </Tag>
                ))}
              </div>
            ) : (
              <div style={{ textAlign: 'center', color: '#999', paddingTop: '32px' }}>
                暂无分切尺寸，请添加
              </div>
            )}
          </div>
        </Form>
      </Modal>
    </div>
  );
} 