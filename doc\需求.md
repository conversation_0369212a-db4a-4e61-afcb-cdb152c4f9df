# 新算价系统

## 用户模块

1. **多维度身份认证**
   - OAuth2.0 联合登录（手机验证）
   - 密码策略管理（复杂度要求）
   - 二步验证机制（短信验证）

2. **分级权限控制**
   - 角色层级：系统管理员 >  报价专员 > 普通客户
   - 功能权限矩阵：

     | 角色        | 数据查看范围       | 操作权限               |
     |-------------|--------------------|------------------------|
     | 系统管理员  | 全量数据           | 系统配置               |
     | 报价专员    | 报价数据、利润数据 | 报价单生成、历史查询   |
     | 普通客户    | 自有报价记录       | 自助报价               |

3. **数据隔离**
   - 自定义盒型可见范围
   - [TODO] 会员功能

## 后台管理

### 1. 盒型参数建模系统

- 参数化盒型库
  - 支持自定义多结构 (上盖、底盒)
  - 结构自定义计算属性（长、宽、高、糊口、板厚、包边...）
    - 支持默认值
  - 自定义使用材料
  - 自定义面积计算公式（展开长度、展开宽度...）
    - 例:
      - 面纸展开长度 = (2*长)+(2*宽)+4*板厚+包边
      - 面纸展开宽度 = 长+高+2*板厚+包边
      - 面纸面积 = 面纸展开长度 * 面纸展开宽度
  - 自定义材料可选使用材料工艺
    - 工艺（烫金、UV..）
  - 其中的关联关系为
    - 盒型 -> 多个属性
    - 盒型 -> 多个部位
    - 部位 -> 多个公式
    - 公式 -> 引用多个属性
- 自定义计算公式(材料、工艺等具体计算)
  - 名称、计算属性、计算方式
- 其他扩展
  - 待商讨

### 2. 材料工艺计算矩阵

- 基材库（纸张/塑料/金属）
- 表面处理工艺（烫金/UV/压纹）
- 复合工艺冲突检测
- 自选配件
- 自定义价格计算公式

### 3. 报价计算

1. 基础参数输入（尺寸/数量/盒型）
2. 材料工艺组合配置
3. 自定义公式自主添加
4. 数量梯度利润率计算

## 前台（Web 端）

- 首页
- 包装报价（分类）
- 报价计算
- 通用组件

## 其他扩展功能

- 打印功能（
- 报价分析（

## 程序使用组件

- OSS
- Docker
- MySql
- Redis
- NextJS

## 参数

- 盒型
  - 分体（上盖、下底）
    - 属性（可定义是否必选）
      - 尺寸
      - 数量
      - 材料
      - 印刷
      - 覆膜
      - 印色
      - 工艺
        - 烫金
        - UV
        - 击凸
        - 深压
        - 压纹
        - ...
      - 加工
        - 模切
        - 刀版
        - ...
      - 配件
        - ...

## 使用技术

前端使用 antd 组件
 