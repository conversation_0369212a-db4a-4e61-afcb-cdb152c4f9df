import { ErrorCode } from "@/lib/constants/errorCodes";

// API 响应类型
export interface ApiResponse<T = any> {
  code: ErrorCode;
  message: string;
  data: T;
  timestamp?: string;
}

// 错误响应类型
export interface ErrorResponse {
  code: ErrorCode;
  message: string;
  errors?: any;
  timestamp?: string;
}

// 分页数据类型 - 修正字段名称
export interface PaginatedData<T> {
  list: T[];
  pagination: {
    page: number;        // 修正：从 current 改为 page，与后端保持一致
    pageSize: number;
    total: number;
  };
}

// 前端错误处理结果类型
export interface Result<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: number;
    message: string;
    details?: any;
  };
}

// 创建成功结果
export function createSuccess<T>(data: T): Result<T> {
  return {
    success: true,
    data,
  };
}

// 创建失败结果
export function createError(
  code: number,
  message: string,
  details?: any
): Result<never> {
  return {
    success: false,
    error: {
      code,
      message,
      details,
    },
  };
}

// 通用分页参数类型
export interface PaginationParams {
  page?: number;
  pageSize?: number;
}

// 通用搜索参数类型
export interface SearchParams extends PaginationParams {
  keyword?: string;
  startTime?: string;
  endTime?: string;
}

// 操作状态枚举
export enum Status {
  DRAFT = 0,      // 草稿
  PUBLISHED = 1,  // 已发布
  DISABLED = 2,   // 已禁用
}

// HTTP方法枚举
export enum HttpMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH',
}

// 加载状态类型
export interface LoadingState {
  loading: boolean;
  error: string | null;
} 