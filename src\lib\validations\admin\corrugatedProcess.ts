import { z } from 'zod';
import { FLUTE_TYPES, CORRUGATED_PROCESS_UNITS } from '@/types/craftSalary';

// 瓦楞工艺表单验证模式
export const corrugatedProcessFormSchema = z.object({
  code: z.string()
    .max(50, '代号长度不能超过50字符')
    .optional(),
  
  materialName: z.string()
    .min(1, '请输入材质名称')
    .max(100, '材质名称长度不能超过100字符'),
  
  price: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '价格必须是大于等于0的数字'),
  
  unit: z.enum(CORRUGATED_PROCESS_UNITS, {
    required_error: '请选择计价单位',
    invalid_type_error: '无效的计价单位',
  }),
  
  setupFee: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '上机费必须是大于等于0的数字'),
  
  thickness: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '厚度必须是大于0等于的数字'),
  
  coreWeight1: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '芯纸1必须是大于等于0的数字'),
  
  fluteType1: z.enum(FLUTE_TYPES, {
    invalid_type_error: '无效的楞形',
  }).optional(),
  
  linerWeight1: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '里纸1必须是大于等于0的数字'),
  
  coreWeight2: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '芯纸2必须是大于等于0的数字'),
  
  fluteType2: z.enum(FLUTE_TYPES, {
    invalid_type_error: '无效的楞形',
  }).optional(),
  
  linerWeight2: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0;
    }, '里纸2必须是大于等于0的数字'),
  
  remark: z.string()
    .max(500, '备注长度不能超过500字符')
    .optional()
    .or(z.literal(''))
});

// 瓦楞工艺创建请求验证模式
export const createCorrugatedProcessSchema = z.object({
  code: z.string().max(50).optional(),
  materialName: z.string().min(1).max(100),
  price: z.number().min(0),
  unit: z.enum(CORRUGATED_PROCESS_UNITS),
  setupFee: z.number().min(0),
  thickness: z.number().min(0),
  coreWeight1: z.number().min(0),
  fluteType1: z.enum(FLUTE_TYPES).optional(),
  linerWeight1: z.number().min(0),
  coreWeight2: z.number().min(0),
  fluteType2: z.enum(FLUTE_TYPES).optional(),
  linerWeight2: z.number().min(0),
  remark: z.string().max(500).optional()
});

// 瓦楞工艺更新请求验证模式
export const updateCorrugatedProcessSchema = createCorrugatedProcessSchema.extend({
  id: z.number().int().positive()
});

// 瓦楞工艺列表查询参数验证模式
export const corrugatedProcessListParamsSchema = z.object({
  page: z.number().int().positive().optional(),
  pageSize: z.number().int().positive().max(100).optional(),
  search: z.string().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
});

// 瓦楞工艺删除请求验证模式
export const deleteCorrugatedProcessSchema = z.object({
  id: z.number().int().positive(),
});

// 瓦楞工艺详情请求验证模式
export const getCorrugatedProcessDetailSchema = z.object({
  id: z.number().int().positive(),
});
// 瓦楞率配置表单验证模式
export const corrugatedRateFormSchema = z.object({
  fluteType: z.enum(FLUTE_TYPES, {
    required_error: '请选择楞形',
    invalid_type_error: '无效的楞形',
  }),
  
  rate: z.string()
    .refine((val) => {
      const num = Number(val);
      return !isNaN(num) && num > 0;
    }, '瓦楞率必须是大于0的数字'),
});

// 瓦楞率配置创建请求验证模式
export const createCorrugatedRateSchema = z.object({
  fluteType: z.enum(FLUTE_TYPES),
  rate: z.number().positive()
});

// 瓦楞率配置更新请求验证模式
export const updateCorrugatedRateSchema = createCorrugatedRateSchema.extend({
  id: z.number().int().positive()
});

// 瓦楞率配置列表查询参数验证模式
export const corrugatedRateListParamsSchema = z.object({
  page: z.number().int().positive().optional(),
  pageSize: z.number().int().positive().max(100).optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
});

// 表单数据转换函数
export function transformCorrugatedProcessFormData(formData: z.infer<typeof corrugatedProcessFormSchema>) {
  return {
    code: formData.code || undefined,
    materialName: formData.materialName,
    price: Number(formData.price),
    unit: formData.unit,
    setupFee: Number(formData.setupFee),
    thickness: Number(formData.thickness),
    coreWeight1: Number(formData.coreWeight1),
    fluteType1: formData.fluteType1 || undefined,
    linerWeight1: Number(formData.linerWeight1),
    coreWeight2: Number(formData.coreWeight2),
    fluteType2: formData.fluteType2 || undefined,
    linerWeight2: Number(formData.linerWeight2),
    remark: formData.remark || undefined
  };
}

export function transformCorrugatedRateFormData(formData: z.infer<typeof corrugatedRateFormSchema>) {
  return {
    fluteType: formData.fluteType,
    rate: Number(formData.rate)
  };
}

export type CorrugatedProcessFormData = z.infer<typeof corrugatedProcessFormSchema>;
export type CreateCorrugatedProcessData = z.infer<typeof createCorrugatedProcessSchema>;
export type UpdateCorrugatedProcessData = z.infer<typeof updateCorrugatedProcessSchema>;
export type DeleteCorrugatedProcessData = z.infer<typeof deleteCorrugatedProcessSchema>;
export type GetCorrugatedProcessDetailData = z.infer<typeof getCorrugatedProcessDetailSchema>;
export type CorrugatedRateFormData = z.infer<typeof corrugatedRateFormSchema>;
export type CreateCorrugatedRateData = z.infer<typeof createCorrugatedRateSchema>;
export type UpdateCorrugatedRateData = z.infer<typeof updateCorrugatedRateSchema>; 
export type CorrugatedProcessListParams = z.infer<typeof corrugatedProcessListParamsSchema>;

