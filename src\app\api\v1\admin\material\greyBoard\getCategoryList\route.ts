import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withValidation } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';

export const POST = withValidation(
  null,
  async (request: NextRequest) => {
    // 查询所有非删除的灰板纸记录的category字段
    const categories = await prisma.greyBoard.findMany({
      where: {
        isDel: false,
      },
      select: {
        category: true,
      },
      distinct: ['category'],
    });

    // 过滤掉空值和空字符串
    const filteredCategories = categories
      .map(item => item.category)
      .filter(category => category && category.trim() !== '')
      .sort(); // 按字母顺序排序

    return successResponse(filteredCategories, '获取灰板纸品类列表成功');
  }
); 