import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { deleteGreyBoardCuttingSchema, DeleteGreyBoardCuttingParams } from '@/lib/validations/admin/greyBoardCutting';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation<DeleteGreyBoardCuttingParams>(
  deleteGreyBoardCuttingSchema,
  async (request: NextRequest, validatedQuery: DeleteGreyBoardCuttingParams) => {
    // 检查记录是否存在
    const existingRecord = await prisma.greyBoardCutting.findFirst({
      where: {
        id: validatedQuery.id,
        isDel: false,
      },
    });
    assertExists(existingRecord, ErrorCode.MATERIAL_NOT_FOUND, '分切尺寸不存在');

    // 软删除
    await prisma.greyBoardCutting.update({
      where: { id: validatedQuery.id },
      data: { isDel: true },
    });

    return successResponse({ id: validatedQuery.id }, '删除分切尺寸成功');
  }
); 