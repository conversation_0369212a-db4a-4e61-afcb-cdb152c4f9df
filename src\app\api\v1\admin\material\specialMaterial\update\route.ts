import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { updateSpecialMaterialSchema, validateStockSize, UpdateSpecialMaterialParams } from '@/lib/validations/admin/specialMaterial';
import { withValidation, assertExists, assert } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation<UpdateSpecialMaterialParams>(
  updateSpecialMaterialSchema,
  async (request: AuthenticatedRequest, validatedData: UpdateSpecialMaterialParams) => {
    // 验证现货尺寸条件
    const stockSizeValidation = validateStockSize(validatedData);
    assert(stockSizeValidation.success, ErrorCode.INVALID_PARAMETERS, stockSizeValidation.error || '现货尺寸验证失败');

    // 检查特殊材料是否存在
    const existingMaterial = await prisma.specialMaterial.findUnique({
      where: {
        id: validatedData.id,
        isDel: false,
      },
    });

    assertExists(existingMaterial, ErrorCode.MATERIAL_NOT_FOUND, '特殊材料不存在');

    // 检查名称是否与其他特殊材料重复
    const duplicateName = await prisma.specialMaterial.findFirst({
      where: {
        name: validatedData.name,
        id: { not: validatedData.id },
        isDel: false,
      },
    });

    assert(!duplicateName, ErrorCode.MATERIAL_NAME_EXISTS, '特殊材料名称已存在');

    // 更新特殊材料
    const specialMaterial = await prisma.specialMaterial.update({
      where: {
        id: validatedData.id,
      },
      data: {
        name: validatedData.name,
        price: validatedData.price,
        unit: validatedData.unit,
        thickness: validatedData.thickness,
        density: validatedData.density,
        isStockSize: validatedData.isStockSize,
        stockLength: validatedData.isStockSize ? validatedData.stockLength : null,
        stockWidth: validatedData.isStockSize ? validatedData.stockWidth : null,
        category: validatedData.category,
        remark: validatedData.remark || null,
        updatedAt: new Date(),
      },
    });

    return successResponse(specialMaterial, '更新特殊材料成功');
  }
); 
export const POST = withInternalAuth(handler);