import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getSpecialMaterialDetailSchema, GetSpecialMaterialDetailParams } from '@/lib/validations/admin/specialMaterial';
import { withValidation, assertExists } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation<GetSpecialMaterialDetailParams>(
  getSpecialMaterialDetailSchema,
  async (request: AuthenticatedRequest, validatedQuery: GetSpecialMaterialDetailParams) => {
    const { id } = validatedQuery;

    // 查询特殊材料详情
    const specialMaterial = await prisma.specialMaterial.findUnique({
      where: {
        id,
        isDel: false,
      },
    });

    // 检查特殊材料是否存在
    assertExists(specialMaterial, ErrorCode.MATERIAL_NOT_FOUND, '特殊材料不存在');

    return successResponse(specialMaterial, '获取特殊材料详情成功');
  }
); 
export const POST = withInternalAuth(handler);