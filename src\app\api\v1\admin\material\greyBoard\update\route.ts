import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { updateGreyBoardSchema, validateStockSize, UpdateGreyBoardParams } from '@/lib/validations/admin/greyBoard';
import { withValidation, assert, assertExists } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation<UpdateGreyBoardParams>(
  updateGreyBoardSchema,
  async (request: NextRequest, validatedData: UpdateGreyBoardParams) => {
    // 现货尺寸条件验证
    const stockSizeValidation = validateStockSize(validatedData);
    assert(
      stockSizeValidation.success,
      ErrorCode.INVALID_PARAMETERS,
      stockSizeValidation.error || '现货尺寸验证失败'
    );

    // 检查记录是否存在
    const existingMaterial = await prisma.greyBoard.findUnique({
      where: { id: validatedData.id, isDel: false },
    });
    assertExists(existingMaterial, ErrorCode.MATERIAL_NOT_FOUND, '灰板纸不存在');

    // 检查名称重复（排除自己）
    const duplicateName = await prisma.greyBoard.findFirst({
      where: {
        name: validatedData.name,
        id: { not: validatedData.id },
        isDel: false,
      },
    });
    assert(!duplicateName, ErrorCode.MATERIAL_NAME_EXISTS, '灰板纸名称已存在');

    // 更新灰板纸
    const greyBoard = await prisma.greyBoard.update({
      where: { id: validatedData.id },
      data: {
        name: validatedData.name,
        price: validatedData.price,
        unit: validatedData.unit,
        weight: validatedData.weight,
        thickness: validatedData.thickness,
        isRegular: validatedData.isRegular,
        isLarge: validatedData.isLarge,
        isStockSize: validatedData.isStockSize,
        stockLength: validatedData.isStockSize ? validatedData.stockLength : null,
        stockWidth: validatedData.isStockSize ? validatedData.stockWidth : null,
        category: validatedData.category,
        remark: validatedData.remark || null,
        updatedAt: new Date(),
      },
    });

    return successResponse(greyBoard, '更新灰板纸成功');
  }
); 