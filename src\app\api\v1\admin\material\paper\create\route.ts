import { NextRequest } from 'next/server';
import { createPaperSchema, CreatePaperParams } from '@/lib/validations/admin/paper';
import { prisma } from '@/lib/prisma';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

const handler = withValidation<CreatePaperParams>(
  createPaperSchema,
  async (request: AuthenticatedRequest, validatedData: CreatePaperParams) => {
    // 检查纸张名称是否已存在
    const existingPaper = await prisma.paper.findFirst({
      where: { 
        name: validatedData.name,
        isDel: false 
      }
    });
    
    assert(
      !existingPaper,
      ErrorCode.DUPLICATE_ENTRY,
      '纸张材料名称已存在'
    );

    // 创建纸张材料
    const paper = await prisma.paper.create({
      data: {
        name: validatedData.name,
        price: validatedData.price,
        unit: validatedData.unit,
        weight: validatedData.weight,
        thickness: validatedData.thickness,
        regularPrice: validatedData.regularPrice || null,
        largePrice: validatedData.largePrice || null,
        category: validatedData.category,
        remark: validatedData.remark || null,
        isDel: false,
      },
    });

    return successResponse(
      paper,
      '创建纸张材料成功'
    );
  }
); 
export const POST = withInternalAuth(handler);