import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getGreyBoardListSchema, GetGreyBoardListParams } from '@/lib/validations/admin/greyBoard';
import { withValidation } from '@/lib/middleware/errorHandler';
import { paginatedResponse } from '@/lib/utils/apiResponse';
import { Prisma } from '@prisma/client';

export const POST = withValidation<GetGreyBoardListParams>(
  getGreyBoardListSchema,
  async (request: NextRequest, validatedQuery: GetGreyBoardListParams) => {
    const { page = 1, pageSize = 10, search } = validatedQuery;
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const where: Prisma.GreyBoardWhereInput = {
      isDel: false,
    };

    if (search) {
      where.OR = [
        { name: { contains: search } },
        { unit: { contains: search } },
        { category: { contains: search } },
      ];
    }

    // 查询总数和列表数据
    const [total, list] = await Promise.all([
      prisma.greyBoard.count({ where }),
      prisma.greyBoard.findMany({
        where,
        select: {
          id: true,
          name: true,
          category: true,
          thickness: true,
          weight: true,
          unit: true,
          price: true,
          isRegular: true,
          isLarge: true,
          isStockSize: true,
          stockLength: true,
          stockWidth: true,
          remark: true,
          createdAt: true,
          updatedAt: true,
        },
        skip,
        take: pageSize,
        orderBy: { id: 'desc' },
      })
    ]);

    return paginatedResponse(
      list,
      {
        page,
        pageSize,
        total,
      },
      '获取灰板纸列表成功'
    );
  }
); 