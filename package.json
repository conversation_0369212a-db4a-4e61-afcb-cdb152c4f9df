{"name": "ycbz", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "npx tsc --noEmit", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "db:seed": "prisma db seed", "db:reset": "prisma migrate reset --force", "db:push": "prisma db push"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.8.2", "antd": "^5.25.1", "axios": "^1.9.0", "bcrypt": "^5.1.1", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "html2canvas": "^1.4.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "mathjs": "^14.5.0", "mysql2": "^3.14.1", "next": "^13.5.11", "next-auth": "^4.24.7", "pinyin-pro": "^3.26.0", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.10", "@types/jspdf": "^1.3.3", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^9", "eslint-config-next": "^15.1.8", "prisma": "^6.8.2", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}}