import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { queryBoxSchema, QueryBoxParams } from '@/lib/validations/admin/box';
import { withValidation } from '@/lib/middleware/errorHandler';
import { paginatedResponse } from '@/lib/utils/apiResponse';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { Prisma } from '@prisma/client';

const handler = withValidation<QueryBoxParams>(
  queryBoxSchema,
  async (request: AuthenticatedRequest, validatedQuery: QueryBoxParams) => {
    const data = validatedQuery;
    const skip = (data.page - 1) * data.pageSize;

    // 构建查询条件
    const where: Prisma.BoxWhereInput = {
      isDel: false
    };

    if (data.keyword) {
      where.name = {
        contains: data.keyword,
      };
    }

    if (data.status !== undefined) {
      where.status = data.status;
    }

    // 如果有时间范围，构建时间查询条件
    if (data.startTime || data.endTime) {
      where.createdAt = {};

      if (data.startTime) {
        where.createdAt.gte = new Date(data.startTime);
      }

      if (data.endTime) {
        where.createdAt.lte = new Date(data.endTime);
      }
    }

    // 查询总数和列表数据
    const [total, list] = await Promise.all([
      prisma.box.count({ where }),
      prisma.box.findMany({
        where,
        select: {
          id: true,
          name: true,
          status: true,
          description: true,
          processingFee: true,
          processingBasePrice: true,
          createdAt: true,
          updatedAt: true,
          images: {
            take: 1,
            select: {
              id: true
            },
            orderBy: {
              sortOrder: 'asc'
            }
          },
          _count: {
            select: {
              attributes: true,
              parts: true,
              images: true,
            }
          }
        },
        skip,
        take: data.pageSize,
        orderBy: { createdAt: 'desc' },
      })
    ]);

    return paginatedResponse(
      list,
      {
        page: data.page,
        pageSize: data.pageSize,
        total,
      },
      '获取盒型列表成功'
    );
  }
);

export const POST = withInternalAuth(handler);