import { z } from 'zod';
import { BoxStatus } from '@/types/box';
import { validateAttributeNames, validateFormulaAttributes } from '@/lib/utils/formula';


// 盒型属性基础验证规则
export const boxAttributeSchema = z.object({
  id: z.number().int().positive('ID必须是正整数').optional(),
  name: z.string()
    .min(1, '属性名称不能为空')
    .max(255, '属性名称长度不能超过255'),
  code: z.string()
    .min(1, '属性代码不能为空')
    .max(255, '属性代码长度不能超过255'),
  value: z.number().optional().nullable(),
  sortOrder: z.number().int().min(0).optional(),
});

// 盒型属性数组验证规则
export const boxAttributesSchema = z.array(boxAttributeSchema).refine(
  (attributes) => {
    // 验证属性名称不重复
    const { isValid: nameValid, duplicateNames } = validateAttributeNames(attributes);
    if (!nameValid) {
      throw new Error(`属性名称重复: ${duplicateNames.join(', ')}`);
    }

    // 验证属性代码不重复
    const codeCount = new Map<string, number>();
    const duplicateCodes = new Set<string>();

    attributes.forEach(attr => {
      const count = codeCount.get(attr.code) || 0;
      if (count > 0) {
        duplicateCodes.add(attr.code);
      }
      codeCount.set(attr.code, count + 1);
    });

    if (duplicateCodes.size > 0) {
      throw new Error(`属性代码重复: ${Array.from(duplicateCodes).join(', ')}`);
    }

    return true;
  },
  { message: '属性名称或代码不能重复' }
);

// 盒型公式基础验证规则
export const boxFormulaSchema = z.object({
  id: z.number().int().positive('ID必须是正整数').optional(),
  name: z.string()
    .min(1, '公式名称不能为空')
    .max(255, '公式名称长度不能超过255'),
  expression: z.string()
    .max(1024, '公式内容长度不能超过1024')
    .optional(),
});

// 盒型部件基础验证规则
export const boxPartSchema = z.object({
  id: z.number().int().positive('ID必须是正整数').optional(),
  name: z.string()
    .min(1, '部件名称不能为空')
    .max(255, '部件名称长度不能超过255'),
  formulas: z.array(boxFormulaSchema).optional(),
});

// 盒型图片基础验证规则
export const boxImageSchema = z.object({
  id: z.number().int().positive('ID必须是正整数').optional(),
  name: z.string()
    .min(1, '图片名称不能为空')
    .max(255, '图片名称长度不能超过255'),
  imageData: z.string()
    .min(1, '图片数据不能为空')
    .base64('图片数据必须是有效的base64字符串'),
  mimeType: z.string()
    .min(1, '图片类型不能为空')
    .max(50, '图片类型长度不能超过50')
    .default('image/jpeg'),
  sortOrder: z.number().int().min(0).optional(),
});

// 创建打包信息验证规则的工厂函数
const createBoxPackagingSchema = (attributes: { name: string, code: string }[] = []) => z.object({
  lengthFormula: z.string()
    .min(1, '长度计算公式不能为空')
    .max(1024, '长度计算公式长度不能超过1024')
    .superRefine((formula, ctx) => {
      const validation = validateFormulaAttributes(formula, attributes);
      if (!validation.isValid) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `长度计算公式中包含未定义的属性: ${validation.missingAttributes.join(', ')}`,
        });
      }
    }),
  widthFormula: z.string()
    .min(1, '宽度计算公式不能为空')
    .max(1024, '宽度计算公式长度不能超过1024')
    .superRefine((formula, ctx) => {
      const validation = validateFormulaAttributes(formula, attributes);
      if (!validation.isValid) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `宽度计算公式中包含未定义的属性: ${validation.missingAttributes.join(', ')}`,
        });
      }
    }),
  heightFormula: z.string()
    .max(1024, '高度计算公式长度不能超过1024')
    .optional()
    .superRefine((formula, ctx) => {
      if (formula) {
        const validation = validateFormulaAttributes(formula, attributes);
        if (!validation.isValid) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: `高度计算公式中包含未定义的属性: ${validation.missingAttributes.join(', ')}`,
          });
        }
      }
    }),
});

// 导出打包信息验证规则（用于单独验证）
export const boxPackagingSchema = createBoxPackagingSchema();

// 盒型基础字段验证规则
const boxBaseFields = {
  name: z.string()
    .min(1, '盒型名称不能为空')
    .max(255, '盒型名称长度不能超过255'),
  status: z.number()
    .int()
    .min(0)
    .max(1)
    .default(BoxStatus.DRAFT),
  description: z.string()
    .max(1000, '描述长度不能超过1000')
    .optional()
    .nullable(),
  processingFee: z.number()
    .min(0, '加工费不能小于0')
    .default(0),
  processingBasePrice: z.number()
    .min(0, '加工费起步价不能小于0')
    .default(0),
  attributes: boxAttributesSchema.optional(),
  parts: z.array(boxPartSchema).optional(),
  packaging: z.lazy(() => z.any()).optional(),
  images: z.array(boxImageSchema).optional(),
} as const;

// 定义允许的公式名称
const ALLOWED_HARDCOVER_BOX_FORMULAS = ['面纸长度', '面纸宽度', '灰板纸长度', '灰板纸宽度'];

// 盒型基础验证规则
export const boxBaseSchema = z.object(boxBaseFields).superRefine((data, ctx) => {
  // 验证公式名称是否为允许的默认公式
  if (data.parts) {
    for (const part of data.parts) {
      if (part.formulas) {
        const allowedFormulas = ALLOWED_HARDCOVER_BOX_FORMULAS;

        for (const formula of part.formulas) {
          if (!allowedFormulas.includes(formula.name)) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: `部件 "${part.name}" 包含不允许的公式 "${formula.name}"，只允许使用默认公式: ${allowedFormulas.join(', ')}`,
              path: ['parts'],
            });
          }
        }

        // 公式绑定验证
        const formulas = part.formulas;

        // 检查面纸长度和宽度的绑定
        const faceLength = formulas.find(f => f.name === '面纸长度');
        const faceWidth = formulas.find(f => f.name === '面纸宽度');

        if (faceLength && faceWidth) {
          const faceLengthHasValue = faceLength.expression && faceLength.expression.trim();
          const faceWidthHasValue = faceWidth.expression && faceWidth.expression.trim();

          if ((faceLengthHasValue && !faceWidthHasValue) || (!faceLengthHasValue && faceWidthHasValue)) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: `部件 "${part.name}" 的面纸长度和宽度必须同时填写或同时为空`,
              path: ['parts'],
            });
          }
        }

        // 检查灰板纸长度和宽度的绑定
        const greyLength = formulas.find(f => f.name === '灰板纸长度');
        const greyWidth = formulas.find(f => f.name === '灰板纸宽度');

        if (greyLength && greyWidth) {
          const greyLengthHasValue = greyLength.expression && greyLength.expression.trim();
          const greyWidthHasValue = greyWidth.expression && greyWidth.expression.trim();

          if ((greyLengthHasValue && !greyWidthHasValue) || (!greyLengthHasValue && greyWidthHasValue)) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: `部件 "${part.name}" 的灰板纸长度和宽度必须同时填写或同时为空`,
              path: ['parts'],
            });
          }
        }
      }
    }
  }

  // 如果没有属性，则不需要验证属性引用
  if (!data.attributes || data.attributes.length === 0) {
    return;
  }

  // 验证部件公式中的属性引用
  if (data.parts) {
    for (const part of data.parts) {
      if (part.formulas) {
        for (const formula of part.formulas) {
          if (formula.expression) {
            const validation = validateFormulaAttributes(formula.expression, data.attributes);
            if (!validation.isValid) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: `部件 "${part.name}" 的公式 "${formula.name}" 中包含未定义的属性: ${validation.missingAttributes.join(', ')}`,
                path: ['parts'],
              });
            }
          }
        }
      }
    }
  }

  // 验证打包信息
  if (data.packaging) {
    const packagingSchema = createBoxPackagingSchema(data.attributes);
    const result = packagingSchema.safeParse(data.packaging);
    if (!result.success) {
      result.error.issues.forEach((issue) => {
        ctx.addIssue(issue);
      });
    }
  }
});

// 创建盒型的验证schema
export const createBoxSchema = boxBaseSchema;

// 更新盒型的验证schema
export const updateBoxSchema = z.object({
  id: z.number().int().positive('ID必须是正整数'),
  ...boxBaseFields,
}).superRefine((data, ctx) => {
  // 验证公式名称是否为允许的默认公式
  if (data.parts) {
    for (const part of data.parts) {
      if (part.formulas) {
        for (const formula of part.formulas) {
          if (!ALLOWED_HARDCOVER_BOX_FORMULAS.includes(formula.name)) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: `部件 "${part.name}" 包含不允许的公式 "${formula.name}"，只允许使用默认公式: ${ALLOWED_HARDCOVER_BOX_FORMULAS.join(', ')}`,
              path: ['parts'],
            });
          }
        }

        // 公式绑定验证
        const formulas = part.formulas;

        // 检查面纸长度和宽度的绑定
        const faceLength = formulas.find(f => f.name === '面纸长度');
        const faceWidth = formulas.find(f => f.name === '面纸宽度');

        if (faceLength && faceWidth) {
          const faceLengthHasValue = faceLength.expression && faceLength.expression.trim();
          const faceWidthHasValue = faceWidth.expression && faceWidth.expression.trim();

          if ((faceLengthHasValue && !faceWidthHasValue) || (!faceLengthHasValue && faceWidthHasValue)) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: `部件 "${part.name}" 的面纸长度和宽度必须同时填写或同时为空`,
              path: ['parts'],
            });
          }
        }

        // 检查灰板纸长度和宽度的绑定
        const greyLength = formulas.find(f => f.name === '灰板纸长度');
        const greyWidth = formulas.find(f => f.name === '灰板纸宽度');

        if (greyLength && greyWidth) {
          const greyLengthHasValue = greyLength.expression && greyLength.expression.trim();
          const greyWidthHasValue = greyWidth.expression && greyWidth.expression.trim();

          if ((greyLengthHasValue && !greyWidthHasValue) || (!greyLengthHasValue && greyWidthHasValue)) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: `部件 "${part.name}" 的灰板纸长度和宽度必须同时填写或同时为空`,
              path: ['parts'],
            });
          }
        }
      }
    }
  }

  // 如果没有属性，则不需要验证属性引用
  if (!data.attributes || data.attributes.length === 0) {
    return;
  }

  // 验证部件公式中的属性引用
  if (data.parts) {
    for (const part of data.parts) {
      if (part.formulas) {
        for (const formula of part.formulas) {
          if (formula.expression) {
            const validation = validateFormulaAttributes(formula.expression, data.attributes);
            if (!validation.isValid) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: `部件 "${part.name}" 的公式 "${formula.name}" 中包含未定义的属性: ${validation.missingAttributes.join(', ')}`,
                path: ['parts'],
              });
            }
          }
        }
      }
    }
  }

  // 验证打包信息
  if (data.packaging) {
    const packagingSchema = createBoxPackagingSchema(data.attributes);
    const result = packagingSchema.safeParse(data.packaging);
    if (!result.success) {
      result.error.issues.forEach((issue) => {
        ctx.addIssue(issue);
      });
    }
  }
});

// 删除盒型的验证schema
export const deleteBoxSchema = z.object({
  id: z.number().int().positive('ID必须是正整数'),
});

// 查询盒型列表的验证schema
export const queryBoxSchema = z.object({
  page: z.number().int().min(1).default(1),
  pageSize: z.number().int().min(1).max(100).default(10),
  name: z.string().optional(),
  keyword: z.string().optional(),
  status: z.number().int().min(0).max(1).optional(),
  startTime: z.string().optional(),
  endTime: z.string().optional(),
});

// 获取盒型详情的验证schema
export const getBoxDetailSchema = z.object({
  id: z.number().int().positive('ID必须是正整数'),
});

// 获取盒型图片的验证schema
export const getBoxImageSchema = z.object({
  id: z.number().int().positive('ID必须是正整数'),
});

// 导出类型
export type CreateBoxParams = z.infer<typeof createBoxSchema>;
export type UpdateBoxParams = z.infer<typeof updateBoxSchema>;
export type DeleteBoxParams = z.infer<typeof deleteBoxSchema>;
export type QueryBoxParams = z.infer<typeof queryBoxSchema>;
export type GetBoxDetailParams = z.infer<typeof getBoxDetailSchema>;
export type GetBoxImageParams = z.infer<typeof getBoxImageSchema>;
