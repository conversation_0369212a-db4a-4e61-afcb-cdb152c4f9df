import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { UpdateHotStampingProcessData, updateHotStampingProcessSchema } from '@/lib/validations/admin/hotStampingProcess';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation(
  updateHotStampingProcessSchema,
  async (request: NextRequest, validatedData: UpdateHotStampingProcessData) => {
    const { id, ...data } = validatedData;

    // 检查烫金工艺是否存在
    const existingHotStampingProcess = await prisma.hotStampingProcess.findFirst({
      where: {
        id: id,
        isDel: false,
      },
    });

    assert(!!existingHotStampingProcess, ErrorCode.NOT_FOUND, '烫金工艺不存在');

    // 检查名称是否重复（排除自己）
    const duplicateHotStampingProcess = await prisma.hotStampingProcess.findFirst({
      where: {
        name: data.name,
        isDel: false,
        id: { not: id },
      },
    });

    assert(!duplicateHotStampingProcess, ErrorCode.DUPLICATE_ENTRY, '烫金工艺名称已存在');

    // 更新烫金工艺
    const updatedHotStampingProcess = await prisma.hotStampingProcess.update({
      where: { id: id },
      data: {
        name: data.name,
        salary: data.salary,
        salaryUnit: data.salaryUnit,
        materialPrice: data.materialPrice,
        materialUnit: data.materialUnit,
        basePrice: data.basePrice,
        remark: data.remark || null,
      },
    });

    return successResponse(
      updatedHotStampingProcess,
      '更新烫金工艺成功'
    );
  }
); 