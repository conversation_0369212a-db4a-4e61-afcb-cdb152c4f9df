import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createEmbossingProcessSchema } from '@/lib/validations/admin/embossingProcess';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation(
  createEmbossingProcessSchema,
  async (request: NextRequest, validatedData: any) => {
    const data = validatedData;

    // 检查名称是否重复
    const existingEmbossingProcess = await prisma.embossingProcess.findFirst({
      where: {
        name: data.name,
        isDel: false,
      },
    });

    assert(!existingEmbossingProcess, ErrorCode.DUPLICATE_ENTRY, '凹凸工艺名称已存在');

    // 创建凹凸工艺
    const embossingProcess = await prisma.embossingProcess.create({
      data: {
        name: data.name,
        price: data.price,
        unit: data.unit,
        basePrice: data.basePrice,
        salary: data.salary,
        salaryBasePrice: data.salaryBasePrice,
        remark: data.remark || null,
        isDel: false,
      },
    });

    return successResponse(
      embossingProcess,
      '创建凹凸工艺成功'
    );
  }
); 