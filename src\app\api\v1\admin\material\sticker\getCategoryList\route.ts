import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withValidation } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';

const handler = withValidation(
  null,
  async (request: AuthenticatedRequest) => {
    // 查询所有不干胶的品类，去重并排序
    const categories = await prisma.sticker.findMany({
      where: {
        isDel: false,
        category: {
          not: '',
        },
      },
      select: {
        category: true,
      },
      distinct: ['category'],
      orderBy: {
        category: 'asc',
      },
    });

    // 提取品类名称并过滤空值
    const categoryList = categories
      .map(item => item.category)
      .filter(Boolean) as string[];

    return successResponse(categoryList, '获取不干胶品类列表成功');
  }
); 
export const POST = withInternalAuth(handler);