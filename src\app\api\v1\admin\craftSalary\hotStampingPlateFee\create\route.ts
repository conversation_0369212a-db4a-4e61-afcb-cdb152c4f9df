import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createHotStampingPlateFeeSchema } from '@/lib/validations/admin/hotStampingProcess';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

export const POST = withValidation(
  createHotStampingPlateFeeSchema,
  async (request: NextRequest, validatedData: any) => {
    const data = validatedData;

    // 检查名称是否重复
    const existingHotStampingPlateFee = await prisma.hotStampingPlateFee.findFirst({
      where: {
        name: data.name,
        isDel: false,
      },
    });

    assert(!existingHotStampingPlateFee, ErrorCode.DUPLICATE_ENTRY, '烫金版费名称已存在');

    // 创建烫金版费
    const hotStampingPlateFee = await prisma.hotStampingPlateFee.create({
      data: {
        name: data.name,
        price: data.price,
        unit: data.unit,
        basePrice: data.basePrice,
        remark: data.remark || null,
        isDel: false,
      },
    });

    return successResponse(
      hotStampingPlateFee,
      '创建烫金版费成功'
    );
  }
); 