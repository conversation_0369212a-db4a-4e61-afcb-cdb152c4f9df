import { z } from 'zod';

// 纸箱材料基础验证 schema
const boxMaterialBaseSchema = {
  code: z.string({
    required_error: '请输入编号',
    invalid_type_error: '编号必须是字符串',
  }).min(1, '编号不能为空').max(50, '编号长度不能超过50个字符'),

  facePaper: z.string({
    required_error: '请输入面纸',
    invalid_type_error: '面纸必须是字符串',
  }).min(1, '面纸不能为空').max(100, '面纸名称长度不能超过100个字符'),

  linerPaper: z.string({
    required_error: '请输入里纸',
    invalid_type_error: '里纸必须是字符串',
  }).min(1, '里纸不能为空').max(100, '里纸名称长度不能超过100个字符'),

  threeLayerBE: z.number({
    required_error: '请输入三层B/E',
    invalid_type_error: '三层B/E必须是数字',
  }).min(0, '三层B/E不能小于0').optional(),

  threeLayerAC: z.number({
    required_error: '请输入三层A/C',
    invalid_type_error: '三层A/C必须是数字',
  }).min(0, '三层A/C不能小于0').optional(),

  fiveLayerABBC: z.number({
    required_error: '请输入五层AB/BC',
    invalid_type_error: '五层AB/BC必须是数字',
  }).min(0, '五层AB/BC不能小于0').optional(),

  fiveLayerEB: z.number({
    required_error: '请输入五层EB',
    invalid_type_error: '五层EB必须是数字',
  }).min(0, '五层EB不能小于0').optional(),

  sevenLayerEBA: z.number({
    required_error: '请输入七层EBA',
    invalid_type_error: '七层EBA必须是数字',
  }).min(0, '七层EBA不能小于0').optional(),

  remark: z.string().max(1000, '备注不能超过1000个字符').optional(),
};

// 创建纸箱材料验证 schema
export const createBoxMaterialSchema = z.object({
  ...boxMaterialBaseSchema,
});

// 更新纸箱材料验证 schema
export const updateBoxMaterialSchema = z.object({
  id: z.number({
    required_error: '请提供纸箱材料ID',
    invalid_type_error: '纸箱材料ID必须是数字',
  }).positive('纸箱材料ID必须大于0'),
  ...boxMaterialBaseSchema,
});

// 删除纸箱材料验证 schema
export const deleteBoxMaterialSchema = z.object({
  id: z.number({
    required_error: '请提供纸箱材料ID',
    invalid_type_error: '纸箱材料ID必须是数字',
  }).positive('纸箱材料ID必须大于0'),
});

// 查询参数验证 schema
export const boxMaterialQuerySchema = z.object({
  page: z.number().positive('页码必须大于0').optional(),
  pageSize: z.number().positive('每页条数必须大于0').optional(),
  keyword: z.string().optional(),
  code: z.string().optional(),
});

// 类型定义
export type CreateBoxMaterialData = z.infer<typeof createBoxMaterialSchema>;
export type UpdateBoxMaterialData = z.infer<typeof updateBoxMaterialSchema>;
export type DeleteBoxMaterialData = z.infer<typeof deleteBoxMaterialSchema>;
export type BoxMaterialQueryData = z.infer<typeof boxMaterialQuerySchema>; 