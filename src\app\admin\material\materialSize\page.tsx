'use client';

import React, { useState, useEffect } from 'react';
import {
  Card, Form, InputNumber, Button, Space, message,
  Row, Col, Typography, Divider, Spin
} from 'antd';
import {
  SaveOutlined, ReloadOutlined, UndoOutlined
} from '@ant-design/icons';
import { materialSizeApi } from '@/services/adminApi';
import { MaterialSizeConfig } from '@/types/material';
import { useAsyncError } from '@/lib/hooks/useErrorHandler';

const { Title, Text } = Typography;

// 材料尺寸配置管理页面
export default function MaterialSizeManagementPage() {
  // 错误处理Hook
  const { execute, loading, errorState } = useAsyncError();

  // 状态管理
  const [form] = Form.useForm();
  const [config, setConfig] = useState<MaterialSizeConfig>({
    regularLength: 1092,
    regularWidth: 787,
    largeLength: 1194,
    largeWidth: 889,
    specialLength: 787,
    specialWidth: 1092,
  });
  const [originalConfig, setOriginalConfig] = useState<MaterialSizeConfig>(config);
  const [hasChanges, setHasChanges] = useState(false);

  // 初始加载数据
  useEffect(() => {
    fetchConfig();
  }, []);

  // 获取配置数据
  const fetchConfig = async () => {
    const result = await execute(async () => {
      return await materialSizeApi.getList();
    }, '获取材料尺寸配置');

    if (result?.config) {
      setConfig(result.config);
      setOriginalConfig(result.config);
      form.setFieldsValue(result.config);
      setHasChanges(false);
    }
  };

  // 处理表单值变化
  const handleValuesChange = (changedValues: any, allValues: MaterialSizeConfig) => {
    setConfig(allValues);
    
    // 检查是否有变化
    const changed = Object.keys(allValues).some(key => {
      return allValues[key as keyof MaterialSizeConfig] !== originalConfig[key as keyof MaterialSizeConfig];
    });
    setHasChanges(changed);
  };

  // 保存配置
  const handleSave = async () => {
    try {
      await form.validateFields();
      
      const result = await execute(async () => {
        return await materialSizeApi.update({ config });
      }, '保存材料尺寸配置');

      if (result) {
        setOriginalConfig(config);
        setHasChanges(false);
        message.success('材料尺寸配置保存成功');
      }
    } catch (error) {
      console.error('保存失败:', error);
    }
  };

  // 重置为默认值
  const handleReset = async () => {
    const result = await execute(async () => {
      return await materialSizeApi.reset();
    }, '重置材料尺寸配置');

    if (result) {
      setConfig(result);
      setOriginalConfig(result);
      form.setFieldsValue(result);
      setHasChanges(false);
      message.success('材料尺寸已重置为默认值');
    }
  };

  // 撤销更改
  const handleUndo = () => {
    setConfig(originalConfig);
    form.setFieldsValue(originalConfig);
    setHasChanges(false);
    message.info('已撤销所有更改');
  };

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>材料尺寸配置</Title>
        <Text type="secondary">
          配置印刷行业标准的纸张尺寸规格，用于计算报价时的开数计算
        </Text>
      </div>

      <Spin spinning={loading}>
        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleValuesChange}
          initialValues={config}
        >
          <Row gutter={[24, 24]}>
            {/* 正度尺寸 */}
            <Col xs={24} lg={8}>
              <Card title="正度尺寸" size="small">
                <Form.Item
                  label="正度长度 (mm)"
                  name="regularLength"
                  rules={[
                    { required: true, message: '请输入正度长度' },
                    { type: 'number', min: 100, max: 2000, message: '长度范围为100-2000mm' }
                  ]}
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    placeholder="请输入正度长度"
                    min={100}
                    max={2000}
                    precision={0}
                  />
                </Form.Item>
                <Form.Item
                  label="正度宽度 (mm)"
                  name="regularWidth"
                  rules={[
                    { required: true, message: '请输入正度宽度' },
                    { type: 'number', min: 100, max: 2000, message: '宽度范围为100-2000mm' }
                  ]}
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    placeholder="请输入正度宽度"
                    min={100}
                    max={2000}
                    precision={0}
                  />
                </Form.Item>
              </Card>
            </Col>

            {/* 大度尺寸 */}
            <Col xs={24} lg={8}>
              <Card title="大度尺寸" size="small">
                <Form.Item
                  label="大度长度 (mm)"
                  name="largeLength"
                  rules={[
                    { required: true, message: '请输入大度长度' },
                    { type: 'number', min: 100, max: 2000, message: '长度范围为100-2000mm' }
                  ]}
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    placeholder="请输入大度长度"
                    min={100}
                    max={2000}
                    precision={0}
                  />
                </Form.Item>
                <Form.Item
                  label="大度宽度 (mm)"
                  name="largeWidth"
                  rules={[
                    { required: true, message: '请输入大度宽度' },
                    { type: 'number', min: 100, max: 2000, message: '宽度范围为100-2000mm' }
                  ]}
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    placeholder="请输入大度宽度"
                    min={100}
                    max={2000}
                    precision={0}
                  />
                </Form.Item>
              </Card>
            </Col>

            {/* 特规尺寸 */}
            <Col xs={24} lg={8}>
              <Card title="特规尺寸" size="small">
                <Form.Item
                  label="特规长度 (mm)"
                  name="specialLength"
                  rules={[
                    { required: true, message: '请输入特规长度' },
                    { type: 'number', min: 100, max: 2000, message: '长度范围为100-2000mm' }
                  ]}
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    placeholder="请输入特规长度"
                    min={100}
                    max={2000}
                    precision={0}
                  />
                </Form.Item>
                <Form.Item
                  label="特规宽度 (mm)"
                  name="specialWidth"
                  rules={[
                    { required: true, message: '请输入特规宽度' },
                    { type: 'number', min: 100, max: 2000, message: '宽度范围为100-2000mm' }
                  ]}
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    placeholder="请输入特规宽度"
                    min={100}
                    max={2000}
                    precision={0}
                  />
                </Form.Item>
              </Card>
            </Col>
          </Row>

          <Divider />

          {/* 操作按钮 */}
          <div style={{ textAlign: 'center' }}>
            <Space size="middle">
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSave}
                disabled={!hasChanges}
                loading={loading}
              >
                保存配置
              </Button>
              <Button
                icon={<UndoOutlined />}
                onClick={handleUndo}
                disabled={!hasChanges}
              >
                撤销更改
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleReset}
                loading={loading}
              >
                重置默认
              </Button>
            </Space>
          </div>
        </Form>
      </Spin>

      {/* 说明信息 */}
      <Card style={{ marginTop: '24px' }} size="small">
        <Title level={4}>说明</Title>
        <ul>
          <li><strong>正度尺寸</strong>：标准正度纸张规格，默认为 1092×787mm</li>
          <li><strong>大度尺寸</strong>：标准大度纸张规格，默认为 1194×889mm</li>
          <li><strong>特规尺寸</strong>：特殊规格纸张尺寸，默认为 787×1092mm</li>
          <li>所有尺寸单位为毫米(mm)，取值范围为 100-2000mm</li>
          <li>这些尺寸用于计算印刷品报价时的开数计算</li>
        </ul>
      </Card>
    </div>
  );
} 