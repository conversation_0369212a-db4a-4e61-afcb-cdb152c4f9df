import jwt from 'jsonwebtoken';
import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';
import { JWTPayload, UserRole } from '@/types/user';

// JWT配置
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';
const JWT_EXPIRES_IN = (Number(process.env.JWT_EXPIRES_IN) || 7 * 24) * 3600; // 默认7天
const COOKIE_NAME = process.env.JWT_COOKIE_NAME || 'auth-token';

// JWT Token生成
export function generateToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: JWT_EXPIRES_IN,
  });
}

// JWT Token验证
export function verifyToken(token: string): JWTPayload | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload;
    return decoded;
  } catch (error) {
    console.error('JWT验证失败:', error);
    return null;
  }
}

// 从请求中获取Token
export function getTokenFromRequest(request: NextRequest): string | null {
  // 优先从Authorization header获取
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // 从cookie获取
  const cookieToken = request.cookies.get(COOKIE_NAME)?.value;
  if (cookieToken) {
    return cookieToken;
  }

  return null;
}

// 从服务端cookies获取Token
export async function getTokenFromCookies(): Promise<string | null> {
  try {
    const cookieStore = cookies();
    const token = cookieStore.get(COOKIE_NAME)?.value;
    return token || null;
  } catch (error) {
    console.error('获取cookie token失败:', error);
    return null;
  }
}

// 验证请求中的Token
export function verifyRequestToken(request: NextRequest): JWTPayload | null {
  const token = getTokenFromRequest(request);
  if (!token) {
    return null;
  }
  return verifyToken(token);
}

// 设置Token到Cookie
export function setTokenCookie(token: string): string {
  const maxAge = 7 * 24 * 60 * 60; // 7天（秒）
  return `${COOKIE_NAME}=${token}; HttpOnly; Secure; SameSite=Strict; Max-Age=${maxAge}; Path=/`;
}

// 清除Token Cookie
export function clearTokenCookie(): string {
  return `${COOKIE_NAME}=; HttpOnly; Secure; SameSite=Strict; Max-Age=0; Path=/`;
}

// 检查Token是否即将过期（剩余时间少于1小时）
export function isTokenExpiringSoon(payload: JWTPayload): boolean {
  if (!payload.exp) return false;
  
  const now = Math.floor(Date.now() / 1000);
  const timeLeft = payload.exp - now;
  
  // 剩余时间少于1小时
  return timeLeft < 60 * 60;
}

// 刷新Token（如果即将过期）
export function refreshTokenIfNeeded(payload: JWTPayload): string | null {
  if (isTokenExpiringSoon(payload)) {
    // 生成新Token
    const newPayload: Omit<JWTPayload, 'iat' | 'exp'> = {
      userId: payload.userId,
      role: payload.role,
      phone: payload.phone,
    };
    return generateToken(newPayload);
  }
  return null;
}

// 权限检查函数
export function hasPermission(userRole: UserRole, requiredRoles: UserRole[]): boolean {
  return requiredRoles.includes(userRole);
}

// 管理员权限检查
export function isAdmin(userRole: UserRole): boolean {
  return userRole === UserRole.ADMIN;
}

// 内部用户权限检查（包括管理员和内部用户）
export function isInternalUser(userRole: UserRole): boolean {
  return [UserRole.ADMIN, UserRole.INTERNAL_USER].includes(userRole);
}

// 超级用户权限检查
export function isSuperUser(userRole: UserRole): boolean {
  return userRole === UserRole.SUPER_USER;
}

// 检查用户是否有访问管理后台的权限
export function canAccessAdmin(userRole: UserRole): boolean {
  return [UserRole.ADMIN, UserRole.INTERNAL_USER].includes(userRole);
}

// 获取客户端IP地址
export function getClientIP(request: NextRequest): string {
  // 尝试从各种header获取真实IP
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const clientIP = request.headers.get('x-client-ip');
  
  if (forwarded) {
    // x-forwarded-for可能包含多个IP，取第一个
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  if (clientIP) {
    return clientIP;
  }
  
  // 最后尝试从连接信息获取
  return request.ip || 'unknown';
}

// Token常量导出
export const AUTH_CONFIG = {
  COOKIE_NAME,
  JWT_SECRET,
  JWT_EXPIRES_IN,
} as const;
