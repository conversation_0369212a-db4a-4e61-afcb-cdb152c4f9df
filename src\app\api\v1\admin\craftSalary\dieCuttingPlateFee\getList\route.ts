import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { dieCuttingPlateFeeListParamsSchema } from '@/lib/validations/admin/dieCuttingPlateFee';
import { withValidation } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';

const handler = withValidation(
  dieCuttingPlateFeeListParamsSchema,
  async (request: AuthenticatedRequest, validatedQuery: any) => {
    const {
      page = 1,
      pageSize = 10,
      search,
      unit,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = validatedQuery;

    // 构建查询条件
    const where: any = {
      isDel: false,
    };

    // 搜索条件
    if (search) {
      where.OR = [
        { name: { contains: search } },
        { remark: { contains: search } },
      ];
    }

    // 单位筛选
    if (unit) {
      where.unit = unit;
    }

    // 查询总数
    const total = await prisma.dieCuttingPlateFee.count({ where });

    // 查询数据
    const list = await prisma.dieCuttingPlateFee.findMany({
      where,
      orderBy: {
        [sortBy]: sortOrder,
      },
      skip: (page - 1) * pageSize,
      take: pageSize,
    });

    const result = {
      list,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize),
      },
    };

    return successResponse(result, '获取刀版费列表成功');
  }
);

export const POST = withInternalAuth(handler);